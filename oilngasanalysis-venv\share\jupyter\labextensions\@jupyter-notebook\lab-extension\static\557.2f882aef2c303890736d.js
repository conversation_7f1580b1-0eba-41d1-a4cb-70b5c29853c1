"use strict";(self.webpackChunk_jupyter_notebook_lab_extension=self.webpackChunk_jupyter_notebook_lab_extension||[]).push([[557],{30:(e,t,i)=>{i.r(t),i.d(t,{INotebookPathOpener:()=>C,INotebookShell:()=>k,NotebookApp:()=>b,NotebookShell:()=>y,PanelHandler:()=>w,Private:()=>P,SidePanelHandler:()=>f,SidePanelPalette:()=>W,defaultNotebookPathOpener:()=>H});var n,s=i(342),a=i(27),r=i(950),d=i(448),o=i(82),l=i(797),h=i(916),p=i(697),g=i(930),u=i(901),c=i(882),_=i(399),m=i(633);class w{constructor(){this._panelChildHook=(e,t)=>{if("child-removed"===t.type){const e=t.child;p.ArrayExt.removeFirstWhere(this._items,(t=>t.widget===e))}return!0},this._items=new Array,this._panel=new c.Panel,m.MessageLoop.installMessageHook(this._panel,this._panelChildHook)}get panel(){return this._panel}addWidget(e,t){e.parent=null;const i={widget:e,rank:t},s=p.ArrayExt.upperBound(this._items,i,n.itemCmp);p.ArrayExt.insert(this._items,s,i),this._panel.insertWidget(s,e)}}class f extends w{constructor(e){super(),this._isHiddenByUser=!1,this._widgetAdded=new u.Signal(this),this._widgetRemoved=new u.Signal(this),this._area=e,this._panel.hide(),this._currentWidget=null,this._lastCurrentWidget=null,this._widgetPanel=new c.StackedPanel,this._widgetPanel.widgetRemoved.connect(this._onWidgetRemoved,this),this._closeButton=document.createElement("button"),_.closeIcon.element({container:this._closeButton,height:"16px",width:"auto"}),this._closeButton.onclick=()=>{this.collapse(),this.hide()},this._closeButton.className="jp-Button jp-SidePanel-collapse",this._closeButton.title="Collapse side panel";const t=new c.Widget({node:this._closeButton});this._panel.addWidget(t),this._panel.addWidget(this._widgetPanel)}get currentWidget(){return this._currentWidget||this._lastCurrentWidget||(this._items.length>0?this._items[0].widget:null)}get area(){return this._area}get isVisible(){return this._panel.isVisible}get panel(){return this._panel}get widgets(){return this._items.map((e=>e.widget))}get widgetAdded(){return this._widgetAdded}get widgetRemoved(){return this._widgetRemoved}get closeButton(){return this._closeButton}expand(e){e?this._currentWidget&&this._currentWidget.id===e?(this.collapse(),this.hide()):(this.collapse(),this.hide(),this.activate(e),this.show()):this.currentWidget&&(this._currentWidget=this.currentWidget,this.activate(this._currentWidget.id),this.show())}activate(e){const t=this._findWidgetByID(e);t&&(this._currentWidget=t,t.show(),t.activate())}has(e){return null!==this._findWidgetByID(e)}collapse(){var e;null===(e=this._currentWidget)||void 0===e||e.hide(),this._currentWidget=null}addWidget(e,t){e.parent=null,e.hide();const i={widget:e,rank:t},n=this._findInsertIndex(i);p.ArrayExt.insert(this._items,n,i),this._widgetPanel.insertWidget(n,e),this._refreshVisibility(),this._widgetAdded.emit(e)}hide(){this._isHiddenByUser=!0,this._refreshVisibility()}show(){this._isHiddenByUser=!1,this._refreshVisibility()}_findInsertIndex(e){return p.ArrayExt.upperBound(this._items,e,n.itemCmp)}_findWidgetIndex(e){return p.ArrayExt.findFirstIndex(this._items,(t=>t.widget===e))}_findWidgetByID(e){const t=(0,p.find)(this._items,(t=>t.widget.id===e));return t?t.widget:null}_refreshVisibility(){this._panel.setHidden(this._isHiddenByUser)}_onWidgetRemoved(e,t){t===this._lastCurrentWidget&&(this._lastCurrentWidget=null),p.ArrayExt.removeAt(this._items,this._findWidgetIndex(t)),this._refreshVisibility(),this._widgetRemoved.emit(t)}}class W{constructor(e){this._items=[],this._commandPalette=e.commandPalette,this._command=e.command}getItem(e,t){const i=this._items;for(let n=0;n<i.length;n++){const s=i[n];if(s.widgetId===e.id&&s.area===t)return s}return null}addItem(e,t){if(this.getItem(e,t))return;const i=this._commandPalette.addItem({command:this._command,category:"View",args:{side:t,title:`Show ${e.title.caption}`,id:e.id}});this._items.push({widgetId:e.id,area:t,disposable:i})}removeItem(e,t){const i=this.getItem(e,t);i&&i.disposable.dispose()}}!function(e){e.itemCmp=function(e,t){return e.rank-t.rank}}(n||(n={}));const k=new g.Token("@jupyter-notebook/application:INotebookShell");class y extends c.Widget{constructor(){super(),this._translator=h.nullTranslator,this._currentChanged=new u.Signal(this),this._mainWidgetLoaded=new g.PromiseDelegate,this.id="main",this._userLayout={},this._topHandler=new w,this._menuHandler=new w,this._leftHandler=new f("left"),this._rightHandler=new f("right"),this._main=new c.Panel;const e=this._topWrapper=new c.Panel,t=this._menuWrapper=new c.Panel;this._topHandler.panel.id="top-panel",this._topHandler.panel.node.setAttribute("role","banner"),this._menuHandler.panel.id="menu-panel",this._menuHandler.panel.node.setAttribute("role","navigation"),this._main.id="main-panel",this._main.node.setAttribute("role","main"),this._spacer_top=new c.Widget,this._spacer_top.id="spacer-widget-top",this._spacer_bottom=new c.Widget,this._spacer_bottom.id="spacer-widget-bottom",e.id="top-panel-wrapper",e.addWidget(this._topHandler.panel),t.id="menu-panel-wrapper",t.addWidget(this._menuHandler.panel);const i=new c.BoxLayout,n=this._leftHandler,s=this._rightHandler;n.panel.id="jp-left-stack",n.panel.node.setAttribute("role","complementary"),s.panel.id="jp-right-stack",s.panel.node.setAttribute("role","complementary"),n.hide(),s.hide();const a=new c.BoxLayout({spacing:0,direction:"top-to-bottom"});c.BoxLayout.setStretch(this._topWrapper,0),c.BoxLayout.setStretch(this._menuWrapper,0),c.BoxLayout.setStretch(this._main,1);const r=new c.Panel({layout:a});r.addWidget(this._topWrapper),r.addWidget(this._menuWrapper),r.addWidget(this._spacer_top),r.addWidget(this._main),r.addWidget(this._spacer_bottom),r.layout=a;const d=new c.SplitPanel;d.id="jp-main-vsplit-panel",d.spacing=1,d.orientation="vertical",c.SplitPanel.setStretch(d,1);const o=new _.TabPanelSvg({tabsMovable:!0});this._downPanel=o,this._downPanel.id="jp-down-stack";const l=new c.SplitPanel;l.id="main-split-panel",l.spacing=1,c.BoxLayout.setStretch(l,1),c.SplitPanel.setStretch(n.panel,0),c.SplitPanel.setStretch(s.panel,0),c.SplitPanel.setStretch(r,1),l.addWidget(n.panel),l.addWidget(r),l.addWidget(s.panel),l.setRelativeSizes([1,2.5,1]),d.addWidget(l),d.addWidget(o),i.spacing=0,i.addWidget(d),this._downPanel.hide(),this._downPanel.tabBar.tabMoved.connect(this._onTabPanelChanged,this),this._downPanel.stackedPanel.widgetRemoved.connect(this._onTabPanelChanged,this),this.layout=i;const p=this._skipLinkWidgetHandler=new P.SkipLinkWidgetHandler(this);this.add(p.skipLinkWidget,"top",{rank:0}),this._skipLinkWidgetHandler.show()}get currentChanged(){return this._currentChanged}get currentWidget(){var e;return null!==(e=this._main.widgets[0])&&void 0!==e?e:null}get top(){return this._topWrapper}get menu(){return this._menuWrapper}get leftHandler(){return this._leftHandler}get rightHandler(){return this._rightHandler}get leftCollapsed(){return!(this._leftHandler.isVisible&&this._leftHandler.panel.isVisible)}get rightCollapsed(){return!(this._rightHandler.isVisible&&this._rightHandler.panel.isVisible)}get restored(){return this._mainWidgetLoaded.promise}get translator(){var e;return null!==(e=this._translator)&&void 0!==e?e:h.nullTranslator}set translator(e){if(e!==this._translator){this._translator=e;const t=e.load("notebook");this._leftHandler.closeButton.title=t.__("Collapse %1 side panel",this._leftHandler.area),this._rightHandler.closeButton.title=t.__("Collapse %1 side panel",this._rightHandler.area)}}get userLayout(){return g.JSONExt.deepCopy(this._userLayout)}activateById(e){for(const t of["main","top","left","right","menu","down"]){const i=(0,p.find)(this.widgets(t),(t=>t.id===e));i&&("left"===t?this.expandLeft(e):"right"===t?this.expandRight(e):"down"===t?(this._downPanel.show(),i.activate()):i.activate())}}add(e,t,i){var n,s;let a;a=(null==i?void 0:i.type)&&this._userLayout[i.type]?this._userLayout[i.type]:this._userLayout[e.id],t=null!==(n=null==a?void 0:a.area)&&void 0!==n?n:t;const r=null!==(s=null==(i=i||(null==a?void 0:a.options)?{...i,...null==a?void 0:a.options}:void 0)?void 0:i.rank)&&void 0!==s?s:900;switch(t){case"top":return this._topHandler.addWidget(e,r);case"menu":return this._menuHandler.addWidget(e,r);case"main":case void 0:{if(this._main.widgets.length>0)return;const t=this.currentWidget;this._main.addWidget(e),this._main.update(),this._currentChanged.emit({newValue:e,oldValue:t}),this._mainWidgetLoaded.resolve();break}case"left":return this._leftHandler.addWidget(e,r);case"right":return this._rightHandler.addWidget(e,r);case"down":return this._downPanel.addWidget(e);default:console.warn(`Cannot add widget to area: ${t}`)}}collapseTop(){this._topWrapper.setHidden(!0),this._spacer_top.setHidden(!0)}expandTop(){this._topWrapper.setHidden(!1),this._spacer_top.setHidden(!1)}*widgets(e){switch(null!=e?e:"main"){case"top":return void(yield*this._topHandler.panel.widgets);case"menu":return void(yield*this._menuHandler.panel.widgets);case"main":return void(yield*this._main.widgets);case"left":return void(yield*this._leftHandler.widgets);case"right":return void(yield*this._rightHandler.widgets);case"down":return void(yield*this._downPanel.widgets);default:return void console.error(`This shell has no area called "${e}"`)}}expandLeft(e){this._leftHandler.panel.show(),this._leftHandler.expand(e)}collapseLeft(){this._leftHandler.collapse(),this._leftHandler.panel.hide()}expandRight(e){this._rightHandler.panel.show(),this._rightHandler.expand(e)}collapseRight(){this._rightHandler.collapse(),this._rightHandler.panel.hide()}async restoreLayout(e){this._userLayout=e}_onTabPanelChanged(){0===this._downPanel.stackedPanel.widgets.length&&this._downPanel.hide()}}var P,v;!function(e){e.SkipLinkWidgetHandler=class{constructor(e){this._isDisposed=!1;const t=this._skipLinkWidget=new c.Widget,i=document.createElement("a");i.href="#first-cell",i.tabIndex=1,i.text="Skip to Main",i.className="skip-link",i.addEventListener("click",this),t.addClass("jp-skiplink"),t.id="jp-skiplink",t.node.appendChild(i)}handleEvent(e){"click"===e.type&&this._focusMain()}_focusMain(){const e=document.querySelector("#main-panel .jp-InputArea-editor");e.tabIndex=1,e.focus()}get skipLinkWidget(){return this._skipLinkWidget}dispose(){this.isDisposed||(this._isDisposed=!0,this._skipLinkWidget.node.removeEventListener("click",this),this._skipLinkWidget.dispose())}hide(){this._skipLinkWidget.hide()}show(){this._skipLinkWidget.show()}get isDisposed(){return this._isDisposed}}}(P||(P={}));class b extends s.JupyterFrontEnd{constructor(e={shell:new y}){var t,i;if(super({...e,shell:null!==(t=e.shell)&&void 0!==t?t:new y}),this.name="Jupyter Notebook",this.namespace=this.name,this.status=new d.J(this),this.version=null!==(i=o.PageConfig.getOption("appVersion"))&&void 0!==i?i:"unknown",this._info=s.JupyterLab.defaultInfo,this._formatter=new l.Throttler((()=>{v.setFormat(this)}),250),this.docRegistry.addModelFactory(new a.Base64ModelFactory),e.mimeExtensions)for(const t of(0,r.as)(e.mimeExtensions))this.registerPlugin(t);const n=Object.keys(s.JupyterLab.defaultInfo).reduce(((t,i)=>(i in e&&(t[i]=JSON.parse(JSON.stringify(e[i]))),t)),{});this._info={...s.JupyterLab.defaultInfo,...n},this.restored=this.shell.restored,this.restored.then((()=>this._formatter.invoke()))}get info(){return this._info}get paths(){return{urls:{base:o.PageConfig.getOption("baseUrl"),notFound:o.PageConfig.getOption("notFoundUrl"),app:o.PageConfig.getOption("appUrl"),static:o.PageConfig.getOption("staticUrl"),settings:o.PageConfig.getOption("settingsUrl"),themes:o.PageConfig.getOption("themesUrl"),doc:o.PageConfig.getOption("docUrl"),translations:o.PageConfig.getOption("translationsApiUrl"),hubHost:o.PageConfig.getOption("hubHost")||void 0,hubPrefix:o.PageConfig.getOption("hubPrefix")||void 0,hubUser:o.PageConfig.getOption("hubUser")||void 0,hubServerName:o.PageConfig.getOption("hubServerName")||void 0},directories:{appSettings:o.PageConfig.getOption("appSettingsDir"),schemas:o.PageConfig.getOption("schemasDir"),static:o.PageConfig.getOption("staticDir"),templates:o.PageConfig.getOption("templatesDir"),themes:o.PageConfig.getOption("themesDir"),userSettings:o.PageConfig.getOption("userSettingsDir"),serverRoot:o.PageConfig.getOption("serverRoot"),workspaces:o.PageConfig.getOption("workspacesDir")}}}handleEvent(e){super.handleEvent(e),"resize"===e.type&&this._formatter.invoke()}registerPluginModule(e){let t=e.default;Object.prototype.hasOwnProperty.call(e,"__esModule")||(t=e),Array.isArray(t)||(t=[t]),t.forEach((e=>{try{this.registerPlugin(e)}catch(e){console.error(e)}}))}registerPluginModules(e){e.forEach((e=>{this.registerPluginModule(e)}))}}!function(e){e.setFormat=function(e){e.format=window.matchMedia("only screen and (max-width: 760px)").matches?"mobile":"desktop"}}(v||(v={}));const H=new class{open(e){const{prefix:t,path:i,searchParams:n,target:s,features:a}=e,r=new URL(o.URLExt.join(t,null!=i?i:""),window.location.origin);return n&&(r.search=n.toString()),window.open(r,s,a)}},C=new g.Token("@jupyter-notebook/application:INotebookPathOpener")}}]);