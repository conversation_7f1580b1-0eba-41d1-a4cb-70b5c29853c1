import numpy as np
import matplotlib.pyplot as plt

def ricker_wavelet(points, a):
    """
    Generate a Rick<PERSON> wavelet (Mexican hat wavelet)

    Parameters:
    points : int - Number of points in the wavelet
    a : float - Width parameter
    """
    t = np.linspace(-points//2, points//2, points)
    A = 2 / (np.sqrt(3*a) * (np.pi**0.25))
    wsq = a**2
    vec = A * (1 - (t**2)/wsq) * np.exp(-(t**2)/(2*wsq))
    return vec

# Simulate seismic data
def generate_seismic_section(nx=200, nz=100):
    """Generate synthetic seismic section"""
    # Create layered earth model
    velocity = np.ones((nz, nx)) * 2000  # m/s
    
    # Add geological layers
    velocity[20:40, :] += 500   # Faster layer (limestone)
    velocity[60:80, :] += 1000  # Much faster layer (salt)
    
    # Add some lateral variation (fault)
    velocity[:, 100:] += 200
    
    # Convert to reflection coefficients
    rc = np.diff(velocity, axis=0) / (velocity[:-1] + velocity[1:])
    
    # Create time axis
    dt = 0.001  # 1ms sampling
    t = np.arange(0, 0.2, dt)  # 200ms total time
    
    # Ricker wavelet (seismic source)
    wavelet = ricker_wavelet(len(t), 4.0)
    
    # Convolve each trace with wavelet
    seismic = np.zeros((len(t), nx))
    for i in range(nx):
        rc_trace = np.zeros(len(t))
        rc_trace[:rc.shape[0]] = rc[:, i]
        seismic[:, i] = np.convolve(rc_trace, wavelet, mode='same')
    
    return seismic, t

# Generate and visualize seismic data
seismic_data, time_axis = generate_seismic_section()

plt.figure(figsize=(12, 6))
plt.imshow(seismic_data, aspect='auto', cmap='seismic', 
           extent=[0, 200, 200, 0])
plt.colorbar(label='Amplitude')
plt.xlabel('Distance (traces)')
plt.ylabel('Time (ms)')
plt.title('Synthetic Seismic Section - Oil & Gas Exploration')
plt.show()

print(f"Seismic data shape: {seismic_data.shape}")
print("Traditional processing would require manual interpretation")
print("Deep learning can automatically identify geological features")