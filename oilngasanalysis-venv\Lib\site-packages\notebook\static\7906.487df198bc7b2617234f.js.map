{"version": 3, "file": "7906.487df198bc7b2617234f.js?v=487df198bc7b2617234f", "mappings": ";;;;;;;;;;;;;;AAAqE;AACN;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yEAAc,EAAE,+EAAuB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iEAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/documentsearch-extension/lib/index.js"], "sourcesContent": ["import { ISearchProviderRegistry } from '@jupyterlab/documentsearch';\nimport { INotebookShell } from '@jupyter-notebook/application';\nconst SEARCHABLE_CLASS = 'jp-mod-searchable';\n/**\n * A plugin to add document search functionalities.\n */\nconst notebookShellWidgetListener = {\n    id: '@jupyter-notebook/documentsearch-extension:notebookShellWidgetListener',\n    requires: [INotebookShell, ISearchProviderRegistry],\n    autoStart: true,\n    description: 'A plugin to add document search functionalities',\n    activate: (app, notebookShell, registry) => {\n        // If a given widget is searchable, apply the searchable class.\n        // If it's not searchable, remove the class.\n        const transformWidgetSearchability = (widget) => {\n            if (!widget) {\n                return;\n            }\n            if (registry.hasProvider(widget)) {\n                widget.addClass(SEARCHABLE_CLASS);\n            }\n            else {\n                widget.removeClass(SEARCHABLE_CLASS);\n            }\n        };\n        // Update searchability of the active widget when the registry\n        // changes, in case a provider for the current widget was added\n        // or removed\n        registry.changed.connect(() => transformWidgetSearchability(notebookShell.currentWidget));\n        // Apply the searchable class only to the active widget if it is actually\n        // searchable. Remove the searchable class from a widget when it's\n        // no longer active.\n        notebookShell.currentChanged.connect((_, args) => {\n            if (notebookShell.currentWidget) {\n                transformWidgetSearchability(notebookShell.currentWidget);\n            }\n        });\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [notebookShellWidgetListener];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}