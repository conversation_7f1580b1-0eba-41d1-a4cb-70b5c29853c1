{"version": 3, "file": "7369.a065dc2ed2f56a44cb0f.js?v=a065dc2ed2f56a44cb0f", "mappings": ";;;;;;AAAa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C,wBAAwB,mBAAO,CAAC,KAAyB;AACzD;AACA;AACA,kCAAkC;AAClC;AACA;AACA,wFAAwF;AACxF;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB;AACzB;;;;;;;ACjDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACrBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,8BAA8B;AAC9B,mBAAmB,mBAAO,CAAC,KAAc;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B;AAC9B;;;;;;;AC3Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb,qBAAqB,mBAAO,CAAC,KAAuB;AACpD,qBAAqB,mBAAO,CAAC,KAAsB;AACnD,0BAA0B,mBAAO,CAAC,KAA2B;AAC7D,iBAAiB,mBAAO,CAAC,KAAkB;AAC3C,eAAe,mBAAO,CAAC,KAAsB;AAC7C,2BAA2B,mBAAO,CAAC,KAAoB;AACvD,kBAAkB,mBAAO,CAAC,KAAmB;AAC7C;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,iCAAiC;AACzE;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,mDAAmD,cAAc;AACjE;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,wCAAwC,6CAA6C,0CAA0C;AAC/H;AACA,wCAAwC,kBAAkB;AAC1D,uCAAuC,yBAAyB;AAChE,uCAAuC,yBAAyB;AAChE,uCAAuC,yBAAyB;AAChE,uCAAuC,yBAAyB;AAChE,uCAAuC,yBAAyB;AAChE,wCAAwC,0BAA0B;AAClE,wCAAwC,0BAA0B;AAClE,wCAAwC,0BAA0B;AAClE,wCAAwC,0BAA0B;AAClE,wCAAwC,0BAA0B;AAClE,sCAAsC,sBAAsB;AAC5D,uCAAuC,oBAAoB;AAC3D,uCAAuC,oBAAoB;AAC3D,uCAAuC,oBAAoB;AAC3D,uCAAuC,qBAAqB;AAC5D,uCAAuC,qBAAqB;AAC5D,uCAAuC,qBAAqB;AAC5D,uCAAuC,qBAAqB;AAC5D,uCAAuC,qBAAqB;AAC5D,0CAA0C,eAAe;AACzD,qBAAqB,yBAAyB;AAC9C,uBAAuB,kBAAkB;AACzC,wBAAwB,yBAAyB;AACjD,qBAAqB,sBAAsB;AAC3C,yBAAyB,uBAAuB;AAChD;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,aAAa;AACb;;;;;;;ACzOa;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa,GAAG,qBAAqB,GAAG,sBAAsB,GAAG,eAAe,GAAG,cAAc,GAAG,qBAAqB;AACzH,4BAA4B,mBAAO,CAAC,KAAuB;AAC3D,aAAa,mBAAO,CAAC,KAAuB;AAC5C;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB;AACA;AACA;AACA,KAAK;AACL;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,eAAe;AACf;AACA,4DAA4D;AAC5D;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA,eAAe;AACf;AACA,QAAQ;AACR;AACA,sBAAsB;AACtB;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB;AACA;AACA;AACA,KAAK;AACL;AACA,aAAa;AACb;;;;;;;ACrGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB,GAAG,aAAa,GAAG,gBAAgB;AACvD,2BAA2B,mBAAO,CAAC,KAAuB;AAC1D,mBAAmB,mBAAO,CAAC,KAAsB;AACjD,gBAAgB,mBAAO,CAAC,KAAoB;AAC5C,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,WAAW;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,YAAY;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF,UAAU;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,gFAAgF,iBAAiB;AACjG;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,eAAe;AACf,mCAAmC;AACnC;AACA;AACA;AACA,mBAAmB;AACnB,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AC3Ta;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,0BAA0B,mBAAO,CAAC,KAA6B;AAC/D,oBAAoB,mBAAO,CAAC,KAAe;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2BAA2B;AAC3B;;;;;;;AC7Ba;AACb;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,mBAAmB,mBAAO,CAAC,IAAc;AACzC,gBAAgB,mBAAO,CAAC,KAAoB;AAC5C,cAAc,mBAAO,CAAC,KAAkB;AACxC,cAAc,mBAAO,CAAC,KAAkB;AACxC,cAAc,mBAAO,CAAC,GAAkB;AACxC,cAAc,mBAAO,CAAC,KAAkB;AACxC,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD,gBAAgB,mBAAO,CAAC,KAAoB;AAC5C,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,iBAAiB,mBAAO,CAAC,KAAqB;AAC9C,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,sBAAsB,mBAAO,CAAC,KAA0B;AACxD,yBAAyB,mBAAO,CAAC,KAA6B;AAC9D,kBAAkB,mBAAO,CAAC,IAAsB;AAChD,eAAe,mBAAO,CAAC,KAAmB;AAC1C,eAAe,mBAAO,CAAC,KAAmB;AAC1C,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,qBAAqB,mBAAO,CAAC,KAAyB;AACtD,mBAAmB,mBAAO,CAAC,IAAuB;AAClD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD,qBAAqB,WAAW;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA2C;AACtE,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AC1Ca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,oBAAoB,mBAAO,CAAC,KAAmC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,iBAAiB;AACjG;AACA;AACA;AACA;AACA;AACA,6CAA6C,4BAA4B;AACzE;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;ACrFa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA2C;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,yBAAyB;AACzB;AACA;AACA;AACA,mEAAmE,wDAAwD;AAC3H;AACA;AACA,yBAAyB;AACzB;AACA;AACA,mEAAmE,mDAAmD;AACtH;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F;AAC/F;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;ACnJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,gBAAgB,mBAAO,CAAC,KAA+B;AACvD,gBAAgB,mBAAO,CAAC,IAAwC;AAChE,gBAAgB,mBAAO,CAAC,KAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;AC3Ia;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,oBAAoB,mBAAO,CAAC,KAAmC;AAC/D,4BAA4B,mBAAO,CAAC,KAAgB;AACpD,oBAAoB,mBAAO,CAAC,KAA4C;AACxE,mBAAmB,mBAAO,CAAC,KAA0B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iFAAiF,UAAU;AAC3F;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,iCAAiC;AACjC,+BAA+B;AAC/B;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,cAAc;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,qBAAqB;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC,aAAa;AACb;AACA;AACA,wCAAwC;AACxC,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E;AAC9E;AACA;AACA,2BAA2B;AAC3B;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB,0CAA0C,mCAAmC;AAC7E;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,8FAA8F,qBAAqB;AACnH,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB,wCAAwC,yBAAyB;AACjE;AACA,aAAa;AACb;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;AC5Za;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA2C;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AClCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,iBAAiB,mBAAO,CAAC,KAAgC;AACzD,iBAAiB,mBAAO,CAAC,KAAyC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,YAAY,IAAI;AAChD,sDAAsD,WAAW,gBAAgB,eAAe;AAChG,+CAA+C,kBAAkB,IAAI;AACrE,iDAAiD,oBAAoB,IAAI;AACzE,gCAAgC,6BAA6B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,6BAA6B;AAC7B,6BAA6B;AAC7B,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,oCAAoC;AACpC,0CAA0C;AAC1C;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,wBAAwB,IAAI,YAAY;AACxE,sDAAsD,WAAW,cAAc,eAAe;AAC9F,+CAA+C,kBAAkB,IAAI;AACrE,iDAAiD,oBAAoB,IAAI;AACzE;AACA,wBAAwB;AACxB,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AClNa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,kBAAkB,mBAAO,CAAC,KAAiC;AAC3D,kBAAkB,mBAAO,CAAC,KAA0C;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,+CAA+C;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;ACtDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,cAAc,mBAAO,CAAC,KAA6B;AACnD,cAAc,mBAAO,CAAC,KAAsC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;;;;;;;AC9Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B,mBAAmB,mBAAO,CAAC,KAAc;AACzC,yBAAyB,mBAAO,CAAC,KAAwC;AACzE,yBAAyB,mBAAO,CAAC,KAAiD;AAClF,kBAAkB,mBAAO,CAAC,KAAyB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,SAAS,uBAAuB,IAAI;AACpE,8BAA8B,SAAS,iCAAiC,IAAI;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,0BAA0B;AAC1B;;;;;;;ACxGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,cAAc,mBAAO,CAAC,KAA6B;AACnD,cAAc,mBAAO,CAAC,KAAsC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;;;;;;;AC9Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,cAAc,mBAAO,CAAC,KAA6B;AACnD,cAAc,mBAAO,CAAC,KAAsC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AACA,4CAA4C;AAC5C;AACA,gDAAgD,gDAAgD;AAChG;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;;;;;;;ACvLa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA2C;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,cAAc;AAClF;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;ACxGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,iBAAiB,mBAAO,CAAC,KAAY;AACrC,iBAAiB,mBAAO,CAAC,KAAgC;AACzD,iBAAiB,mBAAO,CAAC,KAAyC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;ACvDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,iBAAiB;AAC7C,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,gBAAgB,mBAAO,CAAC,KAA+B;AACvD,gBAAgB,mBAAO,CAAC,KAA+B;AACvD,gBAAgB,mBAAO,CAAC,KAAwC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB;AACzB;;;;;;;ACjFa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,cAAc,mBAAO,CAAC,KAA6B;AACnD,cAAc,mBAAO,CAAC,KAAsC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;;;;;;;AC9Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,kBAAkB,mBAAO,CAAC,KAAiC;AAC3D,kBAAkB,mBAAO,CAAC,KAA0C;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;AChDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,iBAAiB,mBAAO,CAAC,KAAgC;AACzD,iBAAiB,mBAAO,CAAC,KAAyC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA,0CAA0C,SAAS,0BAA0B;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC9Fa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB,GAAG,iBAAiB,GAAG,iBAAiB;AAC5D,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA2C;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,oEAAoE,cAAc;AAClF;AACA,wDAAwD,SAAS,4BAA4B;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AC/Fa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,kBAAkB,mBAAO,CAAC,IAAiC;AAC3D,kBAAkB,mBAAO,CAAC,KAA0C;AACpE,kBAAkB,mBAAO,CAAC,KAAyB;AACnD;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA,+DAA+D,eAAe;AAC9E;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,UAAU;AAC9E;AACA;AACA;AACA,4FAA4F,UAAU;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA,yHAAyH,UAAU;AACnI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA,gHAAgH,UAAU;AAC1H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,6FAA6F,UAAU;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,UAAU;AACxF;AACA;AACA;AACA;AACA;AACA,iHAAiH,UAAU;AAC3H;AACA;AACA;AACA;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,UAAU;AAC9E;AACA;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA,2DAA2D,SAAS,sBAAsB;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;ACjiBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,eAAe,mBAAO,CAAC,KAA8B;AACrD,eAAe,mBAAO,CAAC,KAAuC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;AC/Fa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,iBAAiB,mBAAO,CAAC,KAAgC;AACzD,iBAAiB,mBAAO,CAAC,KAAyC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC9Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB,GAAG,gBAAgB;AAC1C,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,eAAe,mBAAO,CAAC,KAA8B;AACrD,eAAe,mBAAO,CAAC,KAA8B;AACrD,eAAe,mBAAO,CAAC,KAAuC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,kBAAkB,IAAI;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACtGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB,GAAG,kBAAkB,GAAG,mBAAmB;AAClE,mBAAmB,mBAAO,CAAC,KAAc;AACzC,sBAAsB,mBAAO,CAAC,KAAqC;AACnE,sBAAsB,mBAAO,CAAC,KAAqC;AACnE,sBAAsB,mBAAO,CAAC,KAAqC;AACnE,sBAAsB,mBAAO,CAAC,KAA8C;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACvJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,sBAAsB,mBAAO,CAAC,KAAqC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,2FAA2F,cAAc;AACzG;AACA;AACA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS,2CAA2C;AAC7F;AACA,mGAAmG,UAAU;AAC7G;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;AC9Ga;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,0BAA0B,GAAG,uBAAuB,GAAG,sBAAsB;AAChG,mBAAmB,mBAAO,CAAC,IAAe;AAC1C,qBAAqB,mBAAO,CAAC,KAAoC;AACjE,qBAAqB,mBAAO,CAAC,KAA6C;AAC1E,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;AC1Fa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,2BAA2B,GAAG,4BAA4B,GAAG,qBAAqB,GAAG,oBAAoB,GAAG,iBAAiB,GAAG,wBAAwB,GAAG,gBAAgB,GAAG,kBAAkB,GAAG,mBAAmB,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,aAAa,GAAG,eAAe,GAAG,iBAAiB,GAAG,cAAc,GAAG,eAAe,GAAG,cAAc;AAChd,cAAc,MAAM,eAAe,MAAM,cAAc;AACvD,iBAAiB;AACjB,eAAe;AACf,aAAa;AACb,iBAAiB,KAAK;AACtB,iBAAiB;AACjB,gBAAgB,sBAAsB,0DAA0D;AAChG,mBAAmB,sBAAsB,yCAAyC;AAClF,kBAAkB,sBAAsB,2CAA2C;AACnF;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,0BAA0B,2FAA2F;AACrH,4BAA4B,2FAA2F;AACvH,6BAA6B,4FAA4F;AACzH,4BAA4B,4FAA4F;AACxH,8BAA8B,2GAA2G;AACzI,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA,mBAAmB;AACnB;;;;;;;ACnKa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,qBAAqB,mBAAO,CAAC,KAAyB;AACtD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD,mBAAmB,mBAAO,CAAC,IAAuB;AAClD,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,kBAAkB,mBAAO,CAAC,IAAsB;AAChD,qBAAqB,mBAAO,CAAC,KAAyB;AACtD;AACA;AACA;AACA,kCAAkC;AAClC,yCAAyC;AACzC,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,8BAA8B;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,UAAU;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,UAAU;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,iEAAiE,UAAU;AAC3E;AACA;AACA,+FAA+F,UAAU;AACzG;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,iEAAiE,UAAU;AAC3E;AACA;AACA,iGAAiG,UAAU;AAC3G;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,4CAA4C;AAC5C;AACA;AACA;AACA,uBAAuB;AACvB,0CAA0C;AAC1C;AACA;AACA;AACA,uBAAuB;AACvB,6CAA6C;AAC7C;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA,yCAAyC,kBAAkB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA,+CAA+C,eAAe;AAC9D;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,SAAS,2BAA2B;AACvF,+CAA+C,SAAS,gDAAgD;AACxG;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,cAAc;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,+CAA+C,uJAAuJ,+GAA+G;AACvW;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;AC9aa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,mBAAmB,mBAAO,CAAC,KAA4B;AACvD,mBAAmB,mBAAO,CAAC,KAA+B;AAC1D,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,2BAA2B,mBAAO,CAAC,KAAuB;AAC1D,kBAAkB,mBAAO,CAAC,IAAsB;AAChD,gBAAgB,mBAAO,CAAC,KAAoB;AAC5C,oBAAoB,mBAAO,CAAC,IAAe;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,OAAO;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E;AAC1E,2EAA2E;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,UAAU;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,+BAA+B;AAC/B,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,6CAA6C,iCAAiC,aAAa;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA,4CAA4C,gBAAgB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,eAAe;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;AC3kBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B;AAC5B,0BAA0B,mBAAO,CAAC,KAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD,4BAA4B;AAC5B;;;;;;;ACrCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B;AAC1B;;;;;;;AC3Ca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,iBAAiB;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,2BAA2B;AAC3B;;;;;;;AC/Ga;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B,GAAG,mBAAmB;AAChD,kBAAkB,mBAAO,CAAC,KAAyB;AACnD,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA,gFAAgF,KAAK;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B;AAC1B;;;;;;;ACvGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,uBAAuB;AACvB;;;;;;;AC/Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,4BAA4B,mBAAO,CAAC,KAAgB;AACpD,kBAAkB,mBAAO,CAAC,KAAyB;AACnD;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sHAAsH,UAAU;AAChI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA,uGAAuG,UAAU;AACjH;AACA;AACA;AACA;AACA,wCAAwC,QAAQ;AAChD;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA,mDAAmD,kCAAkC;AACrF;AACA;AACA,4BAA4B,cAAc;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,2BAA2B;AAC3B;;;;;;;ACzSa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,UAAU;AAC5F;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B;AAC1B;;;;;;;ACrHa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;;;;;;;ACtKa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,yBAAyB;AACzB;;;;;;;ACpFa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB;;;;;;;ACjCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gCAAgC,GAAG,mBAAmB,GAAG,kBAAkB;AAC3E,gBAAgB,mBAAO,CAAC,KAAuB;AAC/C,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,gCAAgC;AAChC;;;;;;;ACjMa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB;;;;;;;AC3Ca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,mBAAmB;AAC3C,gBAAgB,mBAAO,CAAC,KAAuB;AAC/C,kBAAkB,mBAAO,CAAC,KAAyB;AACnD,oBAAoB,mBAAO,CAAC,IAAgB;AAC5C,mBAAmB,WAAW;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,mBAAmB,mBAAmB;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB;;;;;;;AC1Ra;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B;AAC1B;;;;;;;AC/Fa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;;;;;;;AC/Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,uBAAuB;AACzD,gBAAgB,mBAAO,CAAC,KAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,gGAAgG,oBAAoB;AACpH;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,+BAA+B;AAC/B;;;;;;;ACzKa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB;;;;;;;ACzEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,yBAAyB;AACzB;;;;;;;ACrCa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB,gBAAgB,mBAAO,CAAC,KAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;;;;;;;ACxHa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B,GAAG,uBAAuB,GAAG,uBAAuB;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,0BAA0B;AAC1B;;;;;;;ACpJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB,gBAAgB,mBAAO,CAAC,KAAuB;AAC/C,kBAAkB,mBAAO,CAAC,KAAyB;AACnD,mBAAmB,mBAAO,CAAC,KAA0B;AACrD;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,sBAAsB;AAC7G;AACA,8EAA8E,gDAAgD;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,kCAAkC;AACzH,iFAAiF,kCAAkC;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB;AACtE;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,gGAAgG,oBAAoB;AACpH;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,iBAAiB;AAC7C;AACA;AACA;AACA,gCAAgC,kBAAkB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,qBAAqB;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,sDAAsD,SAAS;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,oDAAoD,8BAA8B;AAClF,qDAAqD,+BAA+B;AACpF;AACA,iDAAiD,SAAS;AAC1D;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,eAAe;AACtE;AACA;AACA;AACA;AACA;AACA,uDAAuD,eAAe;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA,2CAA2C,yBAAyB;AACpE;AACA;AACA;AACA;AACA;AACA,2CAA2C,4BAA4B;AACvE;AACA;AACA,KAAK;AACL;AACA,yBAAyB;AACzB;;;;;;;AC3lBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,sBAAsB;AACtB;;;;;;;AC/Ca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,wBAAwB;AACxB;;;;;;;AClDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B,GAAG,sBAAsB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,yDAAyD,wBAAwB;AACjF;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,4FAA4F,oBAAoB;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,oBAAoB;AACxH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,gGAAgG,oBAAoB;AACpH;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,kEAAkE,wBAAwB;AAC1F;AACA;AACA,KAAK;AACL;AACA,6BAA6B;AAC7B;;;;;;;ACjLa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B,GAAG,wBAAwB,GAAG,yBAAyB;AACpF;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,6BAA6B;AAC7B;;;;;;;AClMa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B;AAC7B,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,uBAAuB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,UAAU;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA,wDAAwD,4BAA4B;AACpF;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA,0CAA0C,gEAAgE;AAC1G;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iFAAiF,UAAU;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA,oGAAoG,oBAAoB;AACxH;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,6BAA6B;AAC7B;;;;;;;ACzXa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,4BAA4B;AAC5B;;;;;;;ACrCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,YAAY;AACZ,mBAAmB,mBAAO,CAAC,KAAc;AACzC;AACA;AACA,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,kBAAkB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,YAAY;AACZ;;;;;;;ACpFa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,2BAA2B,mBAAO,CAAC,KAAsB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,0DAA0D,UAAU;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,mBAAmB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AChHa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,UAAU;AACV,sCAAsC,gCAAgC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACvCa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,4FAA4F,qBAAqB;AACjH;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA4F,qBAAqB;AACjH;AACA,0CAA0C,0DAA0D;AACpG;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gGAAgG,sBAAsB;AACtH;AACA,4EAA4E;AAC5E;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;ACrHa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,oBAAoB;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,oBAAoB;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,iBAAiB;AACzF;AACA;AACA;AACA,2FAA2F,UAAU;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,UAAU;AAC5F;AACA;AACA;AACA,4EAA4E;AAC5E;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF,UAAU;AAC7F;AACA;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,+BAA+B;AACpF;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,cAAc;AACd;;;;;;;ACxea;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,WAAW,GAAG,WAAW;AACzB;AACA,sCAAsC,eAAe;AACrD;AACA,WAAW;AACX;AACA,sCAAsC,wBAAwB;AAC9D;AACA,WAAW;AACX", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/OutputJax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/Wrapper.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/WrapperFactory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Notation.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrapper.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/WrapperFactory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/TeXAtom.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/TextNode.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/maction.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/math.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/menclose.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mfenced.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mfrac.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mglyph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mi.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mmultiscripts.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mn.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mo.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mpadded.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mroot.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mrow.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/ms.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mspace.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/msqrt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/msubsup.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mtable.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mtd.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mtext.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/mtr.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/munderover.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/scriptbase.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Wrappers/semantics.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Notation.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/OutputJax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrapper.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/WrapperFactory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/TeXAtom.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/TextNode.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/maction.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/math.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/menclose.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mfenced.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mfrac.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mglyph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mi.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mmultiscripts.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mn.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mo.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mpadded.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mroot.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mrow.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/ms.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mspace.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/msqrt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/msubsup.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mtable.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mtd.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mtext.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/mtr.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/munderover.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/scriptbase.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/Wrappers/semantics.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/BBox.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/FunctionList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/PrioritizedList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/StyleList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Styles.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/numeric.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractOutputJax = void 0;\nvar Options_js_1 = require(\"../util/Options.js\");\nvar FunctionList_js_1 = require(\"../util/FunctionList.js\");\nvar AbstractOutputJax = (function () {\n    function AbstractOutputJax(options) {\n        if (options === void 0) { options = {}; }\n        this.adaptor = null;\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.postFilters = new FunctionList_js_1.FunctionList();\n    }\n    Object.defineProperty(AbstractOutputJax.prototype, \"name\", {\n        get: function () {\n            return this.constructor.NAME;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractOutputJax.prototype.setAdaptor = function (adaptor) {\n        this.adaptor = adaptor;\n    };\n    AbstractOutputJax.prototype.initialize = function () {\n    };\n    AbstractOutputJax.prototype.reset = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    AbstractOutputJax.prototype.getMetrics = function (_document) {\n    };\n    AbstractOutputJax.prototype.styleSheet = function (_document) {\n        return null;\n    };\n    AbstractOutputJax.prototype.pageElements = function (_document) {\n        return null;\n    };\n    AbstractOutputJax.prototype.executeFilters = function (filters, math, document, data) {\n        var args = { math: math, document: document, data: data };\n        filters.execute(args);\n        return args.data;\n    };\n    AbstractOutputJax.NAME = 'generic';\n    AbstractOutputJax.OPTIONS = {};\n    return AbstractOutputJax;\n}());\nexports.AbstractOutputJax = AbstractOutputJax;\n//# sourceMappingURL=OutputJax.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractWrapper = void 0;\nvar AbstractWrapper = (function () {\n    function AbstractWrapper(factory, node) {\n        this.factory = factory;\n        this.node = node;\n    }\n    Object.defineProperty(AbstractWrapper.prototype, \"kind\", {\n        get: function () {\n            return this.node.kind;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractWrapper.prototype.wrap = function (node) {\n        return this.factory.wrap(node);\n    };\n    return AbstractWrapper;\n}());\nexports.AbstractWrapper = AbstractWrapper;\n//# sourceMappingURL=Wrapper.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractWrapperFactory = void 0;\nvar Factory_js_1 = require(\"./Factory.js\");\nvar AbstractWrapperFactory = (function (_super) {\n    __extends(AbstractWrapperFactory, _super);\n    function AbstractWrapperFactory() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AbstractWrapperFactory.prototype.wrap = function (node) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        return this.create.apply(this, __spreadArray([node.kind, node], __read(args), false));\n    };\n    return AbstractWrapperFactory;\n}(Factory_js_1.AbstractFactory));\nexports.AbstractWrapperFactory = AbstractWrapperFactory;\n//# sourceMappingURL=WrapperFactory.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTML = void 0;\nvar OutputJax_js_1 = require(\"./common/OutputJax.js\");\nvar StyleList_js_1 = require(\"../util/StyleList.js\");\nvar WrapperFactory_js_1 = require(\"./chtml/WrapperFactory.js\");\nvar Usage_js_1 = require(\"./chtml/Usage.js\");\nvar tex_js_1 = require(\"./chtml/fonts/tex.js\");\nvar LENGTHS = __importStar(require(\"../util/lengths.js\"));\nvar string_js_1 = require(\"../util/string.js\");\nvar CHTML = (function (_super) {\n    __extends(CHTML, _super);\n    function CHTML(options) {\n        if (options === void 0) { options = null; }\n        var _this = _super.call(this, options, WrapperFactory_js_1.CHTMLWrapperFactory, tex_js_1.TeXFont) || this;\n        _this.chtmlStyles = null;\n        _this.font.adaptiveCSS(_this.options.adaptiveCSS);\n        _this.wrapperUsage = new Usage_js_1.Usage();\n        return _this;\n    }\n    CHTML.prototype.escaped = function (math, html) {\n        this.setDocument(html);\n        return this.html('span', {}, [this.text(math.math)]);\n    };\n    CHTML.prototype.styleSheet = function (html) {\n        if (this.chtmlStyles) {\n            if (this.options.adaptiveCSS) {\n                var styles = new StyleList_js_1.CssStyles();\n                this.addWrapperStyles(styles);\n                this.updateFontStyles(styles);\n                this.adaptor.insertRules(this.chtmlStyles, styles.getStyleRules());\n            }\n            return this.chtmlStyles;\n        }\n        var sheet = this.chtmlStyles = _super.prototype.styleSheet.call(this, html);\n        this.adaptor.setAttribute(sheet, 'id', CHTML.STYLESHEETID);\n        this.wrapperUsage.update();\n        return sheet;\n    };\n    CHTML.prototype.updateFontStyles = function (styles) {\n        styles.addStyles(this.font.updateStyles({}));\n    };\n    CHTML.prototype.addWrapperStyles = function (styles) {\n        var e_1, _a;\n        if (!this.options.adaptiveCSS) {\n            _super.prototype.addWrapperStyles.call(this, styles);\n            return;\n        }\n        try {\n            for (var _b = __values(this.wrapperUsage.update()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var kind = _c.value;\n                var wrapper = this.factory.getNodeClass(kind);\n                wrapper && this.addClassStyles(wrapper, styles);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    CHTML.prototype.addClassStyles = function (wrapper, styles) {\n        var _a;\n        var CLASS = wrapper;\n        if (CLASS.autoStyle && CLASS.kind !== 'unknown') {\n            styles.addStyles((_a = {},\n                _a['mjx-' + CLASS.kind] = {\n                    display: 'inline-block',\n                    'text-align': 'left'\n                },\n                _a));\n        }\n        this.wrapperUsage.add(CLASS.kind);\n        _super.prototype.addClassStyles.call(this, wrapper, styles);\n    };\n    CHTML.prototype.processMath = function (math, parent) {\n        this.factory.wrap(math).toCHTML(parent);\n    };\n    CHTML.prototype.clearCache = function () {\n        this.cssStyles.clear();\n        this.font.clearCache();\n        this.wrapperUsage.clear();\n        this.chtmlStyles = null;\n    };\n    CHTML.prototype.reset = function () {\n        this.clearCache();\n    };\n    CHTML.prototype.unknownText = function (text, variant, width) {\n        if (width === void 0) { width = null; }\n        var styles = {};\n        var scale = 100 / this.math.metrics.scale;\n        if (scale !== 100) {\n            styles['font-size'] = this.fixed(scale, 1) + '%';\n            styles.padding = LENGTHS.em(75 / scale) + ' 0 ' + LENGTHS.em(20 / scale) + ' 0';\n        }\n        if (variant !== '-explicitFont') {\n            var c = (0, string_js_1.unicodeChars)(text);\n            if (c.length !== 1 || c[0] < 0x1D400 || c[0] > 0x1D7FF) {\n                this.cssFontStyles(this.font.getCssFont(variant), styles);\n            }\n        }\n        if (width !== null) {\n            var metrics = this.math.metrics;\n            styles.width = Math.round(width * metrics.em * metrics.scale) + 'px';\n        }\n        return this.html('mjx-utext', { variant: variant, style: styles }, [this.text(text)]);\n    };\n    CHTML.prototype.measureTextNode = function (textNode) {\n        var adaptor = this.adaptor;\n        var text = adaptor.clone(textNode);\n        adaptor.setStyle(text, 'font-family', adaptor.getStyle(text, 'font-family').replace(/MJXZERO, /g, ''));\n        var style = { position: 'absolute', 'white-space': 'nowrap' };\n        var node = this.html('mjx-measure-text', { style: style }, [text]);\n        adaptor.append(adaptor.parent(this.math.start.node), this.container);\n        adaptor.append(this.container, node);\n        var w = adaptor.nodeSize(text, this.math.metrics.em)[0] / this.math.metrics.scale;\n        adaptor.remove(this.container);\n        adaptor.remove(node);\n        return { w: w, h: .75, d: .2 };\n    };\n    CHTML.NAME = 'CHTML';\n    CHTML.OPTIONS = __assign(__assign({}, OutputJax_js_1.CommonOutputJax.OPTIONS), { adaptiveCSS: true, matchFontHeight: true });\n    CHTML.commonStyles = {\n        'mjx-container[jax=\"CHTML\"]': { 'line-height': 0 },\n        'mjx-container [space=\"1\"]': { 'margin-left': '.111em' },\n        'mjx-container [space=\"2\"]': { 'margin-left': '.167em' },\n        'mjx-container [space=\"3\"]': { 'margin-left': '.222em' },\n        'mjx-container [space=\"4\"]': { 'margin-left': '.278em' },\n        'mjx-container [space=\"5\"]': { 'margin-left': '.333em' },\n        'mjx-container [rspace=\"1\"]': { 'margin-right': '.111em' },\n        'mjx-container [rspace=\"2\"]': { 'margin-right': '.167em' },\n        'mjx-container [rspace=\"3\"]': { 'margin-right': '.222em' },\n        'mjx-container [rspace=\"4\"]': { 'margin-right': '.278em' },\n        'mjx-container [rspace=\"5\"]': { 'margin-right': '.333em' },\n        'mjx-container [size=\"s\"]': { 'font-size': '70.7%' },\n        'mjx-container [size=\"ss\"]': { 'font-size': '50%' },\n        'mjx-container [size=\"Tn\"]': { 'font-size': '60%' },\n        'mjx-container [size=\"sm\"]': { 'font-size': '85%' },\n        'mjx-container [size=\"lg\"]': { 'font-size': '120%' },\n        'mjx-container [size=\"Lg\"]': { 'font-size': '144%' },\n        'mjx-container [size=\"LG\"]': { 'font-size': '173%' },\n        'mjx-container [size=\"hg\"]': { 'font-size': '207%' },\n        'mjx-container [size=\"HG\"]': { 'font-size': '249%' },\n        'mjx-container [width=\"full\"]': { width: '100%' },\n        'mjx-box': { display: 'inline-block' },\n        'mjx-block': { display: 'block' },\n        'mjx-itable': { display: 'inline-table' },\n        'mjx-row': { display: 'table-row' },\n        'mjx-row > *': { display: 'table-cell' },\n        'mjx-mtext': {\n            display: 'inline-block'\n        },\n        'mjx-mstyle': {\n            display: 'inline-block'\n        },\n        'mjx-merror': {\n            display: 'inline-block',\n            color: 'red',\n            'background-color': 'yellow'\n        },\n        'mjx-mphantom': {\n            visibility: 'hidden'\n        },\n        '_::-webkit-full-page-media, _:future, :root mjx-container': {\n            'will-change': 'opacity'\n        }\n    };\n    CHTML.STYLESHEETID = 'MJX-CHTML-styles';\n    return CHTML;\n}(OutputJax_js_1.CommonOutputJax));\nexports.CHTML = CHTML;\n//# sourceMappingURL=chtml.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Arrow = exports.DiagonalArrow = exports.DiagonalStrike = exports.Border2 = exports.Border = exports.RenderElement = void 0;\nvar Notation = __importStar(require(\"../common/Notation.js\"));\n__exportStar(require(\"../common/Notation.js\"), exports);\nvar RenderElement = function (name, offset) {\n    if (offset === void 0) { offset = ''; }\n    return (function (node, _child) {\n        var shape = node.adjustBorder(node.html('mjx-' + name));\n        if (offset) {\n            var d = node.getOffset(offset);\n            if (node.thickness !== Notation.THICKNESS || d) {\n                var transform = \"translate\".concat(offset, \"(\").concat(node.em(node.thickness / 2 - d), \")\");\n                node.adaptor.setStyle(shape, 'transform', transform);\n            }\n        }\n        node.adaptor.append(node.chtml, shape);\n    });\n};\nexports.RenderElement = RenderElement;\nvar Border = function (side) {\n    return Notation.CommonBorder(function (node, child) {\n        node.adaptor.setStyle(child, 'border-' + side, node.em(node.thickness) + ' solid');\n    })(side);\n};\nexports.Border = Border;\nvar Border2 = function (name, side1, side2) {\n    return Notation.CommonBorder2(function (node, child) {\n        var border = node.em(node.thickness) + ' solid';\n        node.adaptor.setStyle(child, 'border-' + side1, border);\n        node.adaptor.setStyle(child, 'border-' + side2, border);\n    })(name, side1, side2);\n};\nexports.Border2 = Border2;\nvar DiagonalStrike = function (name, neg) {\n    return Notation.CommonDiagonalStrike(function (cname) { return function (node, _child) {\n        var _a = node.getBBox(), w = _a.w, h = _a.h, d = _a.d;\n        var _b = __read(node.getArgMod(w, h + d), 2), a = _b[0], W = _b[1];\n        var t = neg * node.thickness / 2;\n        var strike = node.adjustBorder(node.html(cname, { style: {\n                width: node.em(W),\n                transform: 'rotate(' + node.fixed(-neg * a) + 'rad) translateY(' + t + 'em)',\n            } }));\n        node.adaptor.append(node.chtml, strike);\n    }; })(name);\n};\nexports.DiagonalStrike = DiagonalStrike;\nvar DiagonalArrow = function (name) {\n    return Notation.CommonDiagonalArrow(function (node, arrow) {\n        node.adaptor.append(node.chtml, arrow);\n    })(name);\n};\nexports.DiagonalArrow = DiagonalArrow;\nvar Arrow = function (name) {\n    return Notation.CommonArrow(function (node, arrow) {\n        node.adaptor.append(node.chtml, arrow);\n    })(name);\n};\nexports.Arrow = Arrow;\n//# sourceMappingURL=Notation.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLWrapper = exports.SPACE = exports.FONTSIZE = void 0;\nvar LENGTHS = __importStar(require(\"../../util/lengths.js\"));\nvar Wrapper_js_1 = require(\"../common/Wrapper.js\");\nvar BBox_js_1 = require(\"../../util/BBox.js\");\nexports.FONTSIZE = {\n    '70.7%': 's',\n    '70%': 's',\n    '50%': 'ss',\n    '60%': 'Tn',\n    '85%': 'sm',\n    '120%': 'lg',\n    '144%': 'Lg',\n    '173%': 'LG',\n    '207%': 'hg',\n    '249%': 'HG'\n};\nexports.SPACE = (_a = {},\n    _a[LENGTHS.em(2 / 18)] = '1',\n    _a[LENGTHS.em(3 / 18)] = '2',\n    _a[LENGTHS.em(4 / 18)] = '3',\n    _a[LENGTHS.em(5 / 18)] = '4',\n    _a[LENGTHS.em(6 / 18)] = '5',\n    _a);\nvar CHTMLWrapper = (function (_super) {\n    __extends(CHTMLWrapper, _super);\n    function CHTMLWrapper() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.chtml = null;\n        return _this;\n    }\n    CHTMLWrapper.prototype.toCHTML = function (parent) {\n        var e_1, _a;\n        var chtml = this.standardCHTMLnode(parent);\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.toCHTML(chtml);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    CHTMLWrapper.prototype.standardCHTMLnode = function (parent) {\n        this.markUsed();\n        var chtml = this.createCHTMLnode(parent);\n        this.handleStyles();\n        this.handleVariant();\n        this.handleScale();\n        this.handleColor();\n        this.handleSpace();\n        this.handleAttributes();\n        this.handlePWidth();\n        return chtml;\n    };\n    CHTMLWrapper.prototype.markUsed = function () {\n        this.jax.wrapperUsage.add(this.kind);\n    };\n    CHTMLWrapper.prototype.createCHTMLnode = function (parent) {\n        var href = this.node.attributes.get('href');\n        if (href) {\n            parent = this.adaptor.append(parent, this.html('a', { href: href }));\n        }\n        this.chtml = this.adaptor.append(parent, this.html('mjx-' + this.node.kind));\n        return this.chtml;\n    };\n    CHTMLWrapper.prototype.handleStyles = function () {\n        if (!this.styles)\n            return;\n        var styles = this.styles.cssText;\n        if (styles) {\n            this.adaptor.setAttribute(this.chtml, 'style', styles);\n            var family = this.styles.get('font-family');\n            if (family) {\n                this.adaptor.setStyle(this.chtml, 'font-family', 'MJXZERO, ' + family);\n            }\n        }\n    };\n    CHTMLWrapper.prototype.handleVariant = function () {\n        if (this.node.isToken && this.variant !== '-explicitFont') {\n            this.adaptor.setAttribute(this.chtml, 'class', (this.font.getVariant(this.variant) || this.font.getVariant('normal')).classes);\n        }\n    };\n    CHTMLWrapper.prototype.handleScale = function () {\n        this.setScale(this.chtml, this.bbox.rscale);\n    };\n    CHTMLWrapper.prototype.setScale = function (chtml, rscale) {\n        var scale = (Math.abs(rscale - 1) < .001 ? 1 : rscale);\n        if (chtml && scale !== 1) {\n            var size = this.percent(scale);\n            if (exports.FONTSIZE[size]) {\n                this.adaptor.setAttribute(chtml, 'size', exports.FONTSIZE[size]);\n            }\n            else {\n                this.adaptor.setStyle(chtml, 'fontSize', size);\n            }\n        }\n        return chtml;\n    };\n    CHTMLWrapper.prototype.handleSpace = function () {\n        var e_2, _a;\n        try {\n            for (var _b = __values([[this.bbox.L, 'space', 'marginLeft'],\n                [this.bbox.R, 'rspace', 'marginRight']]), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var data = _c.value;\n                var _d = __read(data, 3), dimen = _d[0], name_1 = _d[1], margin = _d[2];\n                if (dimen) {\n                    var space = this.em(dimen);\n                    if (exports.SPACE[space]) {\n                        this.adaptor.setAttribute(this.chtml, name_1, exports.SPACE[space]);\n                    }\n                    else {\n                        this.adaptor.setStyle(this.chtml, margin, space);\n                    }\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    CHTMLWrapper.prototype.handleColor = function () {\n        var attributes = this.node.attributes;\n        var mathcolor = attributes.getExplicit('mathcolor');\n        var color = attributes.getExplicit('color');\n        var mathbackground = attributes.getExplicit('mathbackground');\n        var background = attributes.getExplicit('background');\n        if (mathcolor || color) {\n            this.adaptor.setStyle(this.chtml, 'color', mathcolor || color);\n        }\n        if (mathbackground || background) {\n            this.adaptor.setStyle(this.chtml, 'backgroundColor', mathbackground || background);\n        }\n    };\n    CHTMLWrapper.prototype.handleAttributes = function () {\n        var e_3, _a, e_4, _b;\n        var attributes = this.node.attributes;\n        var defaults = attributes.getAllDefaults();\n        var skip = CHTMLWrapper.skipAttributes;\n        try {\n            for (var _c = __values(attributes.getExplicitNames()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var name_2 = _d.value;\n                if (skip[name_2] === false || (!(name_2 in defaults) && !skip[name_2] &&\n                    !this.adaptor.hasAttribute(this.chtml, name_2))) {\n                    this.adaptor.setAttribute(this.chtml, name_2, attributes.getExplicit(name_2));\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        if (attributes.get('class')) {\n            var names = attributes.get('class').trim().split(/ +/);\n            try {\n                for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n                    var name_3 = names_1_1.value;\n                    this.adaptor.addClass(this.chtml, name_3);\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (names_1_1 && !names_1_1.done && (_b = names_1.return)) _b.call(names_1);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n        }\n    };\n    CHTMLWrapper.prototype.handlePWidth = function () {\n        if (this.bbox.pwidth) {\n            if (this.bbox.pwidth === BBox_js_1.BBox.fullWidth) {\n                this.adaptor.setAttribute(this.chtml, 'width', 'full');\n            }\n            else {\n                this.adaptor.setStyle(this.chtml, 'width', this.bbox.pwidth);\n            }\n        }\n    };\n    CHTMLWrapper.prototype.setIndent = function (chtml, align, shift) {\n        var adaptor = this.adaptor;\n        if (align === 'center' || align === 'left') {\n            var L = this.getBBox().L;\n            adaptor.setStyle(chtml, 'margin-left', this.em(shift + L));\n        }\n        if (align === 'center' || align === 'right') {\n            var R = this.getBBox().R;\n            adaptor.setStyle(chtml, 'margin-right', this.em(-shift + R));\n        }\n    };\n    CHTMLWrapper.prototype.drawBBox = function () {\n        var _a = this.getBBox(), w = _a.w, h = _a.h, d = _a.d, R = _a.R;\n        var box = this.html('mjx-box', { style: {\n                opacity: .25, 'margin-left': this.em(-w - R)\n            } }, [\n            this.html('mjx-box', { style: {\n                    height: this.em(h),\n                    width: this.em(w),\n                    'background-color': 'red'\n                } }),\n            this.html('mjx-box', { style: {\n                    height: this.em(d),\n                    width: this.em(w),\n                    'margin-left': this.em(-w),\n                    'vertical-align': this.em(-d),\n                    'background-color': 'green'\n                } })\n        ]);\n        var node = this.chtml || this.parent.chtml;\n        var size = this.adaptor.getAttribute(node, 'size');\n        if (size) {\n            this.adaptor.setAttribute(box, 'size', size);\n        }\n        var fontsize = this.adaptor.getStyle(node, 'fontSize');\n        if (fontsize) {\n            this.adaptor.setStyle(box, 'fontSize', fontsize);\n        }\n        this.adaptor.append(this.adaptor.parent(node), box);\n        this.adaptor.setStyle(node, 'backgroundColor', '#FFEE00');\n    };\n    CHTMLWrapper.prototype.html = function (type, def, content) {\n        if (def === void 0) { def = {}; }\n        if (content === void 0) { content = []; }\n        return this.jax.html(type, def, content);\n    };\n    CHTMLWrapper.prototype.text = function (text) {\n        return this.jax.text(text);\n    };\n    CHTMLWrapper.prototype.char = function (n) {\n        return this.font.charSelector(n).substr(1);\n    };\n    CHTMLWrapper.kind = 'unknown';\n    CHTMLWrapper.autoStyle = true;\n    return CHTMLWrapper;\n}(Wrapper_js_1.CommonWrapper));\nexports.CHTMLWrapper = CHTMLWrapper;\n//# sourceMappingURL=Wrapper.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLWrapperFactory = void 0;\nvar WrapperFactory_js_1 = require(\"../common/WrapperFactory.js\");\nvar Wrappers_js_1 = require(\"./Wrappers.js\");\nvar CHTMLWrapperFactory = (function (_super) {\n    __extends(CHTMLWrapperFactory, _super);\n    function CHTMLWrapperFactory() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLWrapperFactory.defaultNodes = Wrappers_js_1.CHTMLWrappers;\n    return CHTMLWrapperFactory;\n}(WrapperFactory_js_1.CommonWrapperFactory));\nexports.CHTMLWrapperFactory = CHTMLWrapperFactory;\n//# sourceMappingURL=WrapperFactory.js.map", "\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLWrappers = void 0;\nvar Wrapper_js_1 = require(\"./Wrapper.js\");\nvar math_js_1 = require(\"./Wrappers/math.js\");\nvar mi_js_1 = require(\"./Wrappers/mi.js\");\nvar mo_js_1 = require(\"./Wrappers/mo.js\");\nvar mn_js_1 = require(\"./Wrappers/mn.js\");\nvar ms_js_1 = require(\"./Wrappers/ms.js\");\nvar mtext_js_1 = require(\"./Wrappers/mtext.js\");\nvar mspace_js_1 = require(\"./Wrappers/mspace.js\");\nvar mpadded_js_1 = require(\"./Wrappers/mpadded.js\");\nvar menclose_js_1 = require(\"./Wrappers/menclose.js\");\nvar mrow_js_1 = require(\"./Wrappers/mrow.js\");\nvar mfenced_js_1 = require(\"./Wrappers/mfenced.js\");\nvar mfrac_js_1 = require(\"./Wrappers/mfrac.js\");\nvar msqrt_js_1 = require(\"./Wrappers/msqrt.js\");\nvar mroot_js_1 = require(\"./Wrappers/mroot.js\");\nvar msubsup_js_1 = require(\"./Wrappers/msubsup.js\");\nvar munderover_js_1 = require(\"./Wrappers/munderover.js\");\nvar mmultiscripts_js_1 = require(\"./Wrappers/mmultiscripts.js\");\nvar mtable_js_1 = require(\"./Wrappers/mtable.js\");\nvar mtr_js_1 = require(\"./Wrappers/mtr.js\");\nvar mtd_js_1 = require(\"./Wrappers/mtd.js\");\nvar maction_js_1 = require(\"./Wrappers/maction.js\");\nvar mglyph_js_1 = require(\"./Wrappers/mglyph.js\");\nvar semantics_js_1 = require(\"./Wrappers/semantics.js\");\nvar TeXAtom_js_1 = require(\"./Wrappers/TeXAtom.js\");\nvar TextNode_js_1 = require(\"./Wrappers/TextNode.js\");\nexports.CHTMLWrappers = (_a = {},\n    _a[math_js_1.CHTMLmath.kind] = math_js_1.CHTMLmath,\n    _a[mrow_js_1.CHTMLmrow.kind] = mrow_js_1.CHTMLmrow,\n    _a[mrow_js_1.CHTMLinferredMrow.kind] = mrow_js_1.CHTMLinferredMrow,\n    _a[mi_js_1.CHTMLmi.kind] = mi_js_1.CHTMLmi,\n    _a[mo_js_1.CHTMLmo.kind] = mo_js_1.CHTMLmo,\n    _a[mn_js_1.CHTMLmn.kind] = mn_js_1.CHTMLmn,\n    _a[ms_js_1.CHTMLms.kind] = ms_js_1.CHTMLms,\n    _a[mtext_js_1.CHTMLmtext.kind] = mtext_js_1.CHTMLmtext,\n    _a[mspace_js_1.CHTMLmspace.kind] = mspace_js_1.CHTMLmspace,\n    _a[mpadded_js_1.CHTMLmpadded.kind] = mpadded_js_1.CHTMLmpadded,\n    _a[menclose_js_1.CHTMLmenclose.kind] = menclose_js_1.CHTMLmenclose,\n    _a[mfrac_js_1.CHTMLmfrac.kind] = mfrac_js_1.CHTMLmfrac,\n    _a[msqrt_js_1.CHTMLmsqrt.kind] = msqrt_js_1.CHTMLmsqrt,\n    _a[mroot_js_1.CHTMLmroot.kind] = mroot_js_1.CHTMLmroot,\n    _a[msubsup_js_1.CHTMLmsub.kind] = msubsup_js_1.CHTMLmsub,\n    _a[msubsup_js_1.CHTMLmsup.kind] = msubsup_js_1.CHTMLmsup,\n    _a[msubsup_js_1.CHTMLmsubsup.kind] = msubsup_js_1.CHTMLmsubsup,\n    _a[munderover_js_1.CHTMLmunder.kind] = munderover_js_1.CHTMLmunder,\n    _a[munderover_js_1.CHTMLmover.kind] = munderover_js_1.CHTMLmover,\n    _a[munderover_js_1.CHTMLmunderover.kind] = munderover_js_1.CHTMLmunderover,\n    _a[mmultiscripts_js_1.CHTMLmmultiscripts.kind] = mmultiscripts_js_1.CHTMLmmultiscripts,\n    _a[mfenced_js_1.CHTMLmfenced.kind] = mfenced_js_1.CHTMLmfenced,\n    _a[mtable_js_1.CHTMLmtable.kind] = mtable_js_1.CHTMLmtable,\n    _a[mtr_js_1.CHTMLmtr.kind] = mtr_js_1.CHTMLmtr,\n    _a[mtr_js_1.CHTMLmlabeledtr.kind] = mtr_js_1.CHTMLmlabeledtr,\n    _a[mtd_js_1.CHTMLmtd.kind] = mtd_js_1.CHTMLmtd,\n    _a[maction_js_1.CHTMLmaction.kind] = maction_js_1.CHTMLmaction,\n    _a[mglyph_js_1.CHTMLmglyph.kind] = mglyph_js_1.CHTMLmglyph,\n    _a[semantics_js_1.CHTMLsemantics.kind] = semantics_js_1.CHTMLsemantics,\n    _a[semantics_js_1.CHTMLannotation.kind] = semantics_js_1.CHTMLannotation,\n    _a[semantics_js_1.CHTMLannotationXML.kind] = semantics_js_1.CHTMLannotationXML,\n    _a[semantics_js_1.CHTMLxml.kind] = semantics_js_1.CHTMLxml,\n    _a[TeXAtom_js_1.CHTMLTeXAtom.kind] = TeXAtom_js_1.CHTMLTeXAtom,\n    _a[TextNode_js_1.CHTMLTextNode.kind] = TextNode_js_1.CHTMLTextNode,\n    _a[Wrapper_js_1.CHTMLWrapper.kind] = Wrapper_js_1.CHTMLWrapper,\n    _a);\n//# sourceMappingURL=Wrappers.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLTeXAtom = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar TeXAtom_js_1 = require(\"../../common/Wrappers/TeXAtom.js\");\nvar TeXAtom_js_2 = require(\"../../../core/MmlTree/MmlNodes/TeXAtom.js\");\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar CHTMLTeXAtom = (function (_super) {\n    __extends(CHTMLTeXAtom, _super);\n    function CHTMLTeXAtom() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLTeXAtom.prototype.toCHTML = function (parent) {\n        _super.prototype.toCHTML.call(this, parent);\n        this.adaptor.setAttribute(this.chtml, 'texclass', MmlNode_js_1.TEXCLASSNAMES[this.node.texClass]);\n        if (this.node.texClass === MmlNode_js_1.TEXCLASS.VCENTER) {\n            var bbox = this.childNodes[0].getBBox();\n            var h = bbox.h, d = bbox.d;\n            var a = this.font.params.axis_height;\n            var dh = ((h + d) / 2 + a) - h;\n            this.adaptor.setStyle(this.chtml, 'verticalAlign', this.em(dh));\n        }\n    };\n    CHTMLTeXAtom.kind = TeXAtom_js_2.TeXAtom.prototype.kind;\n    return CHTMLTeXAtom;\n}((0, TeXAtom_js_1.CommonTeXAtomMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLTeXAtom = CHTMLTeXAtom;\n//# sourceMappingURL=TeXAtom.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLTextNode = void 0;\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar TextNode_js_1 = require(\"../../common/Wrappers/TextNode.js\");\nvar CHTMLTextNode = (function (_super) {\n    __extends(CHTMLTextNode, _super);\n    function CHTMLTextNode() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLTextNode.prototype.toCHTML = function (parent) {\n        var e_1, _a;\n        this.markUsed();\n        var adaptor = this.adaptor;\n        var variant = this.parent.variant;\n        var text = this.node.getText();\n        if (text.length === 0)\n            return;\n        if (variant === '-explicitFont') {\n            adaptor.append(parent, this.jax.unknownText(text, variant, this.getBBox().w));\n        }\n        else {\n            var chars = this.remappedText(text, variant);\n            try {\n                for (var chars_1 = __values(chars), chars_1_1 = chars_1.next(); !chars_1_1.done; chars_1_1 = chars_1.next()) {\n                    var n = chars_1_1.value;\n                    var data = this.getVariantChar(variant, n)[3];\n                    var font = (data.f ? ' TEX-' + data.f : '');\n                    var node = (data.unknown ?\n                        this.jax.unknownText(String.fromCodePoint(n), variant) :\n                        this.html('mjx-c', { class: this.char(n) + font }));\n                    adaptor.append(parent, node);\n                    !data.unknown && this.font.charUsage.add([variant, n]);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (chars_1_1 && !chars_1_1.done && (_a = chars_1.return)) _a.call(chars_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n    };\n    CHTMLTextNode.kind = MmlNode_js_1.TextNode.prototype.kind;\n    CHTMLTextNode.autoStyle = false;\n    CHTMLTextNode.styles = {\n        'mjx-c': {\n            display: 'inline-block'\n        },\n        'mjx-utext': {\n            display: 'inline-block',\n            padding: '.75em 0 .2em 0'\n        }\n    };\n    return CHTMLTextNode;\n}((0, TextNode_js_1.CommonTextNodeMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLTextNode = CHTMLTextNode;\n//# sourceMappingURL=TextNode.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmaction = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar maction_js_1 = require(\"../../common/Wrappers/maction.js\");\nvar maction_js_2 = require(\"../../common/Wrappers/maction.js\");\nvar maction_js_3 = require(\"../../../core/MmlTree/MmlNodes/maction.js\");\nvar CHTMLmaction = (function (_super) {\n    __extends(CHTMLmaction, _super);\n    function CHTMLmaction() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmaction.prototype.toCHTML = function (parent) {\n        var chtml = this.standardCHTMLnode(parent);\n        var child = this.selected;\n        child.toCHTML(chtml);\n        this.action(this, this.data);\n    };\n    CHTMLmaction.prototype.setEventHandler = function (type, handler) {\n        this.chtml.addEventListener(type, handler);\n    };\n    CHTMLmaction.kind = maction_js_3.MmlMaction.prototype.kind;\n    CHTMLmaction.styles = {\n        'mjx-maction': {\n            position: 'relative'\n        },\n        'mjx-maction > mjx-tool': {\n            display: 'none',\n            position: 'absolute',\n            bottom: 0, right: 0,\n            width: 0, height: 0,\n            'z-index': 500\n        },\n        'mjx-tool > mjx-tip': {\n            display: 'inline-block',\n            padding: '.2em',\n            border: '1px solid #888',\n            'font-size': '70%',\n            'background-color': '#F8F8F8',\n            color: 'black',\n            'box-shadow': '2px 2px 5px #AAAAAA'\n        },\n        'mjx-maction[toggle]': {\n            cursor: 'pointer'\n        },\n        'mjx-status': {\n            display: 'block',\n            position: 'fixed',\n            left: '1em',\n            bottom: '1em',\n            'min-width': '25%',\n            padding: '.2em .4em',\n            border: '1px solid #888',\n            'font-size': '90%',\n            'background-color': '#F8F8F8',\n            color: 'black'\n        }\n    };\n    CHTMLmaction.actions = new Map([\n        ['toggle', [function (node, _data) {\n                    node.adaptor.setAttribute(node.chtml, 'toggle', node.node.attributes.get('selection'));\n                    var math = node.factory.jax.math;\n                    var document = node.factory.jax.document;\n                    var mml = node.node;\n                    node.setEventHandler('click', function (event) {\n                        if (!math.end.node) {\n                            math.start.node = math.end.node = math.typesetRoot;\n                            math.start.n = math.end.n = 0;\n                        }\n                        mml.nextToggleSelection();\n                        math.rerender(document);\n                        event.stopPropagation();\n                    });\n                }, {}]],\n        ['tooltip', [function (node, data) {\n                    var tip = node.childNodes[1];\n                    if (!tip)\n                        return;\n                    if (tip.node.isKind('mtext')) {\n                        var text = tip.node.getText();\n                        node.adaptor.setAttribute(node.chtml, 'title', text);\n                    }\n                    else {\n                        var adaptor_1 = node.adaptor;\n                        var tool_1 = adaptor_1.append(node.chtml, node.html('mjx-tool', {\n                            style: { bottom: node.em(-node.dy), right: node.em(-node.dx) }\n                        }, [node.html('mjx-tip')]));\n                        tip.toCHTML(adaptor_1.firstChild(tool_1));\n                        node.setEventHandler('mouseover', function (event) {\n                            data.stopTimers(node, data);\n                            var timeout = setTimeout(function () { return adaptor_1.setStyle(tool_1, 'display', 'block'); }, data.postDelay);\n                            data.hoverTimer.set(node, timeout);\n                            event.stopPropagation();\n                        });\n                        node.setEventHandler('mouseout', function (event) {\n                            data.stopTimers(node, data);\n                            var timeout = setTimeout(function () { return adaptor_1.setStyle(tool_1, 'display', ''); }, data.clearDelay);\n                            data.clearTimer.set(node, timeout);\n                            event.stopPropagation();\n                        });\n                    }\n                }, maction_js_2.TooltipData]],\n        ['statusline', [function (node, data) {\n                    var tip = node.childNodes[1];\n                    if (!tip)\n                        return;\n                    if (tip.node.isKind('mtext')) {\n                        var adaptor_2 = node.adaptor;\n                        var text_1 = tip.node.getText();\n                        adaptor_2.setAttribute(node.chtml, 'statusline', text_1);\n                        node.setEventHandler('mouseover', function (event) {\n                            if (data.status === null) {\n                                var body = adaptor_2.body(adaptor_2.document);\n                                data.status = adaptor_2.append(body, node.html('mjx-status', {}, [node.text(text_1)]));\n                            }\n                            event.stopPropagation();\n                        });\n                        node.setEventHandler('mouseout', function (event) {\n                            if (data.status) {\n                                adaptor_2.remove(data.status);\n                                data.status = null;\n                            }\n                            event.stopPropagation();\n                        });\n                    }\n                }, {\n                    status: null\n                }]]\n    ]);\n    return CHTMLmaction;\n}((0, maction_js_1.CommonMactionMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmaction = CHTMLmaction;\n//# sourceMappingURL=maction.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmath = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar math_js_1 = require(\"../../common/Wrappers/math.js\");\nvar math_js_2 = require(\"../../../core/MmlTree/MmlNodes/math.js\");\nvar BBox_js_1 = require(\"../../../util/BBox.js\");\nvar CHTMLmath = (function (_super) {\n    __extends(CHTMLmath, _super);\n    function CHTMLmath() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmath.prototype.toCHTML = function (parent) {\n        _super.prototype.toCHTML.call(this, parent);\n        var chtml = this.chtml;\n        var adaptor = this.adaptor;\n        var display = (this.node.attributes.get('display') === 'block');\n        if (display) {\n            adaptor.setAttribute(chtml, 'display', 'true');\n            adaptor.setAttribute(parent, 'display', 'true');\n            this.handleDisplay(parent);\n        }\n        else {\n            this.handleInline(parent);\n        }\n        adaptor.addClass(chtml, 'MJX-TEX');\n    };\n    CHTMLmath.prototype.handleDisplay = function (parent) {\n        var adaptor = this.adaptor;\n        var _a = __read(this.getAlignShift(), 2), align = _a[0], shift = _a[1];\n        if (align !== 'center') {\n            adaptor.setAttribute(parent, 'justify', align);\n        }\n        if (this.bbox.pwidth === BBox_js_1.BBox.fullWidth) {\n            adaptor.setAttribute(parent, 'width', 'full');\n            if (this.jax.table) {\n                var _b = this.jax.table.getOuterBBox(), L = _b.L, w = _b.w, R = _b.R;\n                if (align === 'right') {\n                    R = Math.max(R || -shift, -shift);\n                }\n                else if (align === 'left') {\n                    L = Math.max(L || shift, shift);\n                }\n                else if (align === 'center') {\n                    w += 2 * Math.abs(shift);\n                }\n                var W = this.em(Math.max(0, L + w + R));\n                adaptor.setStyle(parent, 'min-width', W);\n                adaptor.setStyle(this.jax.table.chtml, 'min-width', W);\n            }\n        }\n        else {\n            this.setIndent(this.chtml, align, shift);\n        }\n    };\n    CHTMLmath.prototype.handleInline = function (parent) {\n        var adaptor = this.adaptor;\n        var margin = adaptor.getStyle(this.chtml, 'margin-right');\n        if (margin) {\n            adaptor.setStyle(this.chtml, 'margin-right', '');\n            adaptor.setStyle(parent, 'margin-right', margin);\n            adaptor.setStyle(parent, 'width', '0');\n        }\n    };\n    CHTMLmath.prototype.setChildPWidths = function (recompute, w, clear) {\n        if (w === void 0) { w = null; }\n        if (clear === void 0) { clear = true; }\n        return (this.parent ? _super.prototype.setChildPWidths.call(this, recompute, w, clear) : false);\n    };\n    CHTMLmath.kind = math_js_2.MmlMath.prototype.kind;\n    CHTMLmath.styles = {\n        'mjx-math': {\n            'line-height': 0,\n            'text-align': 'left',\n            'text-indent': 0,\n            'font-style': 'normal',\n            'font-weight': 'normal',\n            'font-size': '100%',\n            'font-size-adjust': 'none',\n            'letter-spacing': 'normal',\n            'border-collapse': 'collapse',\n            'word-wrap': 'normal',\n            'word-spacing': 'normal',\n            'white-space': 'nowrap',\n            'direction': 'ltr',\n            'padding': '1px 0'\n        },\n        'mjx-container[jax=\"CHTML\"][display=\"true\"]': {\n            display: 'block',\n            'text-align': 'center',\n            margin: '1em 0'\n        },\n        'mjx-container[jax=\"CHTML\"][display=\"true\"][width=\"full\"]': {\n            display: 'flex'\n        },\n        'mjx-container[jax=\"CHTML\"][display=\"true\"] mjx-math': {\n            padding: 0\n        },\n        'mjx-container[jax=\"CHTML\"][justify=\"left\"]': {\n            'text-align': 'left'\n        },\n        'mjx-container[jax=\"CHTML\"][justify=\"right\"]': {\n            'text-align': 'right'\n        }\n    };\n    return CHTMLmath;\n}((0, math_js_1.CommonMathMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmath = CHTMLmath;\n//# sourceMappingURL=math.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmenclose = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar menclose_js_1 = require(\"../../common/Wrappers/menclose.js\");\nvar Notation = __importStar(require(\"../Notation.js\"));\nvar menclose_js_2 = require(\"../../../core/MmlTree/MmlNodes/menclose.js\");\nvar lengths_js_1 = require(\"../../../util/lengths.js\");\nfunction Angle(x, y) {\n    return Math.atan2(x, y).toFixed(3).replace(/\\.?0+$/, '');\n}\nvar ANGLE = Angle(Notation.ARROWDX, Notation.ARROWY);\nvar CHTMLmenclose = (function (_super) {\n    __extends(CHTMLmenclose, _super);\n    function CHTMLmenclose() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmenclose.prototype.toCHTML = function (parent) {\n        var e_1, _a, e_2, _b;\n        var adaptor = this.adaptor;\n        var chtml = this.standardCHTMLnode(parent);\n        var block = adaptor.append(chtml, this.html('mjx-box'));\n        if (this.renderChild) {\n            this.renderChild(this, block);\n        }\n        else {\n            this.childNodes[0].toCHTML(block);\n        }\n        try {\n            for (var _c = __values(Object.keys(this.notations)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var name_1 = _d.value;\n                var notation = this.notations[name_1];\n                !notation.renderChild && notation.renderer(this, block);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var pbox = this.getPadding();\n        try {\n            for (var _e = __values(Notation.sideNames), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var name_2 = _f.value;\n                var i = Notation.sideIndex[name_2];\n                pbox[i] > 0 && adaptor.setStyle(block, 'padding-' + name_2, this.em(pbox[i]));\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    CHTMLmenclose.prototype.arrow = function (w, a, double, offset, dist) {\n        if (offset === void 0) { offset = ''; }\n        if (dist === void 0) { dist = 0; }\n        var W = this.getBBox().w;\n        var style = { width: this.em(w) };\n        if (W !== w) {\n            style.left = this.em((W - w) / 2);\n        }\n        if (a) {\n            style.transform = 'rotate(' + this.fixed(a) + 'rad)';\n        }\n        var arrow = this.html('mjx-arrow', { style: style }, [\n            this.html('mjx-aline'), this.html('mjx-rthead'), this.html('mjx-rbhead')\n        ]);\n        if (double) {\n            this.adaptor.append(arrow, this.html('mjx-lthead'));\n            this.adaptor.append(arrow, this.html('mjx-lbhead'));\n            this.adaptor.setAttribute(arrow, 'double', 'true');\n        }\n        this.adjustArrow(arrow, double);\n        this.moveArrow(arrow, offset, dist);\n        return arrow;\n    };\n    CHTMLmenclose.prototype.adjustArrow = function (arrow, double) {\n        var _this = this;\n        var t = this.thickness;\n        var head = this.arrowhead;\n        if (head.x === Notation.ARROWX && head.y === Notation.ARROWY &&\n            head.dx === Notation.ARROWDX && t === Notation.THICKNESS)\n            return;\n        var _a = __read([t * head.x, t * head.y].map(function (x) { return _this.em(x); }), 2), x = _a[0], y = _a[1];\n        var a = Angle(head.dx, head.y);\n        var _b = __read(this.adaptor.childNodes(arrow), 5), line = _b[0], rthead = _b[1], rbhead = _b[2], lthead = _b[3], lbhead = _b[4];\n        this.adjustHead(rthead, [y, '0', '1px', x], a);\n        this.adjustHead(rbhead, ['1px', '0', y, x], '-' + a);\n        this.adjustHead(lthead, [y, x, '1px', '0'], '-' + a);\n        this.adjustHead(lbhead, ['1px', x, y, '0'], a);\n        this.adjustLine(line, t, head.x, double);\n    };\n    CHTMLmenclose.prototype.adjustHead = function (head, border, a) {\n        if (head) {\n            this.adaptor.setStyle(head, 'border-width', border.join(' '));\n            this.adaptor.setStyle(head, 'transform', 'skewX(' + a + 'rad)');\n        }\n    };\n    CHTMLmenclose.prototype.adjustLine = function (line, t, x, double) {\n        this.adaptor.setStyle(line, 'borderTop', this.em(t) + ' solid');\n        this.adaptor.setStyle(line, 'top', this.em(-t / 2));\n        this.adaptor.setStyle(line, 'right', this.em(t * (x - 1)));\n        if (double) {\n            this.adaptor.setStyle(line, 'left', this.em(t * (x - 1)));\n        }\n    };\n    CHTMLmenclose.prototype.moveArrow = function (arrow, offset, d) {\n        if (!d)\n            return;\n        var transform = this.adaptor.getStyle(arrow, 'transform');\n        this.adaptor.setStyle(arrow, 'transform', \"translate\".concat(offset, \"(\").concat(this.em(-d), \")\").concat((transform ? ' ' + transform : '')));\n    };\n    CHTMLmenclose.prototype.adjustBorder = function (node) {\n        if (this.thickness !== Notation.THICKNESS) {\n            this.adaptor.setStyle(node, 'borderWidth', this.em(this.thickness));\n        }\n        return node;\n    };\n    CHTMLmenclose.prototype.adjustThickness = function (shape) {\n        if (this.thickness !== Notation.THICKNESS) {\n            this.adaptor.setStyle(shape, 'strokeWidth', this.fixed(this.thickness));\n        }\n        return shape;\n    };\n    CHTMLmenclose.prototype.fixed = function (m, n) {\n        if (n === void 0) { n = 3; }\n        if (Math.abs(m) < .0006) {\n            return '0';\n        }\n        return m.toFixed(n).replace(/\\.?0+$/, '');\n    };\n    CHTMLmenclose.prototype.em = function (m) {\n        return _super.prototype.em.call(this, m);\n    };\n    CHTMLmenclose.kind = menclose_js_2.MmlMenclose.prototype.kind;\n    CHTMLmenclose.styles = {\n        'mjx-menclose': {\n            position: 'relative'\n        },\n        'mjx-menclose > mjx-dstrike': {\n            display: 'inline-block',\n            left: 0, top: 0,\n            position: 'absolute',\n            'border-top': Notation.SOLID,\n            'transform-origin': 'top left'\n        },\n        'mjx-menclose > mjx-ustrike': {\n            display: 'inline-block',\n            left: 0, bottom: 0,\n            position: 'absolute',\n            'border-top': Notation.SOLID,\n            'transform-origin': 'bottom left'\n        },\n        'mjx-menclose > mjx-hstrike': {\n            'border-top': Notation.SOLID,\n            position: 'absolute',\n            left: 0, right: 0, bottom: '50%',\n            transform: 'translateY(' + (0, lengths_js_1.em)(Notation.THICKNESS / 2) + ')'\n        },\n        'mjx-menclose > mjx-vstrike': {\n            'border-left': Notation.SOLID,\n            position: 'absolute',\n            top: 0, bottom: 0, right: '50%',\n            transform: 'translateX(' + (0, lengths_js_1.em)(Notation.THICKNESS / 2) + ')'\n        },\n        'mjx-menclose > mjx-rbox': {\n            position: 'absolute',\n            top: 0, bottom: 0, right: 0, left: 0,\n            'border': Notation.SOLID,\n            'border-radius': (0, lengths_js_1.em)(Notation.THICKNESS + Notation.PADDING)\n        },\n        'mjx-menclose > mjx-cbox': {\n            position: 'absolute',\n            top: 0, bottom: 0, right: 0, left: 0,\n            'border': Notation.SOLID,\n            'border-radius': '50%'\n        },\n        'mjx-menclose > mjx-arrow': {\n            position: 'absolute',\n            left: 0, bottom: '50%', height: 0, width: 0\n        },\n        'mjx-menclose > mjx-arrow > *': {\n            display: 'block',\n            position: 'absolute',\n            'transform-origin': 'bottom',\n            'border-left': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWX) + ' solid',\n            'border-right': 0,\n            'box-sizing': 'border-box'\n        },\n        'mjx-menclose > mjx-arrow > mjx-aline': {\n            left: 0, top: (0, lengths_js_1.em)(-Notation.THICKNESS / 2),\n            right: (0, lengths_js_1.em)(Notation.THICKNESS * (Notation.ARROWX - 1)), height: 0,\n            'border-top': (0, lengths_js_1.em)(Notation.THICKNESS) + ' solid',\n            'border-left': 0\n        },\n        'mjx-menclose > mjx-arrow[double] > mjx-aline': {\n            left: (0, lengths_js_1.em)(Notation.THICKNESS * (Notation.ARROWX - 1)), height: 0,\n        },\n        'mjx-menclose > mjx-arrow > mjx-rthead': {\n            transform: 'skewX(' + ANGLE + 'rad)',\n            right: 0, bottom: '-1px',\n            'border-bottom': '1px solid transparent',\n            'border-top': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWY) + ' solid transparent'\n        },\n        'mjx-menclose > mjx-arrow > mjx-rbhead': {\n            transform: 'skewX(-' + ANGLE + 'rad)',\n            'transform-origin': 'top',\n            right: 0, top: '-1px',\n            'border-top': '1px solid transparent',\n            'border-bottom': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWY) + ' solid transparent'\n        },\n        'mjx-menclose > mjx-arrow > mjx-lthead': {\n            transform: 'skewX(-' + ANGLE + 'rad)',\n            left: 0, bottom: '-1px',\n            'border-left': 0,\n            'border-right': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWX) + ' solid',\n            'border-bottom': '1px solid transparent',\n            'border-top': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWY) + ' solid transparent'\n        },\n        'mjx-menclose > mjx-arrow > mjx-lbhead': {\n            transform: 'skewX(' + ANGLE + 'rad)',\n            'transform-origin': 'top',\n            left: 0, top: '-1px',\n            'border-left': 0,\n            'border-right': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWX) + ' solid',\n            'border-top': '1px solid transparent',\n            'border-bottom': (0, lengths_js_1.em)(Notation.THICKNESS * Notation.ARROWY) + ' solid transparent'\n        },\n        'mjx-menclose > dbox': {\n            position: 'absolute',\n            top: 0, bottom: 0, left: (0, lengths_js_1.em)(-1.5 * Notation.PADDING),\n            width: (0, lengths_js_1.em)(3 * Notation.PADDING),\n            border: (0, lengths_js_1.em)(Notation.THICKNESS) + ' solid',\n            'border-radius': '50%',\n            'clip-path': 'inset(0 0 0 ' + (0, lengths_js_1.em)(1.5 * Notation.PADDING) + ')',\n            'box-sizing': 'border-box'\n        }\n    };\n    CHTMLmenclose.notations = new Map([\n        Notation.Border('top'),\n        Notation.Border('right'),\n        Notation.Border('bottom'),\n        Notation.Border('left'),\n        Notation.Border2('actuarial', 'top', 'right'),\n        Notation.Border2('madruwb', 'bottom', 'right'),\n        Notation.DiagonalStrike('up', 1),\n        Notation.DiagonalStrike('down', -1),\n        ['horizontalstrike', {\n                renderer: Notation.RenderElement('hstrike', 'Y'),\n                bbox: function (node) { return [0, node.padding, 0, node.padding]; }\n            }],\n        ['verticalstrike', {\n                renderer: Notation.RenderElement('vstrike', 'X'),\n                bbox: function (node) { return [node.padding, 0, node.padding, 0]; }\n            }],\n        ['box', {\n                renderer: function (node, child) {\n                    node.adaptor.setStyle(child, 'border', node.em(node.thickness) + ' solid');\n                },\n                bbox: Notation.fullBBox,\n                border: Notation.fullBorder,\n                remove: 'left right top bottom'\n            }],\n        ['roundedbox', {\n                renderer: Notation.RenderElement('rbox'),\n                bbox: Notation.fullBBox\n            }],\n        ['circle', {\n                renderer: Notation.RenderElement('cbox'),\n                bbox: Notation.fullBBox\n            }],\n        ['phasorangle', {\n                renderer: function (node, child) {\n                    var _a = node.getBBox(), h = _a.h, d = _a.d;\n                    var _b = __read(node.getArgMod(1.75 * node.padding, h + d), 2), a = _b[0], W = _b[1];\n                    var t = node.thickness * Math.sin(a) * .9;\n                    node.adaptor.setStyle(child, 'border-bottom', node.em(node.thickness) + ' solid');\n                    var strike = node.adjustBorder(node.html('mjx-ustrike', { style: {\n                            width: node.em(W),\n                            transform: 'translateX(' + node.em(t) + ') rotate(' + node.fixed(-a) + 'rad)',\n                        } }));\n                    node.adaptor.append(node.chtml, strike);\n                },\n                bbox: function (node) {\n                    var p = node.padding / 2;\n                    var t = node.thickness;\n                    return [2 * p, p, p + t, 3 * p + t];\n                },\n                border: function (node) { return [0, 0, node.thickness, 0]; },\n                remove: 'bottom'\n            }],\n        Notation.Arrow('up'),\n        Notation.Arrow('down'),\n        Notation.Arrow('left'),\n        Notation.Arrow('right'),\n        Notation.Arrow('updown'),\n        Notation.Arrow('leftright'),\n        Notation.DiagonalArrow('updiagonal'),\n        Notation.DiagonalArrow('northeast'),\n        Notation.DiagonalArrow('southeast'),\n        Notation.DiagonalArrow('northwest'),\n        Notation.DiagonalArrow('southwest'),\n        Notation.DiagonalArrow('northeastsouthwest'),\n        Notation.DiagonalArrow('northwestsoutheast'),\n        ['longdiv', {\n                renderer: function (node, child) {\n                    var adaptor = node.adaptor;\n                    adaptor.setStyle(child, 'border-top', node.em(node.thickness) + ' solid');\n                    var arc = adaptor.append(node.chtml, node.html('dbox'));\n                    var t = node.thickness;\n                    var p = node.padding;\n                    if (t !== Notation.THICKNESS) {\n                        adaptor.setStyle(arc, 'border-width', node.em(t));\n                    }\n                    if (p !== Notation.PADDING) {\n                        adaptor.setStyle(arc, 'left', node.em(-1.5 * p));\n                        adaptor.setStyle(arc, 'width', node.em(3 * p));\n                        adaptor.setStyle(arc, 'clip-path', 'inset(0 0 0 ' + node.em(1.5 * p) + ')');\n                    }\n                },\n                bbox: function (node) {\n                    var p = node.padding;\n                    var t = node.thickness;\n                    return [p + t, p, p, 2 * p + t / 2];\n                }\n            }],\n        ['radical', {\n                renderer: function (node, child) {\n                    node.msqrt.toCHTML(child);\n                    var TRBL = node.sqrtTRBL();\n                    node.adaptor.setStyle(node.msqrt.chtml, 'margin', TRBL.map(function (x) { return node.em(-x); }).join(' '));\n                },\n                init: function (node) {\n                    node.msqrt = node.createMsqrt(node.childNodes[0]);\n                },\n                bbox: function (node) { return node.sqrtTRBL(); },\n                renderChild: true\n            }]\n    ]);\n    return CHTMLmenclose;\n}((0, menclose_js_1.CommonMencloseMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmenclose = CHTMLmenclose;\n//# sourceMappingURL=menclose.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmfenced = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mfenced_js_1 = require(\"../../common/Wrappers/mfenced.js\");\nvar mfenced_js_2 = require(\"../../../core/MmlTree/MmlNodes/mfenced.js\");\nvar CHTMLmfenced = (function (_super) {\n    __extends(CHTMLmfenced, _super);\n    function CHTMLmfenced() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmfenced.prototype.toCHTML = function (parent) {\n        var chtml = this.standardCHTMLnode(parent);\n        this.mrow.toCHTML(chtml);\n    };\n    CHTMLmfenced.kind = mfenced_js_2.MmlMfenced.prototype.kind;\n    return CHTMLmfenced;\n}((0, mfenced_js_1.CommonMfencedMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmfenced = CHTMLmfenced;\n//# sourceMappingURL=mfenced.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmfrac = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mfrac_js_1 = require(\"../../common/Wrappers/mfrac.js\");\nvar mfrac_js_2 = require(\"../../../core/MmlTree/MmlNodes/mfrac.js\");\nvar CHTMLmfrac = (function (_super) {\n    __extends(CHTMLmfrac, _super);\n    function CHTMLmfrac() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmfrac.prototype.toCHTML = function (parent) {\n        this.standardCHTMLnode(parent);\n        var _a = this.node.attributes.getList('linethickness', 'bevelled'), linethickness = _a.linethickness, bevelled = _a.bevelled;\n        var display = this.isDisplay();\n        if (bevelled) {\n            this.makeBevelled(display);\n        }\n        else {\n            var thickness = this.length2em(String(linethickness), .06);\n            if (thickness === 0) {\n                this.makeAtop(display);\n            }\n            else {\n                this.makeFraction(display, thickness);\n            }\n        }\n    };\n    CHTMLmfrac.prototype.makeFraction = function (display, t) {\n        var _a = this.node.attributes.getList('numalign', 'denomalign'), numalign = _a.numalign, denomalign = _a.denomalign;\n        var withDelims = this.node.getProperty('withDelims');\n        var attr = (display ? { type: 'd' } : {});\n        var fattr = (withDelims ? __assign(__assign({}, attr), { delims: 'true' }) : __assign({}, attr));\n        var nattr = (numalign !== 'center' ? { align: numalign } : {});\n        var dattr = (denomalign !== 'center' ? { align: denomalign } : {});\n        var dsattr = __assign({}, attr), nsattr = __assign({}, attr);\n        var tex = this.font.params;\n        if (t !== .06) {\n            var a = tex.axis_height;\n            var tEm = this.em(t);\n            var _b = this.getTUV(display, t), T = _b.T, u = _b.u, v = _b.v;\n            var m = (display ? this.em(3 * t) : tEm) + ' -.1em';\n            attr.style = { height: tEm, 'border-top': tEm + ' solid', margin: m };\n            var nh = this.em(Math.max(0, u));\n            nsattr.style = { height: nh, 'vertical-align': '-' + nh };\n            dsattr.style = { height: this.em(Math.max(0, v)) };\n            fattr.style = { 'vertical-align': this.em(a - T) };\n        }\n        var num, den;\n        this.adaptor.append(this.chtml, this.html('mjx-frac', fattr, [\n            num = this.html('mjx-num', nattr, [this.html('mjx-nstrut', nsattr)]),\n            this.html('mjx-dbox', {}, [\n                this.html('mjx-dtable', {}, [\n                    this.html('mjx-line', attr),\n                    this.html('mjx-row', {}, [\n                        den = this.html('mjx-den', dattr, [this.html('mjx-dstrut', dsattr)])\n                    ])\n                ])\n            ])\n        ]));\n        this.childNodes[0].toCHTML(num);\n        this.childNodes[1].toCHTML(den);\n    };\n    CHTMLmfrac.prototype.makeAtop = function (display) {\n        var _a = this.node.attributes.getList('numalign', 'denomalign'), numalign = _a.numalign, denomalign = _a.denomalign;\n        var withDelims = this.node.getProperty('withDelims');\n        var attr = (display ? { type: 'd', atop: true } : { atop: true });\n        var fattr = (withDelims ? __assign(__assign({}, attr), { delims: true }) : __assign({}, attr));\n        var nattr = (numalign !== 'center' ? { align: numalign } : {});\n        var dattr = (denomalign !== 'center' ? { align: denomalign } : {});\n        var _b = this.getUVQ(display), v = _b.v, q = _b.q;\n        nattr.style = { 'padding-bottom': this.em(q) };\n        fattr.style = { 'vertical-align': this.em(-v) };\n        var num, den;\n        this.adaptor.append(this.chtml, this.html('mjx-frac', fattr, [\n            num = this.html('mjx-num', nattr),\n            den = this.html('mjx-den', dattr)\n        ]));\n        this.childNodes[0].toCHTML(num);\n        this.childNodes[1].toCHTML(den);\n    };\n    CHTMLmfrac.prototype.makeBevelled = function (display) {\n        var adaptor = this.adaptor;\n        adaptor.setAttribute(this.chtml, 'bevelled', 'ture');\n        var num = adaptor.append(this.chtml, this.html('mjx-num'));\n        this.childNodes[0].toCHTML(num);\n        this.bevel.toCHTML(this.chtml);\n        var den = adaptor.append(this.chtml, this.html('mjx-den'));\n        this.childNodes[1].toCHTML(den);\n        var _a = this.getBevelData(display), u = _a.u, v = _a.v, delta = _a.delta, nbox = _a.nbox, dbox = _a.dbox;\n        if (u) {\n            adaptor.setStyle(num, 'verticalAlign', this.em(u / nbox.scale));\n        }\n        if (v) {\n            adaptor.setStyle(den, 'verticalAlign', this.em(v / dbox.scale));\n        }\n        var dx = this.em(-delta / 2);\n        adaptor.setStyle(this.bevel.chtml, 'marginLeft', dx);\n        adaptor.setStyle(this.bevel.chtml, 'marginRight', dx);\n    };\n    CHTMLmfrac.kind = mfrac_js_2.MmlMfrac.prototype.kind;\n    CHTMLmfrac.styles = {\n        'mjx-frac': {\n            display: 'inline-block',\n            'vertical-align': '0.17em',\n            padding: '0 .22em'\n        },\n        'mjx-frac[type=\"d\"]': {\n            'vertical-align': '.04em'\n        },\n        'mjx-frac[delims]': {\n            padding: '0 .1em'\n        },\n        'mjx-frac[atop]': {\n            padding: '0 .12em'\n        },\n        'mjx-frac[atop][delims]': {\n            padding: '0'\n        },\n        'mjx-dtable': {\n            display: 'inline-table',\n            width: '100%'\n        },\n        'mjx-dtable > *': {\n            'font-size': '2000%'\n        },\n        'mjx-dbox': {\n            display: 'block',\n            'font-size': '5%'\n        },\n        'mjx-num': {\n            display: 'block',\n            'text-align': 'center'\n        },\n        'mjx-den': {\n            display: 'block',\n            'text-align': 'center'\n        },\n        'mjx-mfrac[bevelled] > mjx-num': {\n            display: 'inline-block'\n        },\n        'mjx-mfrac[bevelled] > mjx-den': {\n            display: 'inline-block'\n        },\n        'mjx-den[align=\"right\"], mjx-num[align=\"right\"]': {\n            'text-align': 'right'\n        },\n        'mjx-den[align=\"left\"], mjx-num[align=\"left\"]': {\n            'text-align': 'left'\n        },\n        'mjx-nstrut': {\n            display: 'inline-block',\n            height: '.054em',\n            width: 0,\n            'vertical-align': '-.054em'\n        },\n        'mjx-nstrut[type=\"d\"]': {\n            height: '.217em',\n            'vertical-align': '-.217em',\n        },\n        'mjx-dstrut': {\n            display: 'inline-block',\n            height: '.505em',\n            width: 0\n        },\n        'mjx-dstrut[type=\"d\"]': {\n            height: '.726em',\n        },\n        'mjx-line': {\n            display: 'block',\n            'box-sizing': 'border-box',\n            'min-height': '1px',\n            height: '.06em',\n            'border-top': '.06em solid',\n            margin: '.06em -.1em',\n            overflow: 'hidden'\n        },\n        'mjx-line[type=\"d\"]': {\n            margin: '.18em -.1em'\n        }\n    };\n    return CHTMLmfrac;\n}((0, mfrac_js_1.CommonMfracMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmfrac = CHTMLmfrac;\n//# sourceMappingURL=mfrac.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmglyph = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mglyph_js_1 = require(\"../../common/Wrappers/mglyph.js\");\nvar mglyph_js_2 = require(\"../../../core/MmlTree/MmlNodes/mglyph.js\");\nvar CHTMLmglyph = (function (_super) {\n    __extends(CHTMLmglyph, _super);\n    function CHTMLmglyph() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmglyph.prototype.toCHTML = function (parent) {\n        var chtml = this.standardCHTMLnode(parent);\n        if (this.charWrapper) {\n            this.charWrapper.toCHTML(chtml);\n            return;\n        }\n        var _a = this.node.attributes.getList('src', 'alt'), src = _a.src, alt = _a.alt;\n        var styles = {\n            width: this.em(this.width),\n            height: this.em(this.height)\n        };\n        if (this.valign) {\n            styles.verticalAlign = this.em(this.valign);\n        }\n        var img = this.html('img', { src: src, style: styles, alt: alt, title: alt });\n        this.adaptor.append(chtml, img);\n    };\n    CHTMLmglyph.kind = mglyph_js_2.MmlMglyph.prototype.kind;\n    CHTMLmglyph.styles = {\n        'mjx-mglyph > img': {\n            display: 'inline-block',\n            border: 0,\n            padding: 0\n        }\n    };\n    return CHTMLmglyph;\n}((0, mglyph_js_1.CommonMglyphMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmglyph = CHTMLmglyph;\n//# sourceMappingURL=mglyph.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmi = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mi_js_1 = require(\"../../common/Wrappers/mi.js\");\nvar mi_js_2 = require(\"../../../core/MmlTree/MmlNodes/mi.js\");\nvar CHTMLmi = (function (_super) {\n    __extends(CHTMLmi, _super);\n    function CHTMLmi() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmi.kind = mi_js_2.MmlMi.prototype.kind;\n    return CHTMLmi;\n}((0, mi_js_1.CommonMiMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmi = CHTMLmi;\n//# sourceMappingURL=mi.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmmultiscripts = void 0;\nvar msubsup_js_1 = require(\"./msubsup.js\");\nvar mmultiscripts_js_1 = require(\"../../common/Wrappers/mmultiscripts.js\");\nvar mmultiscripts_js_2 = require(\"../../../core/MmlTree/MmlNodes/mmultiscripts.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar CHTMLmmultiscripts = (function (_super) {\n    __extends(CHTMLmmultiscripts, _super);\n    function CHTMLmmultiscripts() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmmultiscripts.prototype.toCHTML = function (parent) {\n        var chtml = this.standardCHTMLnode(parent);\n        var data = this.scriptData;\n        var scriptalign = this.node.getProperty('scriptalign') || 'right left';\n        var _a = __read((0, string_js_1.split)(scriptalign + ' ' + scriptalign), 2), preAlign = _a[0], postAlign = _a[1];\n        var sub = this.combinePrePost(data.sub, data.psub);\n        var sup = this.combinePrePost(data.sup, data.psup);\n        var _b = __read(this.getUVQ(sub, sup), 2), u = _b[0], v = _b[1];\n        if (data.numPrescripts) {\n            var scripts = this.addScripts(u, -v, true, data.psub, data.psup, this.firstPrescript, data.numPrescripts);\n            preAlign !== 'right' && this.adaptor.setAttribute(scripts, 'script-align', preAlign);\n        }\n        this.childNodes[0].toCHTML(chtml);\n        if (data.numScripts) {\n            var scripts = this.addScripts(u, -v, false, data.sub, data.sup, 1, data.numScripts);\n            postAlign !== 'left' && this.adaptor.setAttribute(scripts, 'script-align', postAlign);\n        }\n    };\n    CHTMLmmultiscripts.prototype.addScripts = function (u, v, isPre, sub, sup, i, n) {\n        var adaptor = this.adaptor;\n        var q = (u - sup.d) + (v - sub.h);\n        var U = (u < 0 && v === 0 ? sub.h + u : u);\n        var rowdef = (q > 0 ? { style: { height: this.em(q) } } : {});\n        var tabledef = (U ? { style: { 'vertical-align': this.em(U) } } : {});\n        var supRow = this.html('mjx-row');\n        var sepRow = this.html('mjx-row', rowdef);\n        var subRow = this.html('mjx-row');\n        var name = 'mjx-' + (isPre ? 'pre' : '') + 'scripts';\n        var m = i + 2 * n;\n        while (i < m) {\n            this.childNodes[i++].toCHTML(adaptor.append(subRow, this.html('mjx-cell')));\n            this.childNodes[i++].toCHTML(adaptor.append(supRow, this.html('mjx-cell')));\n        }\n        return adaptor.append(this.chtml, this.html(name, tabledef, [supRow, sepRow, subRow]));\n    };\n    CHTMLmmultiscripts.kind = mmultiscripts_js_2.MmlMmultiscripts.prototype.kind;\n    CHTMLmmultiscripts.styles = {\n        'mjx-prescripts': {\n            display: 'inline-table',\n            'padding-left': '.05em'\n        },\n        'mjx-scripts': {\n            display: 'inline-table',\n            'padding-right': '.05em'\n        },\n        'mjx-prescripts > mjx-row > mjx-cell': {\n            'text-align': 'right'\n        },\n        '[script-align=\"left\"] > mjx-row > mjx-cell': {\n            'text-align': 'left'\n        },\n        '[script-align=\"center\"] > mjx-row > mjx-cell': {\n            'text-align': 'center'\n        },\n        '[script-align=\"right\"] > mjx-row > mjx-cell': {\n            'text-align': 'right'\n        }\n    };\n    return CHTMLmmultiscripts;\n}((0, mmultiscripts_js_1.CommonMmultiscriptsMixin)(msubsup_js_1.CHTMLmsubsup)));\nexports.CHTMLmmultiscripts = CHTMLmmultiscripts;\n//# sourceMappingURL=mmultiscripts.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmn = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mn_js_1 = require(\"../../common/Wrappers/mn.js\");\nvar mn_js_2 = require(\"../../../core/MmlTree/MmlNodes/mn.js\");\nvar CHTMLmn = (function (_super) {\n    __extends(CHTMLmn, _super);\n    function CHTMLmn() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmn.kind = mn_js_2.MmlMn.prototype.kind;\n    return CHTMLmn;\n}((0, mn_js_1.CommonMnMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmn = CHTMLmn;\n//# sourceMappingURL=mn.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmo = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mo_js_1 = require(\"../../common/Wrappers/mo.js\");\nvar mo_js_2 = require(\"../../../core/MmlTree/MmlNodes/mo.js\");\nvar CHTMLmo = (function (_super) {\n    __extends(CHTMLmo, _super);\n    function CHTMLmo() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmo.prototype.toCHTML = function (parent) {\n        var e_1, _a;\n        var attributes = this.node.attributes;\n        var symmetric = attributes.get('symmetric') && this.stretch.dir !== 2;\n        var stretchy = this.stretch.dir !== 0;\n        if (stretchy && this.size === null) {\n            this.getStretchedVariant([]);\n        }\n        var chtml = this.standardCHTMLnode(parent);\n        if (stretchy && this.size < 0) {\n            this.stretchHTML(chtml);\n        }\n        else {\n            if (symmetric || attributes.get('largeop')) {\n                var u = this.em(this.getCenterOffset());\n                if (u !== '0') {\n                    this.adaptor.setStyle(chtml, 'verticalAlign', u);\n                }\n            }\n            if (this.node.getProperty('mathaccent')) {\n                this.adaptor.setStyle(chtml, 'width', '0');\n                this.adaptor.setStyle(chtml, 'margin-left', this.em(this.getAccentOffset()));\n            }\n            try {\n                for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    child.toCHTML(chtml);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n    };\n    CHTMLmo.prototype.stretchHTML = function (chtml) {\n        var c = this.getText().codePointAt(0);\n        this.font.delimUsage.add(c);\n        this.childNodes[0].markUsed();\n        var delim = this.stretch;\n        var stretch = delim.stretch;\n        var content = [];\n        if (stretch[0]) {\n            content.push(this.html('mjx-beg', {}, [this.html('mjx-c')]));\n        }\n        content.push(this.html('mjx-ext', {}, [this.html('mjx-c')]));\n        if (stretch.length === 4) {\n            content.push(this.html('mjx-mid', {}, [this.html('mjx-c')]), this.html('mjx-ext', {}, [this.html('mjx-c')]));\n        }\n        if (stretch[2]) {\n            content.push(this.html('mjx-end', {}, [this.html('mjx-c')]));\n        }\n        var styles = {};\n        var _a = this.bbox, h = _a.h, d = _a.d, w = _a.w;\n        if (delim.dir === 1) {\n            content.push(this.html('mjx-mark'));\n            styles.height = this.em(h + d);\n            styles.verticalAlign = this.em(-d);\n        }\n        else {\n            styles.width = this.em(w);\n        }\n        var dir = mo_js_1.DirectionVH[delim.dir];\n        var properties = { class: this.char(delim.c || c), style: styles };\n        var html = this.html('mjx-stretchy-' + dir, properties, content);\n        this.adaptor.append(chtml, html);\n    };\n    CHTMLmo.kind = mo_js_2.MmlMo.prototype.kind;\n    CHTMLmo.styles = {\n        'mjx-stretchy-h': {\n            display: 'inline-table',\n            width: '100%'\n        },\n        'mjx-stretchy-h > *': {\n            display: 'table-cell',\n            width: 0\n        },\n        'mjx-stretchy-h > * > mjx-c': {\n            display: 'inline-block',\n            transform: 'scalex(1.0000001)'\n        },\n        'mjx-stretchy-h > * > mjx-c::before': {\n            display: 'inline-block',\n            width: 'initial'\n        },\n        'mjx-stretchy-h > mjx-ext': {\n            '/* IE */ overflow': 'hidden',\n            '/* others */ overflow': 'clip visible',\n            width: '100%'\n        },\n        'mjx-stretchy-h > mjx-ext > mjx-c::before': {\n            transform: 'scalex(500)'\n        },\n        'mjx-stretchy-h > mjx-ext > mjx-c': {\n            width: 0\n        },\n        'mjx-stretchy-h > mjx-beg > mjx-c': {\n            'margin-right': '-.1em'\n        },\n        'mjx-stretchy-h > mjx-end > mjx-c': {\n            'margin-left': '-.1em'\n        },\n        'mjx-stretchy-v': {\n            display: 'inline-block'\n        },\n        'mjx-stretchy-v > *': {\n            display: 'block'\n        },\n        'mjx-stretchy-v > mjx-beg': {\n            height: 0\n        },\n        'mjx-stretchy-v > mjx-end > mjx-c': {\n            display: 'block'\n        },\n        'mjx-stretchy-v > * > mjx-c': {\n            transform: 'scaley(1.0000001)',\n            'transform-origin': 'left center',\n            overflow: 'hidden'\n        },\n        'mjx-stretchy-v > mjx-ext': {\n            display: 'block',\n            height: '100%',\n            'box-sizing': 'border-box',\n            border: '0px solid transparent',\n            '/* IE */ overflow': 'hidden',\n            '/* others */ overflow': 'visible clip',\n        },\n        'mjx-stretchy-v > mjx-ext > mjx-c::before': {\n            width: 'initial',\n            'box-sizing': 'border-box'\n        },\n        'mjx-stretchy-v > mjx-ext > mjx-c': {\n            transform: 'scaleY(500) translateY(.075em)',\n            overflow: 'visible'\n        },\n        'mjx-mark': {\n            display: 'inline-block',\n            height: '0px'\n        }\n    };\n    return CHTMLmo;\n}((0, mo_js_1.CommonMoMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmo = CHTMLmo;\n//# sourceMappingURL=mo.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmpadded = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mpadded_js_1 = require(\"../../common/Wrappers/mpadded.js\");\nvar mpadded_js_2 = require(\"../../../core/MmlTree/MmlNodes/mpadded.js\");\nvar CHTMLmpadded = (function (_super) {\n    __extends(CHTMLmpadded, _super);\n    function CHTMLmpadded() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmpadded.prototype.toCHTML = function (parent) {\n        var e_1, _a;\n        var chtml = this.standardCHTMLnode(parent);\n        var content = [];\n        var style = {};\n        var _b = __read(this.getDimens(), 9), W = _b[2], dh = _b[3], dd = _b[4], dw = _b[5], x = _b[6], y = _b[7], dx = _b[8];\n        if (dw) {\n            style.width = this.em(W + dw);\n        }\n        if (dh || dd) {\n            style.margin = this.em(dh) + ' 0 ' + this.em(dd);\n        }\n        if (x + dx || y) {\n            style.position = 'relative';\n            var rbox = this.html('mjx-rbox', {\n                style: { left: this.em(x + dx), top: this.em(-y), 'max-width': style.width }\n            });\n            if (x + dx && this.childNodes[0].getBBox().pwidth) {\n                this.adaptor.setAttribute(rbox, 'width', 'full');\n                this.adaptor.setStyle(rbox, 'left', this.em(x));\n            }\n            content.push(rbox);\n        }\n        chtml = this.adaptor.append(chtml, this.html('mjx-block', { style: style }, content));\n        try {\n            for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var child = _d.value;\n                child.toCHTML(content[0] || chtml);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    CHTMLmpadded.kind = mpadded_js_2.MmlMpadded.prototype.kind;\n    CHTMLmpadded.styles = {\n        'mjx-mpadded': {\n            display: 'inline-block'\n        },\n        'mjx-rbox': {\n            display: 'inline-block',\n            position: 'relative'\n        }\n    };\n    return CHTMLmpadded;\n}((0, mpadded_js_1.CommonMpaddedMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmpadded = CHTMLmpadded;\n//# sourceMappingURL=mpadded.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmroot = void 0;\nvar msqrt_js_1 = require(\"./msqrt.js\");\nvar mroot_js_1 = require(\"../../common/Wrappers/mroot.js\");\nvar mroot_js_2 = require(\"../../../core/MmlTree/MmlNodes/mroot.js\");\nvar CHTMLmroot = (function (_super) {\n    __extends(CHTMLmroot, _super);\n    function CHTMLmroot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmroot.prototype.addRoot = function (ROOT, root, sbox, H) {\n        root.toCHTML(ROOT);\n        var _a = __read(this.getRootDimens(sbox, H), 3), x = _a[0], h = _a[1], dx = _a[2];\n        this.adaptor.setStyle(ROOT, 'verticalAlign', this.em(h));\n        this.adaptor.setStyle(ROOT, 'width', this.em(x));\n        if (dx) {\n            this.adaptor.setStyle(this.adaptor.firstChild(ROOT), 'paddingLeft', this.em(dx));\n        }\n    };\n    CHTMLmroot.kind = mroot_js_2.MmlMroot.prototype.kind;\n    return CHTMLmroot;\n}((0, mroot_js_1.CommonMrootMixin)(msqrt_js_1.CHTMLmsqrt)));\nexports.CHTMLmroot = CHTMLmroot;\n//# sourceMappingURL=mroot.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLinferredMrow = exports.CHTMLmrow = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mrow_js_1 = require(\"../../common/Wrappers/mrow.js\");\nvar mrow_js_2 = require(\"../../common/Wrappers/mrow.js\");\nvar mrow_js_3 = require(\"../../../core/MmlTree/MmlNodes/mrow.js\");\nvar CHTMLmrow = (function (_super) {\n    __extends(CHTMLmrow, _super);\n    function CHTMLmrow() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmrow.prototype.toCHTML = function (parent) {\n        var e_1, _a;\n        var chtml = (this.node.isInferred ? (this.chtml = parent) : this.standardCHTMLnode(parent));\n        var hasNegative = false;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.toCHTML(chtml);\n                if (child.bbox.w < 0) {\n                    hasNegative = true;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (hasNegative) {\n            var w = this.getBBox().w;\n            if (w) {\n                this.adaptor.setStyle(chtml, 'width', this.em(Math.max(0, w)));\n                if (w < 0) {\n                    this.adaptor.setStyle(chtml, 'marginRight', this.em(w));\n                }\n            }\n        }\n    };\n    CHTMLmrow.kind = mrow_js_3.MmlMrow.prototype.kind;\n    return CHTMLmrow;\n}((0, mrow_js_1.CommonMrowMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmrow = CHTMLmrow;\nvar CHTMLinferredMrow = (function (_super) {\n    __extends(CHTMLinferredMrow, _super);\n    function CHTMLinferredMrow() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLinferredMrow.kind = mrow_js_3.MmlInferredMrow.prototype.kind;\n    return CHTMLinferredMrow;\n}((0, mrow_js_2.CommonInferredMrowMixin)(CHTMLmrow)));\nexports.CHTMLinferredMrow = CHTMLinferredMrow;\n//# sourceMappingURL=mrow.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLms = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar ms_js_1 = require(\"../../common/Wrappers/ms.js\");\nvar ms_js_2 = require(\"../../../core/MmlTree/MmlNodes/ms.js\");\nvar CHTMLms = (function (_super) {\n    __extends(CHTMLms, _super);\n    function CHTMLms() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLms.kind = ms_js_2.MmlMs.prototype.kind;\n    return CHTMLms;\n}((0, ms_js_1.CommonMsMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLms = CHTMLms;\n//# sourceMappingURL=ms.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmspace = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mspace_js_1 = require(\"../../common/Wrappers/mspace.js\");\nvar mspace_js_2 = require(\"../../../core/MmlTree/MmlNodes/mspace.js\");\nvar CHTMLmspace = (function (_super) {\n    __extends(CHTMLmspace, _super);\n    function CHTMLmspace() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmspace.prototype.toCHTML = function (parent) {\n        var chtml = this.standardCHTMLnode(parent);\n        var _a = this.getBBox(), w = _a.w, h = _a.h, d = _a.d;\n        if (w < 0) {\n            this.adaptor.setStyle(chtml, 'marginRight', this.em(w));\n            w = 0;\n        }\n        if (w) {\n            this.adaptor.setStyle(chtml, 'width', this.em(w));\n        }\n        h = Math.max(0, h + d);\n        if (h) {\n            this.adaptor.setStyle(chtml, 'height', this.em(Math.max(0, h)));\n        }\n        if (d) {\n            this.adaptor.setStyle(chtml, 'verticalAlign', this.em(-d));\n        }\n    };\n    CHTMLmspace.kind = mspace_js_2.MmlMspace.prototype.kind;\n    return CHTMLmspace;\n}((0, mspace_js_1.CommonMspaceMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmspace = CHTMLmspace;\n//# sourceMappingURL=mspace.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmsqrt = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar msqrt_js_1 = require(\"../../common/Wrappers/msqrt.js\");\nvar msqrt_js_2 = require(\"../../../core/MmlTree/MmlNodes/msqrt.js\");\nvar CHTMLmsqrt = (function (_super) {\n    __extends(CHTMLmsqrt, _super);\n    function CHTMLmsqrt() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmsqrt.prototype.toCHTML = function (parent) {\n        var surd = this.childNodes[this.surd];\n        var base = this.childNodes[this.base];\n        var sbox = surd.getBBox();\n        var bbox = base.getOuterBBox();\n        var _a = __read(this.getPQ(sbox), 2), q = _a[1];\n        var t = this.font.params.rule_thickness;\n        var H = bbox.h + q + t;\n        var CHTML = this.standardCHTMLnode(parent);\n        var SURD, BASE, ROOT, root;\n        if (this.root != null) {\n            ROOT = this.adaptor.append(CHTML, this.html('mjx-root'));\n            root = this.childNodes[this.root];\n        }\n        var SQRT = this.adaptor.append(CHTML, this.html('mjx-sqrt', {}, [\n            SURD = this.html('mjx-surd'),\n            BASE = this.html('mjx-box', { style: { paddingTop: this.em(q) } })\n        ]));\n        this.addRoot(ROOT, root, sbox, H);\n        surd.toCHTML(SURD);\n        base.toCHTML(BASE);\n        if (surd.size < 0) {\n            this.adaptor.addClass(SQRT, 'mjx-tall');\n        }\n    };\n    CHTMLmsqrt.prototype.addRoot = function (_ROOT, _root, _sbox, _H) {\n    };\n    CHTMLmsqrt.kind = msqrt_js_2.MmlMsqrt.prototype.kind;\n    CHTMLmsqrt.styles = {\n        'mjx-root': {\n            display: 'inline-block',\n            'white-space': 'nowrap'\n        },\n        'mjx-surd': {\n            display: 'inline-block',\n            'vertical-align': 'top'\n        },\n        'mjx-sqrt': {\n            display: 'inline-block',\n            'padding-top': '.07em'\n        },\n        'mjx-sqrt > mjx-box': {\n            'border-top': '.07em solid'\n        },\n        'mjx-sqrt.mjx-tall > mjx-box': {\n            'padding-left': '.3em',\n            'margin-left': '-.3em'\n        }\n    };\n    return CHTMLmsqrt;\n}((0, msqrt_js_1.CommonMsqrtMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmsqrt = CHTMLmsqrt;\n//# sourceMappingURL=msqrt.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmsubsup = exports.CHTMLmsup = exports.CHTMLmsub = void 0;\nvar scriptbase_js_1 = require(\"./scriptbase.js\");\nvar msubsup_js_1 = require(\"../../common/Wrappers/msubsup.js\");\nvar msubsup_js_2 = require(\"../../common/Wrappers/msubsup.js\");\nvar msubsup_js_3 = require(\"../../common/Wrappers/msubsup.js\");\nvar msubsup_js_4 = require(\"../../../core/MmlTree/MmlNodes/msubsup.js\");\nvar CHTMLmsub = (function (_super) {\n    __extends(CHTMLmsub, _super);\n    function CHTMLmsub() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmsub.kind = msubsup_js_4.MmlMsub.prototype.kind;\n    return CHTMLmsub;\n}((0, msubsup_js_1.CommonMsubMixin)(scriptbase_js_1.CHTMLscriptbase)));\nexports.CHTMLmsub = CHTMLmsub;\nvar CHTMLmsup = (function (_super) {\n    __extends(CHTMLmsup, _super);\n    function CHTMLmsup() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmsup.kind = msubsup_js_4.MmlMsup.prototype.kind;\n    return CHTMLmsup;\n}((0, msubsup_js_2.CommonMsupMixin)(scriptbase_js_1.CHTMLscriptbase)));\nexports.CHTMLmsup = CHTMLmsup;\nvar CHTMLmsubsup = (function (_super) {\n    __extends(CHTMLmsubsup, _super);\n    function CHTMLmsubsup() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmsubsup.prototype.toCHTML = function (parent) {\n        var adaptor = this.adaptor;\n        var chtml = this.standardCHTMLnode(parent);\n        var _a = __read([this.baseChild, this.supChild, this.subChild], 3), base = _a[0], sup = _a[1], sub = _a[2];\n        var _b = __read(this.getUVQ(), 3), v = _b[1], q = _b[2];\n        var style = { 'vertical-align': this.em(v) };\n        base.toCHTML(chtml);\n        var stack = adaptor.append(chtml, this.html('mjx-script', { style: style }));\n        sup.toCHTML(stack);\n        adaptor.append(stack, this.html('mjx-spacer', { style: { 'margin-top': this.em(q) } }));\n        sub.toCHTML(stack);\n        var ic = this.getAdjustedIc();\n        if (ic) {\n            adaptor.setStyle(sup.chtml, 'marginLeft', this.em(ic / sup.bbox.rscale));\n        }\n        if (this.baseRemoveIc) {\n            adaptor.setStyle(stack, 'marginLeft', this.em(-this.baseIc));\n        }\n    };\n    CHTMLmsubsup.kind = msubsup_js_4.MmlMsubsup.prototype.kind;\n    CHTMLmsubsup.styles = {\n        'mjx-script': {\n            display: 'inline-block',\n            'padding-right': '.05em',\n            'padding-left': '.033em'\n        },\n        'mjx-script > mjx-spacer': {\n            display: 'block'\n        }\n    };\n    return CHTMLmsubsup;\n}((0, msubsup_js_3.CommonMsubsupMixin)(scriptbase_js_1.CHTMLscriptbase)));\nexports.CHTMLmsubsup = CHTMLmsubsup;\n//# sourceMappingURL=msubsup.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmtable = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mtable_js_1 = require(\"../../common/Wrappers/mtable.js\");\nvar mtable_js_2 = require(\"../../../core/MmlTree/MmlNodes/mtable.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar CHTMLmtable = (function (_super) {\n    __extends(CHTMLmtable, _super);\n    function CHTMLmtable(factory, node, parent) {\n        if (parent === void 0) { parent = null; }\n        var _this = _super.call(this, factory, node, parent) || this;\n        _this.itable = _this.html('mjx-itable');\n        _this.labels = _this.html('mjx-itable');\n        return _this;\n    }\n    CHTMLmtable.prototype.getAlignShift = function () {\n        var data = _super.prototype.getAlignShift.call(this);\n        if (!this.isTop) {\n            data[1] = 0;\n        }\n        return data;\n    };\n    CHTMLmtable.prototype.toCHTML = function (parent) {\n        var e_1, _a;\n        var chtml = this.standardCHTMLnode(parent);\n        this.adaptor.append(chtml, this.html('mjx-table', {}, [this.itable]));\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.toCHTML(this.itable);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.padRows();\n        this.handleColumnSpacing();\n        this.handleColumnLines();\n        this.handleColumnWidths();\n        this.handleRowSpacing();\n        this.handleRowLines();\n        this.handleRowHeights();\n        this.handleFrame();\n        this.handleWidth();\n        this.handleLabels();\n        this.handleAlign();\n        this.handleJustify();\n        this.shiftColor();\n    };\n    CHTMLmtable.prototype.shiftColor = function () {\n        var adaptor = this.adaptor;\n        var color = adaptor.getStyle(this.chtml, 'backgroundColor');\n        if (color) {\n            adaptor.setStyle(this.chtml, 'backgroundColor', '');\n            adaptor.setStyle(this.itable, 'backgroundColor', color);\n        }\n    };\n    CHTMLmtable.prototype.padRows = function () {\n        var e_2, _a;\n        var adaptor = this.adaptor;\n        try {\n            for (var _b = __values(adaptor.childNodes(this.itable)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var row = _c.value;\n                while (adaptor.childNodes(row).length < this.numCols) {\n                    adaptor.append(row, this.html('mjx-mtd', { 'extra': true }));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    CHTMLmtable.prototype.handleColumnSpacing = function () {\n        var e_3, _a, e_4, _b;\n        var scale = (this.childNodes[0] ? 1 / this.childNodes[0].getBBox().rscale : 1);\n        var spacing = this.getEmHalfSpacing(this.fSpace[0], this.cSpace, scale);\n        var frame = this.frame;\n        try {\n            for (var _c = __values(this.tableRows), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var row = _d.value;\n                var i = 0;\n                try {\n                    for (var _e = (e_4 = void 0, __values(row.tableCells)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var cell = _f.value;\n                        var lspace = spacing[i++];\n                        var rspace = spacing[i];\n                        var styleNode = (cell ? cell.chtml : this.adaptor.childNodes(row.chtml)[i]);\n                        if ((i > 1 && lspace !== '0.4em') || (frame && i === 1)) {\n                            this.adaptor.setStyle(styleNode, 'paddingLeft', lspace);\n                        }\n                        if ((i < this.numCols && rspace !== '0.4em') || (frame && i === this.numCols)) {\n                            this.adaptor.setStyle(styleNode, 'paddingRight', rspace);\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    CHTMLmtable.prototype.handleColumnLines = function () {\n        var e_5, _a, e_6, _b;\n        if (this.node.attributes.get('columnlines') === 'none')\n            return;\n        var lines = this.getColumnAttributes('columnlines');\n        try {\n            for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var row = _d.value;\n                var i = 0;\n                try {\n                    for (var _e = (e_6 = void 0, __values(this.adaptor.childNodes(row.chtml).slice(1))), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var cell = _f.value;\n                        var line = lines[i++];\n                        if (line === 'none')\n                            continue;\n                        this.adaptor.setStyle(cell, 'borderLeft', '.07em ' + line);\n                    }\n                }\n                catch (e_6_1) { e_6 = { error: e_6_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_6) throw e_6.error; }\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n    };\n    CHTMLmtable.prototype.handleColumnWidths = function () {\n        var e_7, _a, e_8, _b;\n        try {\n            for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var row = _d.value;\n                var i = 0;\n                try {\n                    for (var _e = (e_8 = void 0, __values(this.adaptor.childNodes(row.chtml))), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var cell = _f.value;\n                        var w = this.cWidths[i++];\n                        if (w !== null) {\n                            var width = (typeof w === 'number' ? this.em(w) : w);\n                            this.adaptor.setStyle(cell, 'width', width);\n                            this.adaptor.setStyle(cell, 'maxWidth', width);\n                            this.adaptor.setStyle(cell, 'minWidth', width);\n                        }\n                    }\n                }\n                catch (e_8_1) { e_8 = { error: e_8_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_8) throw e_8.error; }\n                }\n            }\n        }\n        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_7) throw e_7.error; }\n        }\n    };\n    CHTMLmtable.prototype.handleRowSpacing = function () {\n        var e_9, _a, e_10, _b;\n        var scale = (this.childNodes[0] ? 1 / this.childNodes[0].getBBox().rscale : 1);\n        var spacing = this.getEmHalfSpacing(this.fSpace[1], this.rSpace, scale);\n        var frame = this.frame;\n        var i = 0;\n        try {\n            for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var row = _d.value;\n                var tspace = spacing[i++];\n                var bspace = spacing[i];\n                try {\n                    for (var _e = (e_10 = void 0, __values(row.childNodes)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var cell = _f.value;\n                        if ((i > 1 && tspace !== '0.215em') || (frame && i === 1)) {\n                            this.adaptor.setStyle(cell.chtml, 'paddingTop', tspace);\n                        }\n                        if ((i < this.numRows && bspace !== '0.215em') || (frame && i === this.numRows)) {\n                            this.adaptor.setStyle(cell.chtml, 'paddingBottom', bspace);\n                        }\n                    }\n                }\n                catch (e_10_1) { e_10 = { error: e_10_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_10) throw e_10.error; }\n                }\n            }\n        }\n        catch (e_9_1) { e_9 = { error: e_9_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_9) throw e_9.error; }\n        }\n    };\n    CHTMLmtable.prototype.handleRowLines = function () {\n        var e_11, _a, e_12, _b;\n        if (this.node.attributes.get('rowlines') === 'none')\n            return;\n        var lines = this.getRowAttributes('rowlines');\n        var i = 0;\n        try {\n            for (var _c = __values(this.childNodes.slice(1)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var row = _d.value;\n                var line = lines[i++];\n                if (line === 'none')\n                    continue;\n                try {\n                    for (var _e = (e_12 = void 0, __values(this.adaptor.childNodes(row.chtml))), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var cell = _f.value;\n                        this.adaptor.setStyle(cell, 'borderTop', '.07em ' + line);\n                    }\n                }\n                catch (e_12_1) { e_12 = { error: e_12_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_12) throw e_12.error; }\n                }\n            }\n        }\n        catch (e_11_1) { e_11 = { error: e_11_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_11) throw e_11.error; }\n        }\n    };\n    CHTMLmtable.prototype.handleRowHeights = function () {\n        if (this.node.attributes.get('equalrows')) {\n            this.handleEqualRows();\n        }\n    };\n    CHTMLmtable.prototype.handleEqualRows = function () {\n        var space = this.getRowHalfSpacing();\n        var _a = this.getTableData(), H = _a.H, D = _a.D, NH = _a.NH, ND = _a.ND;\n        var HD = this.getEqualRowHeight();\n        for (var i = 0; i < this.numRows; i++) {\n            var row = this.childNodes[i];\n            this.setRowHeight(row, HD + space[i] + space[i + 1] + this.rLines[i]);\n            if (HD !== NH[i] + ND[i]) {\n                this.setRowBaseline(row, HD, (HD - H[i] + D[i]) / 2);\n            }\n        }\n    };\n    CHTMLmtable.prototype.setRowHeight = function (row, HD) {\n        this.adaptor.setStyle(row.chtml, 'height', this.em(HD));\n    };\n    CHTMLmtable.prototype.setRowBaseline = function (row, HD, D) {\n        var e_13, _a;\n        var ralign = row.node.attributes.get('rowalign');\n        try {\n            for (var _b = __values(row.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var cell = _c.value;\n                if (this.setCellBaseline(cell, ralign, HD, D))\n                    break;\n            }\n        }\n        catch (e_13_1) { e_13 = { error: e_13_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_13) throw e_13.error; }\n        }\n    };\n    CHTMLmtable.prototype.setCellBaseline = function (cell, ralign, HD, D) {\n        var calign = cell.node.attributes.get('rowalign');\n        if (calign === 'baseline' || calign === 'axis') {\n            var adaptor = this.adaptor;\n            var child = adaptor.lastChild(cell.chtml);\n            adaptor.setStyle(child, 'height', this.em(HD));\n            adaptor.setStyle(child, 'verticalAlign', this.em(-D));\n            var row = cell.parent;\n            if ((!row.node.isKind('mlabeledtr') || cell !== row.childNodes[0]) &&\n                (ralign === 'baseline' || ralign === 'axis')) {\n                return true;\n            }\n        }\n        return false;\n    };\n    CHTMLmtable.prototype.handleFrame = function () {\n        if (this.frame && this.fLine) {\n            this.adaptor.setStyle(this.itable, 'border', '.07em ' + this.node.attributes.get('frame'));\n        }\n    };\n    CHTMLmtable.prototype.handleWidth = function () {\n        var adaptor = this.adaptor;\n        var _a = this.getBBox(), w = _a.w, L = _a.L, R = _a.R;\n        adaptor.setStyle(this.chtml, 'minWidth', this.em(L + w + R));\n        var W = this.node.attributes.get('width');\n        if ((0, string_js_1.isPercent)(W)) {\n            adaptor.setStyle(this.chtml, 'width', '');\n            adaptor.setAttribute(this.chtml, 'width', 'full');\n        }\n        else if (!this.hasLabels) {\n            if (W === 'auto')\n                return;\n            W = this.em(this.length2em(W) + 2 * this.fLine);\n        }\n        var table = adaptor.firstChild(this.chtml);\n        adaptor.setStyle(table, 'width', W);\n        adaptor.setStyle(table, 'minWidth', this.em(w));\n        if (L || R) {\n            adaptor.setStyle(this.chtml, 'margin', '');\n            var style = (this.node.attributes.get('data-width-includes-label') ? 'padding' : 'margin');\n            if (L === R) {\n                adaptor.setStyle(table, style, '0 ' + this.em(R));\n            }\n            else {\n                adaptor.setStyle(table, style, '0 ' + this.em(R) + ' 0 ' + this.em(L));\n            }\n        }\n        adaptor.setAttribute(this.itable, 'width', 'full');\n    };\n    CHTMLmtable.prototype.handleAlign = function () {\n        var _a = __read(this.getAlignmentRow(), 2), align = _a[0], row = _a[1];\n        if (row === null) {\n            if (align !== 'axis') {\n                this.adaptor.setAttribute(this.chtml, 'align', align);\n            }\n        }\n        else {\n            var y = this.getVerticalPosition(row, align);\n            this.adaptor.setAttribute(this.chtml, 'align', 'top');\n            this.adaptor.setStyle(this.chtml, 'verticalAlign', this.em(y));\n        }\n    };\n    CHTMLmtable.prototype.handleJustify = function () {\n        var align = this.getAlignShift()[0];\n        if (align !== 'center') {\n            this.adaptor.setAttribute(this.chtml, 'justify', align);\n        }\n    };\n    CHTMLmtable.prototype.handleLabels = function () {\n        if (!this.hasLabels)\n            return;\n        var labels = this.labels;\n        var attributes = this.node.attributes;\n        var adaptor = this.adaptor;\n        var side = attributes.get('side');\n        adaptor.setAttribute(this.chtml, 'side', side);\n        adaptor.setAttribute(labels, 'align', side);\n        adaptor.setStyle(labels, side, '0');\n        var _a = __read(this.addLabelPadding(side), 2), align = _a[0], shift = _a[1];\n        if (shift) {\n            var table = adaptor.firstChild(this.chtml);\n            this.setIndent(table, align, shift);\n        }\n        this.updateRowHeights();\n        this.addLabelSpacing();\n    };\n    CHTMLmtable.prototype.addLabelPadding = function (side) {\n        var _a = __read(this.getPadAlignShift(side), 3), align = _a[1], shift = _a[2];\n        var styles = {};\n        if (side === 'right' && !this.node.attributes.get('data-width-includes-label')) {\n            var W = this.node.attributes.get('width');\n            var _b = this.getBBox(), w = _b.w, L = _b.L, R = _b.R;\n            styles.style = {\n                width: ((0, string_js_1.isPercent)(W) ? 'calc(' + W + ' + ' + this.em(L + R) + ')' : this.em(L + w + R))\n            };\n        }\n        this.adaptor.append(this.chtml, this.html('mjx-labels', styles, [this.labels]));\n        return [align, shift];\n    };\n    CHTMLmtable.prototype.updateRowHeights = function () {\n        var _a = this.getTableData(), H = _a.H, D = _a.D, NH = _a.NH, ND = _a.ND;\n        var space = this.getRowHalfSpacing();\n        for (var i = 0; i < this.numRows; i++) {\n            var row = this.childNodes[i];\n            this.setRowHeight(row, H[i] + D[i] + space[i] + space[i + 1] + this.rLines[i]);\n            if (H[i] !== NH[i] || D[i] !== ND[i]) {\n                this.setRowBaseline(row, H[i] + D[i], D[i]);\n            }\n            else if (row.node.isKind('mlabeledtr')) {\n                this.setCellBaseline(row.childNodes[0], '', H[i] + D[i], D[i]);\n            }\n        }\n    };\n    CHTMLmtable.prototype.addLabelSpacing = function () {\n        var adaptor = this.adaptor;\n        var equal = this.node.attributes.get('equalrows');\n        var _a = this.getTableData(), H = _a.H, D = _a.D;\n        var HD = (equal ? this.getEqualRowHeight() : 0);\n        var space = this.getRowHalfSpacing();\n        var h = this.fLine;\n        var current = adaptor.firstChild(this.labels);\n        for (var i = 0; i < this.numRows; i++) {\n            var row = this.childNodes[i];\n            if (row.node.isKind('mlabeledtr')) {\n                h && adaptor.insert(this.html('mjx-mtr', { style: { height: this.em(h) } }), current);\n                adaptor.setStyle(current, 'height', this.em((equal ? HD : H[i] + D[i]) + space[i] + space[i + 1]));\n                current = adaptor.next(current);\n                h = this.rLines[i];\n            }\n            else {\n                h += space[i] + (equal ? HD : H[i] + D[i]) + space[i + 1] + this.rLines[i];\n            }\n        }\n    };\n    CHTMLmtable.kind = mtable_js_2.MmlMtable.prototype.kind;\n    CHTMLmtable.styles = {\n        'mjx-mtable': {\n            'vertical-align': '.25em',\n            'text-align': 'center',\n            'position': 'relative',\n            'box-sizing': 'border-box',\n            'border-spacing': 0,\n            'border-collapse': 'collapse'\n        },\n        'mjx-mstyle[size=\"s\"] mjx-mtable': {\n            'vertical-align': '.354em'\n        },\n        'mjx-labels': {\n            position: 'absolute',\n            left: 0,\n            top: 0\n        },\n        'mjx-table': {\n            'display': 'inline-block',\n            'vertical-align': '-.5ex',\n            'box-sizing': 'border-box'\n        },\n        'mjx-table > mjx-itable': {\n            'vertical-align': 'middle',\n            'text-align': 'left',\n            'box-sizing': 'border-box'\n        },\n        'mjx-labels > mjx-itable': {\n            position: 'absolute',\n            top: 0\n        },\n        'mjx-mtable[justify=\"left\"]': {\n            'text-align': 'left'\n        },\n        'mjx-mtable[justify=\"right\"]': {\n            'text-align': 'right'\n        },\n        'mjx-mtable[justify=\"left\"][side=\"left\"]': {\n            'padding-right': '0 ! important'\n        },\n        'mjx-mtable[justify=\"left\"][side=\"right\"]': {\n            'padding-left': '0 ! important'\n        },\n        'mjx-mtable[justify=\"right\"][side=\"left\"]': {\n            'padding-right': '0 ! important'\n        },\n        'mjx-mtable[justify=\"right\"][side=\"right\"]': {\n            'padding-left': '0 ! important'\n        },\n        'mjx-mtable[align]': {\n            'vertical-align': 'baseline'\n        },\n        'mjx-mtable[align=\"top\"] > mjx-table': {\n            'vertical-align': 'top'\n        },\n        'mjx-mtable[align=\"bottom\"] > mjx-table': {\n            'vertical-align': 'bottom'\n        },\n        'mjx-mtable[side=\"right\"] mjx-labels': {\n            'min-width': '100%'\n        }\n    };\n    return CHTMLmtable;\n}((0, mtable_js_1.CommonMtableMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmtable = CHTMLmtable;\n//# sourceMappingURL=mtable.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmtd = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mtd_js_1 = require(\"../../common/Wrappers/mtd.js\");\nvar mtd_js_2 = require(\"../../../core/MmlTree/MmlNodes/mtd.js\");\nvar CHTMLmtd = (function (_super) {\n    __extends(CHTMLmtd, _super);\n    function CHTMLmtd() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmtd.prototype.toCHTML = function (parent) {\n        _super.prototype.toCHTML.call(this, parent);\n        var ralign = this.node.attributes.get('rowalign');\n        var calign = this.node.attributes.get('columnalign');\n        var palign = this.parent.node.attributes.get('rowalign');\n        if (ralign !== palign) {\n            this.adaptor.setAttribute(this.chtml, 'rowalign', ralign);\n        }\n        if (calign !== 'center' &&\n            (this.parent.kind !== 'mlabeledtr' || this !== this.parent.childNodes[0] ||\n                calign !== this.parent.parent.node.attributes.get('side'))) {\n            this.adaptor.setStyle(this.chtml, 'textAlign', calign);\n        }\n        if (this.parent.parent.node.getProperty('useHeight')) {\n            this.adaptor.append(this.chtml, this.html('mjx-tstrut'));\n        }\n    };\n    CHTMLmtd.kind = mtd_js_2.MmlMtd.prototype.kind;\n    CHTMLmtd.styles = {\n        'mjx-mtd': {\n            display: 'table-cell',\n            'text-align': 'center',\n            'padding': '.215em .4em'\n        },\n        'mjx-mtd:first-child': {\n            'padding-left': 0\n        },\n        'mjx-mtd:last-child': {\n            'padding-right': 0\n        },\n        'mjx-mtable > * > mjx-itable > *:first-child > mjx-mtd': {\n            'padding-top': 0\n        },\n        'mjx-mtable > * > mjx-itable > *:last-child > mjx-mtd': {\n            'padding-bottom': 0\n        },\n        'mjx-tstrut': {\n            display: 'inline-block',\n            height: '1em',\n            'vertical-align': '-.25em'\n        },\n        'mjx-labels[align=\"left\"] > mjx-mtr > mjx-mtd': {\n            'text-align': 'left'\n        },\n        'mjx-labels[align=\"right\"] > mjx-mtr > mjx-mtd': {\n            'text-align': 'right'\n        },\n        'mjx-mtd[extra]': {\n            padding: 0\n        },\n        'mjx-mtd[rowalign=\"top\"]': {\n            'vertical-align': 'top'\n        },\n        'mjx-mtd[rowalign=\"center\"]': {\n            'vertical-align': 'middle'\n        },\n        'mjx-mtd[rowalign=\"bottom\"]': {\n            'vertical-align': 'bottom'\n        },\n        'mjx-mtd[rowalign=\"baseline\"]': {\n            'vertical-align': 'baseline'\n        },\n        'mjx-mtd[rowalign=\"axis\"]': {\n            'vertical-align': '.25em'\n        }\n    };\n    return CHTMLmtd;\n}((0, mtd_js_1.CommonMtdMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmtd = CHTMLmtd;\n//# sourceMappingURL=mtd.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmtext = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mtext_js_1 = require(\"../../common/Wrappers/mtext.js\");\nvar mtext_js_2 = require(\"../../../core/MmlTree/MmlNodes/mtext.js\");\nvar CHTMLmtext = (function (_super) {\n    __extends(CHTMLmtext, _super);\n    function CHTMLmtext() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmtext.kind = mtext_js_2.MmlMtext.prototype.kind;\n    return CHTMLmtext;\n}((0, mtext_js_1.CommonMtextMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmtext = CHTMLmtext;\n//# sourceMappingURL=mtext.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmlabeledtr = exports.CHTMLmtr = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar mtr_js_1 = require(\"../../common/Wrappers/mtr.js\");\nvar mtr_js_2 = require(\"../../common/Wrappers/mtr.js\");\nvar mtr_js_3 = require(\"../../../core/MmlTree/MmlNodes/mtr.js\");\nvar CHTMLmtr = (function (_super) {\n    __extends(CHTMLmtr, _super);\n    function CHTMLmtr() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmtr.prototype.toCHTML = function (parent) {\n        _super.prototype.toCHTML.call(this, parent);\n        var align = this.node.attributes.get('rowalign');\n        if (align !== 'baseline') {\n            this.adaptor.setAttribute(this.chtml, 'rowalign', align);\n        }\n    };\n    CHTMLmtr.kind = mtr_js_3.MmlMtr.prototype.kind;\n    CHTMLmtr.styles = {\n        'mjx-mtr': {\n            display: 'table-row',\n        },\n        'mjx-mtr[rowalign=\"top\"] > mjx-mtd': {\n            'vertical-align': 'top'\n        },\n        'mjx-mtr[rowalign=\"center\"] > mjx-mtd': {\n            'vertical-align': 'middle'\n        },\n        'mjx-mtr[rowalign=\"bottom\"] > mjx-mtd': {\n            'vertical-align': 'bottom'\n        },\n        'mjx-mtr[rowalign=\"baseline\"] > mjx-mtd': {\n            'vertical-align': 'baseline'\n        },\n        'mjx-mtr[rowalign=\"axis\"] > mjx-mtd': {\n            'vertical-align': '.25em'\n        }\n    };\n    return CHTMLmtr;\n}((0, mtr_js_1.CommonMtrMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLmtr = CHTMLmtr;\nvar CHTMLmlabeledtr = (function (_super) {\n    __extends(CHTMLmlabeledtr, _super);\n    function CHTMLmlabeledtr() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmlabeledtr.prototype.toCHTML = function (parent) {\n        _super.prototype.toCHTML.call(this, parent);\n        var child = this.adaptor.firstChild(this.chtml);\n        if (child) {\n            this.adaptor.remove(child);\n            var align = this.node.attributes.get('rowalign');\n            var attr = (align !== 'baseline' && align !== 'axis' ? { rowalign: align } : {});\n            var row = this.html('mjx-mtr', attr, [child]);\n            this.adaptor.append(this.parent.labels, row);\n        }\n    };\n    CHTMLmlabeledtr.prototype.markUsed = function () {\n        _super.prototype.markUsed.call(this);\n        this.jax.wrapperUsage.add(CHTMLmtr.kind);\n    };\n    CHTMLmlabeledtr.kind = mtr_js_3.MmlMlabeledtr.prototype.kind;\n    CHTMLmlabeledtr.styles = {\n        'mjx-mlabeledtr': {\n            display: 'table-row'\n        },\n        'mjx-mlabeledtr[rowalign=\"top\"] > mjx-mtd': {\n            'vertical-align': 'top'\n        },\n        'mjx-mlabeledtr[rowalign=\"center\"] > mjx-mtd': {\n            'vertical-align': 'middle'\n        },\n        'mjx-mlabeledtr[rowalign=\"bottom\"] > mjx-mtd': {\n            'vertical-align': 'bottom'\n        },\n        'mjx-mlabeledtr[rowalign=\"baseline\"] > mjx-mtd': {\n            'vertical-align': 'baseline'\n        },\n        'mjx-mlabeledtr[rowalign=\"axis\"] > mjx-mtd': {\n            'vertical-align': '.25em'\n        }\n    };\n    return CHTMLmlabeledtr;\n}((0, mtr_js_2.CommonMlabeledtrMixin)(CHTMLmtr)));\nexports.CHTMLmlabeledtr = CHTMLmlabeledtr;\n//# sourceMappingURL=mtr.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLmunderover = exports.CHTMLmover = exports.CHTMLmunder = void 0;\nvar msubsup_js_1 = require(\"./msubsup.js\");\nvar munderover_js_1 = require(\"../../common/Wrappers/munderover.js\");\nvar munderover_js_2 = require(\"../../common/Wrappers/munderover.js\");\nvar munderover_js_3 = require(\"../../common/Wrappers/munderover.js\");\nvar munderover_js_4 = require(\"../../../core/MmlTree/MmlNodes/munderover.js\");\nvar CHTMLmunder = (function (_super) {\n    __extends(CHTMLmunder, _super);\n    function CHTMLmunder() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmunder.prototype.toCHTML = function (parent) {\n        if (this.hasMovableLimits()) {\n            _super.prototype.toCHTML.call(this, parent);\n            this.adaptor.setAttribute(this.chtml, 'limits', 'false');\n            return;\n        }\n        this.chtml = this.standardCHTMLnode(parent);\n        var base = this.adaptor.append(this.adaptor.append(this.chtml, this.html('mjx-row')), this.html('mjx-base'));\n        var under = this.adaptor.append(this.adaptor.append(this.chtml, this.html('mjx-row')), this.html('mjx-under'));\n        this.baseChild.toCHTML(base);\n        this.scriptChild.toCHTML(under);\n        var basebox = this.baseChild.getOuterBBox();\n        var underbox = this.scriptChild.getOuterBBox();\n        var k = this.getUnderKV(basebox, underbox)[0];\n        var delta = (this.isLineBelow ? 0 : this.getDelta(true));\n        this.adaptor.setStyle(under, 'paddingTop', this.em(k));\n        this.setDeltaW([base, under], this.getDeltaW([basebox, underbox], [0, -delta]));\n        this.adjustUnderDepth(under, underbox);\n    };\n    CHTMLmunder.kind = munderover_js_4.MmlMunder.prototype.kind;\n    CHTMLmunder.styles = {\n        'mjx-over': {\n            'text-align': 'left'\n        },\n        'mjx-munder:not([limits=\"false\"])': {\n            display: 'inline-table',\n        },\n        'mjx-munder > mjx-row': {\n            'text-align': 'left'\n        },\n        'mjx-under': {\n            'padding-bottom': '.1em'\n        }\n    };\n    return CHTMLmunder;\n}((0, munderover_js_1.CommonMunderMixin)(msubsup_js_1.CHTMLmsub)));\nexports.CHTMLmunder = CHTMLmunder;\nvar CHTMLmover = (function (_super) {\n    __extends(CHTMLmover, _super);\n    function CHTMLmover() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmover.prototype.toCHTML = function (parent) {\n        if (this.hasMovableLimits()) {\n            _super.prototype.toCHTML.call(this, parent);\n            this.adaptor.setAttribute(this.chtml, 'limits', 'false');\n            return;\n        }\n        this.chtml = this.standardCHTMLnode(parent);\n        var over = this.adaptor.append(this.chtml, this.html('mjx-over'));\n        var base = this.adaptor.append(this.chtml, this.html('mjx-base'));\n        this.scriptChild.toCHTML(over);\n        this.baseChild.toCHTML(base);\n        var overbox = this.scriptChild.getOuterBBox();\n        var basebox = this.baseChild.getOuterBBox();\n        this.adjustBaseHeight(base, basebox);\n        var k = this.getOverKU(basebox, overbox)[0];\n        var delta = (this.isLineAbove ? 0 : this.getDelta());\n        this.adaptor.setStyle(over, 'paddingBottom', this.em(k));\n        this.setDeltaW([base, over], this.getDeltaW([basebox, overbox], [0, delta]));\n        this.adjustOverDepth(over, overbox);\n    };\n    CHTMLmover.kind = munderover_js_4.MmlMover.prototype.kind;\n    CHTMLmover.styles = {\n        'mjx-mover:not([limits=\"false\"])': {\n            'padding-top': '.1em'\n        },\n        'mjx-mover:not([limits=\"false\"]) > *': {\n            display: 'block',\n            'text-align': 'left'\n        }\n    };\n    return CHTMLmover;\n}((0, munderover_js_2.CommonMoverMixin)(msubsup_js_1.CHTMLmsup)));\nexports.CHTMLmover = CHTMLmover;\nvar CHTMLmunderover = (function (_super) {\n    __extends(CHTMLmunderover, _super);\n    function CHTMLmunderover() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLmunderover.prototype.toCHTML = function (parent) {\n        if (this.hasMovableLimits()) {\n            _super.prototype.toCHTML.call(this, parent);\n            this.adaptor.setAttribute(this.chtml, 'limits', 'false');\n            return;\n        }\n        this.chtml = this.standardCHTMLnode(parent);\n        var over = this.adaptor.append(this.chtml, this.html('mjx-over'));\n        var table = this.adaptor.append(this.adaptor.append(this.chtml, this.html('mjx-box')), this.html('mjx-munder'));\n        var base = this.adaptor.append(this.adaptor.append(table, this.html('mjx-row')), this.html('mjx-base'));\n        var under = this.adaptor.append(this.adaptor.append(table, this.html('mjx-row')), this.html('mjx-under'));\n        this.overChild.toCHTML(over);\n        this.baseChild.toCHTML(base);\n        this.underChild.toCHTML(under);\n        var overbox = this.overChild.getOuterBBox();\n        var basebox = this.baseChild.getOuterBBox();\n        var underbox = this.underChild.getOuterBBox();\n        this.adjustBaseHeight(base, basebox);\n        var ok = this.getOverKU(basebox, overbox)[0];\n        var uk = this.getUnderKV(basebox, underbox)[0];\n        var delta = this.getDelta();\n        this.adaptor.setStyle(over, 'paddingBottom', this.em(ok));\n        this.adaptor.setStyle(under, 'paddingTop', this.em(uk));\n        this.setDeltaW([base, under, over], this.getDeltaW([basebox, underbox, overbox], [0, this.isLineBelow ? 0 : -delta, this.isLineAbove ? 0 : delta]));\n        this.adjustOverDepth(over, overbox);\n        this.adjustUnderDepth(under, underbox);\n    };\n    CHTMLmunderover.prototype.markUsed = function () {\n        _super.prototype.markUsed.call(this);\n        this.jax.wrapperUsage.add(msubsup_js_1.CHTMLmsubsup.kind);\n    };\n    CHTMLmunderover.kind = munderover_js_4.MmlMunderover.prototype.kind;\n    CHTMLmunderover.styles = {\n        'mjx-munderover:not([limits=\"false\"])': {\n            'padding-top': '.1em'\n        },\n        'mjx-munderover:not([limits=\"false\"]) > *': {\n            display: 'block'\n        },\n    };\n    return CHTMLmunderover;\n}((0, munderover_js_3.CommonMunderoverMixin)(msubsup_js_1.CHTMLmsubsup)));\nexports.CHTMLmunderover = CHTMLmunderover;\n//# sourceMappingURL=munderover.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLscriptbase = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar scriptbase_js_1 = require(\"../../common/Wrappers/scriptbase.js\");\nvar CHTMLscriptbase = (function (_super) {\n    __extends(CHTMLscriptbase, _super);\n    function CHTMLscriptbase() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLscriptbase.prototype.toCHTML = function (parent) {\n        this.chtml = this.standardCHTMLnode(parent);\n        var _a = __read(this.getOffset(), 2), x = _a[0], v = _a[1];\n        var dx = x - (this.baseRemoveIc ? this.baseIc : 0);\n        var style = { 'vertical-align': this.em(v) };\n        if (dx) {\n            style['margin-left'] = this.em(dx);\n        }\n        this.baseChild.toCHTML(this.chtml);\n        this.scriptChild.toCHTML(this.adaptor.append(this.chtml, this.html('mjx-script', { style: style })));\n    };\n    CHTMLscriptbase.prototype.setDeltaW = function (nodes, dx) {\n        for (var i = 0; i < dx.length; i++) {\n            if (dx[i]) {\n                this.adaptor.setStyle(nodes[i], 'paddingLeft', this.em(dx[i]));\n            }\n        }\n    };\n    CHTMLscriptbase.prototype.adjustOverDepth = function (over, overbox) {\n        if (overbox.d >= 0)\n            return;\n        this.adaptor.setStyle(over, 'marginBottom', this.em(overbox.d * overbox.rscale));\n    };\n    CHTMLscriptbase.prototype.adjustUnderDepth = function (under, underbox) {\n        var e_1, _a;\n        if (underbox.d >= 0)\n            return;\n        var adaptor = this.adaptor;\n        var v = this.em(underbox.d);\n        var box = this.html('mjx-box', { style: { 'margin-bottom': v, 'vertical-align': v } });\n        try {\n            for (var _b = __values(adaptor.childNodes(adaptor.firstChild(under))), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                adaptor.append(box, child);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        adaptor.append(adaptor.firstChild(under), box);\n    };\n    CHTMLscriptbase.prototype.adjustBaseHeight = function (base, basebox) {\n        if (this.node.attributes.get('accent')) {\n            var minH = this.font.params.x_height * basebox.scale;\n            if (basebox.h < minH) {\n                this.adaptor.setStyle(base, 'paddingTop', this.em(minH - basebox.h));\n                basebox.h = minH;\n            }\n        }\n    };\n    CHTMLscriptbase.kind = 'scriptbase';\n    return CHTMLscriptbase;\n}((0, scriptbase_js_1.CommonScriptbaseMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLscriptbase = CHTMLscriptbase;\n//# sourceMappingURL=scriptbase.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CHTMLxml = exports.CHTMLannotationXML = exports.CHTMLannotation = exports.CHTMLsemantics = void 0;\nvar Wrapper_js_1 = require(\"../Wrapper.js\");\nvar semantics_js_1 = require(\"../../common/Wrappers/semantics.js\");\nvar semantics_js_2 = require(\"../../../core/MmlTree/MmlNodes/semantics.js\");\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar CHTMLsemantics = (function (_super) {\n    __extends(CHTMLsemantics, _super);\n    function CHTMLsemantics() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLsemantics.prototype.toCHTML = function (parent) {\n        var chtml = this.standardCHTMLnode(parent);\n        if (this.childNodes.length) {\n            this.childNodes[0].toCHTML(chtml);\n        }\n    };\n    CHTMLsemantics.kind = semantics_js_2.MmlSemantics.prototype.kind;\n    return CHTMLsemantics;\n}((0, semantics_js_1.CommonSemanticsMixin)(Wrapper_js_1.CHTMLWrapper)));\nexports.CHTMLsemantics = CHTMLsemantics;\nvar CHTMLannotation = (function (_super) {\n    __extends(CHTMLannotation, _super);\n    function CHTMLannotation() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLannotation.prototype.toCHTML = function (parent) {\n        _super.prototype.toCHTML.call(this, parent);\n    };\n    CHTMLannotation.prototype.computeBBox = function () {\n        return this.bbox;\n    };\n    CHTMLannotation.kind = semantics_js_2.MmlAnnotation.prototype.kind;\n    return CHTMLannotation;\n}(Wrapper_js_1.CHTMLWrapper));\nexports.CHTMLannotation = CHTMLannotation;\nvar CHTMLannotationXML = (function (_super) {\n    __extends(CHTMLannotationXML, _super);\n    function CHTMLannotationXML() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLannotationXML.kind = semantics_js_2.MmlAnnotationXML.prototype.kind;\n    CHTMLannotationXML.styles = {\n        'mjx-annotation-xml': {\n            'font-family': 'initial',\n            'line-height': 'normal'\n        }\n    };\n    return CHTMLannotationXML;\n}(Wrapper_js_1.CHTMLWrapper));\nexports.CHTMLannotationXML = CHTMLannotationXML;\nvar CHTMLxml = (function (_super) {\n    __extends(CHTMLxml, _super);\n    function CHTMLxml() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CHTMLxml.prototype.toCHTML = function (parent) {\n        this.chtml = this.adaptor.append(parent, this.adaptor.clone(this.node.getXML()));\n    };\n    CHTMLxml.prototype.computeBBox = function (bbox, _recompute) {\n        if (_recompute === void 0) { _recompute = false; }\n        var _a = this.jax.measureXMLnode(this.node.getXML()), w = _a.w, h = _a.h, d = _a.d;\n        bbox.w = w;\n        bbox.h = h;\n        bbox.d = d;\n    };\n    CHTMLxml.prototype.getStyles = function () { };\n    CHTMLxml.prototype.getScale = function () { };\n    CHTMLxml.prototype.getVariant = function () { };\n    CHTMLxml.kind = MmlNode_js_1.XMLNode.prototype.kind;\n    CHTMLxml.autoStyle = false;\n    return CHTMLxml;\n}(Wrapper_js_1.CHTMLWrapper));\nexports.CHTMLxml = CHTMLxml;\n//# sourceMappingURL=semantics.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonArrow = exports.CommonDiagonalArrow = exports.CommonDiagonalStrike = exports.CommonBorder2 = exports.CommonBorder = exports.arrowBBox = exports.diagonalArrowDef = exports.arrowDef = exports.arrowBBoxW = exports.arrowBBoxHD = exports.arrowHead = exports.fullBorder = exports.fullPadding = exports.fullBBox = exports.sideNames = exports.sideIndex = exports.SOLID = exports.PADDING = exports.THICKNESS = exports.ARROWY = exports.ARROWDX = exports.ARROWX = void 0;\nexports.ARROWX = 4, exports.ARROWDX = 1, exports.ARROWY = 2;\nexports.THICKNESS = .067;\nexports.PADDING = .2;\nexports.SOLID = exports.THICKNESS + 'em solid';\nexports.sideIndex = { top: 0, right: 1, bottom: 2, left: 3 };\nexports.sideNames = Object.keys(exports.sideIndex);\nexports.fullBBox = (function (node) { return new Array(4).fill(node.thickness + node.padding); });\nexports.fullPadding = (function (node) { return new Array(4).fill(node.padding); });\nexports.fullBorder = (function (node) { return new Array(4).fill(node.thickness); });\nvar arrowHead = function (node) {\n    return Math.max(node.padding, node.thickness * (node.arrowhead.x + node.arrowhead.dx + 1));\n};\nexports.arrowHead = arrowHead;\nvar arrowBBoxHD = function (node, TRBL) {\n    if (node.childNodes[0]) {\n        var _a = node.childNodes[0].getBBox(), h = _a.h, d = _a.d;\n        TRBL[0] = TRBL[2] = Math.max(0, node.thickness * node.arrowhead.y - (h + d) / 2);\n    }\n    return TRBL;\n};\nexports.arrowBBoxHD = arrowBBoxHD;\nvar arrowBBoxW = function (node, TRBL) {\n    if (node.childNodes[0]) {\n        var w = node.childNodes[0].getBBox().w;\n        TRBL[1] = TRBL[3] = Math.max(0, node.thickness * node.arrowhead.y - w / 2);\n    }\n    return TRBL;\n};\nexports.arrowBBoxW = arrowBBoxW;\nexports.arrowDef = {\n    up: [-Math.PI / 2, false, true, 'verticalstrike'],\n    down: [Math.PI / 2, false, true, 'verticakstrike'],\n    right: [0, false, false, 'horizontalstrike'],\n    left: [Math.PI, false, false, 'horizontalstrike'],\n    updown: [Math.PI / 2, true, true, 'verticalstrike uparrow downarrow'],\n    leftright: [0, true, false, 'horizontalstrike leftarrow rightarrow']\n};\nexports.diagonalArrowDef = {\n    updiagonal: [-1, 0, false, 'updiagonalstrike northeastarrow'],\n    northeast: [-1, 0, false, 'updiagonalstrike updiagonalarrow'],\n    southeast: [1, 0, false, 'downdiagonalstrike'],\n    northwest: [1, Math.PI, false, 'downdiagonalstrike'],\n    southwest: [-1, Math.PI, false, 'updiagonalstrike'],\n    northeastsouthwest: [-1, 0, true, 'updiagonalstrike northeastarrow updiagonalarrow southwestarrow'],\n    northwestsoutheast: [1, 0, true, 'downdiagonalstrike northwestarrow southeastarrow']\n};\nexports.arrowBBox = {\n    up: function (node) { return (0, exports.arrowBBoxW)(node, [(0, exports.arrowHead)(node), 0, node.padding, 0]); },\n    down: function (node) { return (0, exports.arrowBBoxW)(node, [node.padding, 0, (0, exports.arrowHead)(node), 0]); },\n    right: function (node) { return (0, exports.arrowBBoxHD)(node, [0, (0, exports.arrowHead)(node), 0, node.padding]); },\n    left: function (node) { return (0, exports.arrowBBoxHD)(node, [0, node.padding, 0, (0, exports.arrowHead)(node)]); },\n    updown: function (node) { return (0, exports.arrowBBoxW)(node, [(0, exports.arrowHead)(node), 0, (0, exports.arrowHead)(node), 0]); },\n    leftright: function (node) { return (0, exports.arrowBBoxHD)(node, [0, (0, exports.arrowHead)(node), 0, (0, exports.arrowHead)(node)]); }\n};\nvar CommonBorder = function (render) {\n    return function (side) {\n        var i = exports.sideIndex[side];\n        return [side, {\n                renderer: render,\n                bbox: function (node) {\n                    var bbox = [0, 0, 0, 0];\n                    bbox[i] = node.thickness + node.padding;\n                    return bbox;\n                },\n                border: function (node) {\n                    var bbox = [0, 0, 0, 0];\n                    bbox[i] = node.thickness;\n                    return bbox;\n                }\n            }];\n    };\n};\nexports.CommonBorder = CommonBorder;\nvar CommonBorder2 = function (render) {\n    return function (name, side1, side2) {\n        var i1 = exports.sideIndex[side1];\n        var i2 = exports.sideIndex[side2];\n        return [name, {\n                renderer: render,\n                bbox: function (node) {\n                    var t = node.thickness + node.padding;\n                    var bbox = [0, 0, 0, 0];\n                    bbox[i1] = bbox[i2] = t;\n                    return bbox;\n                },\n                border: function (node) {\n                    var bbox = [0, 0, 0, 0];\n                    bbox[i1] = bbox[i2] = node.thickness;\n                    return bbox;\n                },\n                remove: side1 + ' ' + side2\n            }];\n    };\n};\nexports.CommonBorder2 = CommonBorder2;\nvar CommonDiagonalStrike = function (render) {\n    return function (name) {\n        var cname = 'mjx-' + name.charAt(0) + 'strike';\n        return [name + 'diagonalstrike', {\n                renderer: render(cname),\n                bbox: exports.fullBBox\n            }];\n    };\n};\nexports.CommonDiagonalStrike = CommonDiagonalStrike;\nvar CommonDiagonalArrow = function (render) {\n    return function (name) {\n        var _a = __read(exports.diagonalArrowDef[name], 4), c = _a[0], pi = _a[1], double = _a[2], remove = _a[3];\n        return [name + 'arrow', {\n                renderer: function (node, _child) {\n                    var _a = __read(node.arrowAW(), 2), a = _a[0], W = _a[1];\n                    var arrow = node.arrow(W, c * (a - pi), double);\n                    render(node, arrow);\n                },\n                bbox: function (node) {\n                    var _a = node.arrowData(), a = _a.a, x = _a.x, y = _a.y;\n                    var _b = __read([node.arrowhead.x, node.arrowhead.y, node.arrowhead.dx], 3), ax = _b[0], ay = _b[1], adx = _b[2];\n                    var _c = __read(node.getArgMod(ax + adx, ay), 2), b = _c[0], ar = _c[1];\n                    var dy = y + (b > a ? node.thickness * ar * Math.sin(b - a) : 0);\n                    var dx = x + (b > Math.PI / 2 - a ? node.thickness * ar * Math.sin(b + a - Math.PI / 2) : 0);\n                    return [dy, dx, dy, dx];\n                },\n                remove: remove\n            }];\n    };\n};\nexports.CommonDiagonalArrow = CommonDiagonalArrow;\nvar CommonArrow = function (render) {\n    return function (name) {\n        var _a = __read(exports.arrowDef[name], 4), angle = _a[0], double = _a[1], isVertical = _a[2], remove = _a[3];\n        return [name + 'arrow', {\n                renderer: function (node, _child) {\n                    var _a = node.getBBox(), w = _a.w, h = _a.h, d = _a.d;\n                    var _b = __read((isVertical ? [h + d, 'X'] : [w, 'Y']), 2), W = _b[0], offset = _b[1];\n                    var dd = node.getOffset(offset);\n                    var arrow = node.arrow(W, angle, double, offset, dd);\n                    render(node, arrow);\n                },\n                bbox: exports.arrowBBox[name],\n                remove: remove\n            }];\n    };\n};\nexports.CommonArrow = CommonArrow;\n//# sourceMappingURL=Notation.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonOutputJax = void 0;\nvar OutputJax_js_1 = require(\"../../core/OutputJax.js\");\nvar MathItem_js_1 = require(\"../../core/MathItem.js\");\nvar Options_js_1 = require(\"../../util/Options.js\");\nvar lengths_js_1 = require(\"../../util/lengths.js\");\nvar Styles_js_1 = require(\"../../util/Styles.js\");\nvar StyleList_js_1 = require(\"../../util/StyleList.js\");\nvar CommonOutputJax = (function (_super) {\n    __extends(CommonOutputJax, _super);\n    function CommonOutputJax(options, defaultFactory, defaultFont) {\n        if (options === void 0) { options = null; }\n        if (defaultFactory === void 0) { defaultFactory = null; }\n        if (defaultFont === void 0) { defaultFont = null; }\n        var _this = this;\n        var _a = __read((0, Options_js_1.separateOptions)(options, defaultFont.OPTIONS), 2), jaxOptions = _a[0], fontOptions = _a[1];\n        _this = _super.call(this, jaxOptions) || this;\n        _this.factory = _this.options.wrapperFactory ||\n            new defaultFactory();\n        _this.factory.jax = _this;\n        _this.cssStyles = _this.options.cssStyles || new StyleList_js_1.CssStyles();\n        _this.font = _this.options.font || new defaultFont(fontOptions);\n        _this.unknownCache = new Map();\n        return _this;\n    }\n    CommonOutputJax.prototype.typeset = function (math, html) {\n        this.setDocument(html);\n        var node = this.createNode();\n        this.toDOM(math, node, html);\n        return node;\n    };\n    CommonOutputJax.prototype.createNode = function () {\n        var jax = this.constructor.NAME;\n        return this.html('mjx-container', { 'class': 'MathJax', jax: jax });\n    };\n    CommonOutputJax.prototype.setScale = function (node) {\n        var scale = this.math.metrics.scale * this.options.scale;\n        if (scale !== 1) {\n            this.adaptor.setStyle(node, 'fontSize', (0, lengths_js_1.percent)(scale));\n        }\n    };\n    CommonOutputJax.prototype.toDOM = function (math, node, html) {\n        if (html === void 0) { html = null; }\n        this.setDocument(html);\n        this.math = math;\n        this.pxPerEm = math.metrics.ex / this.font.params.x_height;\n        math.root.setTeXclass(null);\n        this.setScale(node);\n        this.nodeMap = new Map();\n        this.container = node;\n        this.processMath(math.root, node);\n        this.nodeMap = null;\n        this.executeFilters(this.postFilters, math, html, node);\n    };\n    CommonOutputJax.prototype.getBBox = function (math, html) {\n        this.setDocument(html);\n        this.math = math;\n        math.root.setTeXclass(null);\n        this.nodeMap = new Map();\n        var bbox = this.factory.wrap(math.root).getOuterBBox();\n        this.nodeMap = null;\n        return bbox;\n    };\n    CommonOutputJax.prototype.getMetrics = function (html) {\n        var e_1, _a;\n        this.setDocument(html);\n        var adaptor = this.adaptor;\n        var maps = this.getMetricMaps(html);\n        try {\n            for (var _b = __values(html.math), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var math = _c.value;\n                var parent_1 = adaptor.parent(math.start.node);\n                if (math.state() < MathItem_js_1.STATE.METRICS && parent_1) {\n                    var map = maps[math.display ? 1 : 0];\n                    var _d = map.get(parent_1), em = _d.em, ex = _d.ex, containerWidth = _d.containerWidth, lineWidth = _d.lineWidth, scale = _d.scale, family = _d.family;\n                    math.setMetrics(em, ex, containerWidth, lineWidth, scale);\n                    if (this.options.mtextInheritFont) {\n                        math.outputData.mtextFamily = family;\n                    }\n                    if (this.options.merrorInheritFont) {\n                        math.outputData.merrorFamily = family;\n                    }\n                    math.state(MathItem_js_1.STATE.METRICS);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    CommonOutputJax.prototype.getMetricsFor = function (node, display) {\n        var getFamily = (this.options.mtextInheritFont || this.options.merrorInheritFont);\n        var test = this.getTestElement(node, display);\n        var metrics = this.measureMetrics(test, getFamily);\n        this.adaptor.remove(test);\n        return metrics;\n    };\n    CommonOutputJax.prototype.getMetricMaps = function (html) {\n        var e_2, _a, e_3, _b, e_4, _c, e_5, _d, e_6, _e;\n        var adaptor = this.adaptor;\n        var domMaps = [new Map(), new Map()];\n        try {\n            for (var _f = __values(html.math), _g = _f.next(); !_g.done; _g = _f.next()) {\n                var math = _g.value;\n                var node = adaptor.parent(math.start.node);\n                if (node && math.state() < MathItem_js_1.STATE.METRICS) {\n                    var map = domMaps[math.display ? 1 : 0];\n                    if (!map.has(node)) {\n                        map.set(node, this.getTestElement(node, math.display));\n                    }\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_g && !_g.done && (_a = _f.return)) _a.call(_f);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        var getFamily = this.options.mtextInheritFont || this.options.merrorInheritFont;\n        var maps = [new Map(), new Map()];\n        try {\n            for (var _h = __values(maps.keys()), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var i = _j.value;\n                try {\n                    for (var _k = (e_4 = void 0, __values(domMaps[i].keys())), _l = _k.next(); !_l.done; _l = _k.next()) {\n                        var node = _l.value;\n                        maps[i].set(node, this.measureMetrics(domMaps[i].get(node), getFamily));\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_l && !_l.done && (_c = _k.return)) _c.call(_k);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_b = _h.return)) _b.call(_h);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        try {\n            for (var _m = __values(maps.keys()), _o = _m.next(); !_o.done; _o = _m.next()) {\n                var i = _o.value;\n                try {\n                    for (var _p = (e_6 = void 0, __values(domMaps[i].values())), _q = _p.next(); !_q.done; _q = _p.next()) {\n                        var node = _q.value;\n                        adaptor.remove(node);\n                    }\n                }\n                catch (e_6_1) { e_6 = { error: e_6_1 }; }\n                finally {\n                    try {\n                        if (_q && !_q.done && (_e = _p.return)) _e.call(_p);\n                    }\n                    finally { if (e_6) throw e_6.error; }\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_o && !_o.done && (_d = _m.return)) _d.call(_m);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return maps;\n    };\n    CommonOutputJax.prototype.getTestElement = function (node, display) {\n        var adaptor = this.adaptor;\n        if (!this.testInline) {\n            this.testInline = this.html('mjx-test', { style: {\n                    display: 'inline-block',\n                    width: '100%',\n                    'font-style': 'normal',\n                    'font-weight': 'normal',\n                    'font-size': '100%',\n                    'font-size-adjust': 'none',\n                    'text-indent': 0,\n                    'text-transform': 'none',\n                    'letter-spacing': 'normal',\n                    'word-spacing': 'normal',\n                    overflow: 'hidden',\n                    height: '1px',\n                    'margin-right': '-1px'\n                } }, [\n                this.html('mjx-left-box', { style: {\n                        display: 'inline-block',\n                        width: 0,\n                        'float': 'left'\n                    } }),\n                this.html('mjx-ex-box', { style: {\n                        position: 'absolute',\n                        overflow: 'hidden',\n                        width: '1px', height: '60ex'\n                    } }),\n                this.html('mjx-right-box', { style: {\n                        display: 'inline-block',\n                        width: 0,\n                        'float': 'right'\n                    } })\n            ]);\n            this.testDisplay = adaptor.clone(this.testInline);\n            adaptor.setStyle(this.testDisplay, 'display', 'table');\n            adaptor.setStyle(this.testDisplay, 'margin-right', '');\n            adaptor.setStyle(adaptor.firstChild(this.testDisplay), 'display', 'none');\n            var right = adaptor.lastChild(this.testDisplay);\n            adaptor.setStyle(right, 'display', 'table-cell');\n            adaptor.setStyle(right, 'width', '10000em');\n            adaptor.setStyle(right, 'float', '');\n        }\n        return adaptor.append(node, adaptor.clone(display ? this.testDisplay : this.testInline));\n    };\n    CommonOutputJax.prototype.measureMetrics = function (node, getFamily) {\n        var adaptor = this.adaptor;\n        var family = (getFamily ? adaptor.fontFamily(node) : '');\n        var em = adaptor.fontSize(node);\n        var _a = __read(adaptor.nodeSize(adaptor.childNode(node, 1)), 2), w = _a[0], h = _a[1];\n        var ex = (w ? h / 60 : em * this.options.exFactor);\n        var containerWidth = (!w ? 1000000 : adaptor.getStyle(node, 'display') === 'table' ?\n            adaptor.nodeSize(adaptor.lastChild(node))[0] - 1 :\n            adaptor.nodeBBox(adaptor.lastChild(node)).left -\n                adaptor.nodeBBox(adaptor.firstChild(node)).left - 2);\n        var scale = Math.max(this.options.minScale, this.options.matchFontHeight ? ex / this.font.params.x_height / em : 1);\n        var lineWidth = 1000000;\n        return { em: em, ex: ex, containerWidth: containerWidth, lineWidth: lineWidth, scale: scale, family: family };\n    };\n    CommonOutputJax.prototype.styleSheet = function (html) {\n        var e_7, _a;\n        this.setDocument(html);\n        this.cssStyles.clear();\n        this.cssStyles.addStyles(this.constructor.commonStyles);\n        if ('getStyles' in html) {\n            try {\n                for (var _b = __values(html.getStyles()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var styles = _c.value;\n                    this.cssStyles.addStyles(styles);\n                }\n            }\n            catch (e_7_1) { e_7 = { error: e_7_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_7) throw e_7.error; }\n            }\n        }\n        this.addWrapperStyles(this.cssStyles);\n        this.addFontStyles(this.cssStyles);\n        var sheet = this.html('style', { id: 'MJX-styles' }, [this.text('\\n' + this.cssStyles.cssText + '\\n')]);\n        return sheet;\n    };\n    CommonOutputJax.prototype.addFontStyles = function (styles) {\n        styles.addStyles(this.font.styles);\n    };\n    CommonOutputJax.prototype.addWrapperStyles = function (styles) {\n        var e_8, _a;\n        try {\n            for (var _b = __values(this.factory.getKinds()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var kind = _c.value;\n                this.addClassStyles(this.factory.getNodeClass(kind), styles);\n            }\n        }\n        catch (e_8_1) { e_8 = { error: e_8_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_8) throw e_8.error; }\n        }\n    };\n    CommonOutputJax.prototype.addClassStyles = function (CLASS, styles) {\n        styles.addStyles(CLASS.styles);\n    };\n    CommonOutputJax.prototype.setDocument = function (html) {\n        if (html) {\n            this.document = html;\n            this.adaptor.document = html.document;\n        }\n    };\n    CommonOutputJax.prototype.html = function (type, def, content, ns) {\n        if (def === void 0) { def = {}; }\n        if (content === void 0) { content = []; }\n        return this.adaptor.node(type, def, content, ns);\n    };\n    CommonOutputJax.prototype.text = function (text) {\n        return this.adaptor.text(text);\n    };\n    CommonOutputJax.prototype.fixed = function (m, n) {\n        if (n === void 0) { n = 3; }\n        if (Math.abs(m) < .0006) {\n            return '0';\n        }\n        return m.toFixed(n).replace(/\\.?0+$/, '');\n    };\n    CommonOutputJax.prototype.measureText = function (text, variant, font) {\n        if (font === void 0) { font = ['', false, false]; }\n        var node = this.unknownText(text, variant);\n        if (variant === '-explicitFont') {\n            var styles = this.cssFontStyles(font);\n            this.adaptor.setAttributes(node, { style: styles });\n        }\n        return this.measureTextNodeWithCache(node, text, variant, font);\n    };\n    CommonOutputJax.prototype.measureTextNodeWithCache = function (text, chars, variant, font) {\n        if (font === void 0) { font = ['', false, false]; }\n        if (variant === '-explicitFont') {\n            variant = [font[0], font[1] ? 'T' : 'F', font[2] ? 'T' : 'F', ''].join('-');\n        }\n        if (!this.unknownCache.has(variant)) {\n            this.unknownCache.set(variant, new Map());\n        }\n        var map = this.unknownCache.get(variant);\n        var cached = map.get(chars);\n        if (cached)\n            return cached;\n        var bbox = this.measureTextNode(text);\n        map.set(chars, bbox);\n        return bbox;\n    };\n    CommonOutputJax.prototype.measureXMLnode = function (xml) {\n        var adaptor = this.adaptor;\n        var content = this.html('mjx-xml-block', { style: { display: 'inline-block' } }, [adaptor.clone(xml)]);\n        var base = this.html('mjx-baseline', { style: { display: 'inline-block', width: 0, height: 0 } });\n        var style = {\n            position: 'absolute',\n            display: 'inline-block',\n            'font-family': 'initial',\n            'line-height': 'normal'\n        };\n        var node = this.html('mjx-measure-xml', { style: style }, [base, content]);\n        adaptor.append(adaptor.parent(this.math.start.node), this.container);\n        adaptor.append(this.container, node);\n        var em = this.math.metrics.em * this.math.metrics.scale;\n        var _a = adaptor.nodeBBox(content), left = _a.left, right = _a.right, bottom = _a.bottom, top = _a.top;\n        var w = (right - left) / em;\n        var h = (adaptor.nodeBBox(base).top - top) / em;\n        var d = (bottom - top) / em - h;\n        adaptor.remove(this.container);\n        adaptor.remove(node);\n        return { w: w, h: h, d: d };\n    };\n    CommonOutputJax.prototype.cssFontStyles = function (font, styles) {\n        if (styles === void 0) { styles = {}; }\n        var _a = __read(font, 3), family = _a[0], italic = _a[1], bold = _a[2];\n        styles['font-family'] = this.font.getFamily(family);\n        if (italic)\n            styles['font-style'] = 'italic';\n        if (bold)\n            styles['font-weight'] = 'bold';\n        return styles;\n    };\n    CommonOutputJax.prototype.getFontData = function (styles) {\n        if (!styles) {\n            styles = new Styles_js_1.Styles();\n        }\n        return [this.font.getFamily(styles.get('font-family')),\n            styles.get('font-style') === 'italic',\n            styles.get('font-weight') === 'bold'];\n    };\n    CommonOutputJax.NAME = 'Common';\n    CommonOutputJax.OPTIONS = __assign(__assign({}, OutputJax_js_1.AbstractOutputJax.OPTIONS), { scale: 1, minScale: .5, mtextInheritFont: false, merrorInheritFont: false, mtextFont: '', merrorFont: 'serif', mathmlSpacing: false, skipAttributes: {}, exFactor: .5, displayAlign: 'center', displayIndent: '0', wrapperFactory: null, font: null, cssStyles: null });\n    CommonOutputJax.commonStyles = {};\n    return CommonOutputJax;\n}(OutputJax_js_1.AbstractOutputJax));\nexports.CommonOutputJax = CommonOutputJax;\n//# sourceMappingURL=OutputJax.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonWrapper = void 0;\nvar Wrapper_js_1 = require(\"../../core/Tree/Wrapper.js\");\nvar MmlNode_js_1 = require(\"../../core/MmlTree/MmlNode.js\");\nvar string_js_1 = require(\"../../util/string.js\");\nvar LENGTHS = __importStar(require(\"../../util/lengths.js\"));\nvar Styles_js_1 = require(\"../../util/Styles.js\");\nvar BBox_js_1 = require(\"../../util/BBox.js\");\nvar FontData_js_1 = require(\"./FontData.js\");\nvar SMALLSIZE = 2 / 18;\nfunction MathMLSpace(script, size) {\n    return (script ? size < SMALLSIZE ? 0 : SMALLSIZE : size);\n}\nvar CommonWrapper = (function (_super) {\n    __extends(CommonWrapper, _super);\n    function CommonWrapper(factory, node, parent) {\n        if (parent === void 0) { parent = null; }\n        var _this = _super.call(this, factory, node) || this;\n        _this.parent = null;\n        _this.removedStyles = null;\n        _this.styles = null;\n        _this.variant = '';\n        _this.bboxComputed = false;\n        _this.stretch = FontData_js_1.NOSTRETCH;\n        _this.font = null;\n        _this.parent = parent;\n        _this.font = factory.jax.font;\n        _this.bbox = BBox_js_1.BBox.zero();\n        _this.getStyles();\n        _this.getVariant();\n        _this.getScale();\n        _this.getSpace();\n        _this.childNodes = node.childNodes.map(function (child) {\n            var wrapped = _this.wrap(child);\n            if (wrapped.bbox.pwidth && (node.notParent || node.isKind('math'))) {\n                _this.bbox.pwidth = BBox_js_1.BBox.fullWidth;\n            }\n            return wrapped;\n        });\n        return _this;\n    }\n    Object.defineProperty(CommonWrapper.prototype, \"jax\", {\n        get: function () {\n            return this.factory.jax;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CommonWrapper.prototype, \"adaptor\", {\n        get: function () {\n            return this.factory.jax.adaptor;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CommonWrapper.prototype, \"metrics\", {\n        get: function () {\n            return this.factory.jax.math.metrics;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CommonWrapper.prototype, \"fixesPWidth\", {\n        get: function () {\n            return !this.node.notParent && !this.node.isToken;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CommonWrapper.prototype.wrap = function (node, parent) {\n        if (parent === void 0) { parent = null; }\n        var wrapped = this.factory.wrap(node, parent || this);\n        if (parent) {\n            parent.childNodes.push(wrapped);\n        }\n        this.jax.nodeMap.set(node, wrapped);\n        return wrapped;\n    };\n    CommonWrapper.prototype.getBBox = function (save) {\n        if (save === void 0) { save = true; }\n        if (this.bboxComputed) {\n            return this.bbox;\n        }\n        var bbox = (save ? this.bbox : BBox_js_1.BBox.zero());\n        this.computeBBox(bbox);\n        this.bboxComputed = save;\n        return bbox;\n    };\n    CommonWrapper.prototype.getOuterBBox = function (save) {\n        var e_1, _a;\n        if (save === void 0) { save = true; }\n        var bbox = this.getBBox(save);\n        if (!this.styles)\n            return bbox;\n        var obox = new BBox_js_1.BBox();\n        Object.assign(obox, bbox);\n        try {\n            for (var _b = __values(BBox_js_1.BBox.StyleAdjust), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var _d = __read(_c.value, 2), name_1 = _d[0], side = _d[1];\n                var x = this.styles.get(name_1);\n                if (x) {\n                    obox[side] += this.length2em(x, 1, obox.rscale);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return obox;\n    };\n    CommonWrapper.prototype.computeBBox = function (bbox, recompute) {\n        var e_2, _a;\n        if (recompute === void 0) { recompute = false; }\n        bbox.empty();\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                bbox.append(child.getOuterBBox());\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        bbox.clean();\n        if (this.fixesPWidth && this.setChildPWidths(recompute)) {\n            this.computeBBox(bbox, true);\n        }\n    };\n    CommonWrapper.prototype.setChildPWidths = function (recompute, w, clear) {\n        var e_3, _a;\n        if (w === void 0) { w = null; }\n        if (clear === void 0) { clear = true; }\n        if (recompute) {\n            return false;\n        }\n        if (clear) {\n            this.bbox.pwidth = '';\n        }\n        var changed = false;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                var cbox = child.getOuterBBox();\n                if (cbox.pwidth && child.setChildPWidths(recompute, w === null ? cbox.w : w, clear)) {\n                    changed = true;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return changed;\n    };\n    CommonWrapper.prototype.invalidateBBox = function () {\n        if (this.bboxComputed) {\n            this.bboxComputed = false;\n            if (this.parent) {\n                this.parent.invalidateBBox();\n            }\n        }\n    };\n    CommonWrapper.prototype.copySkewIC = function (bbox) {\n        var first = this.childNodes[0];\n        if (first === null || first === void 0 ? void 0 : first.bbox.sk) {\n            bbox.sk = first.bbox.sk;\n        }\n        if (first === null || first === void 0 ? void 0 : first.bbox.dx) {\n            bbox.dx = first.bbox.dx;\n        }\n        var last = this.childNodes[this.childNodes.length - 1];\n        if (last === null || last === void 0 ? void 0 : last.bbox.ic) {\n            bbox.ic = last.bbox.ic;\n            bbox.w += bbox.ic;\n        }\n    };\n    CommonWrapper.prototype.getStyles = function () {\n        var styleString = this.node.attributes.getExplicit('style');\n        if (!styleString)\n            return;\n        var style = this.styles = new Styles_js_1.Styles(styleString);\n        for (var i = 0, m = CommonWrapper.removeStyles.length; i < m; i++) {\n            var id = CommonWrapper.removeStyles[i];\n            if (style.get(id)) {\n                if (!this.removedStyles)\n                    this.removedStyles = {};\n                this.removedStyles[id] = style.get(id);\n                style.set(id, '');\n            }\n        }\n    };\n    CommonWrapper.prototype.getVariant = function () {\n        if (!this.node.isToken)\n            return;\n        var attributes = this.node.attributes;\n        var variant = attributes.get('mathvariant');\n        if (!attributes.getExplicit('mathvariant')) {\n            var values = attributes.getList('fontfamily', 'fontweight', 'fontstyle');\n            if (this.removedStyles) {\n                var style = this.removedStyles;\n                if (style.fontFamily)\n                    values.family = style.fontFamily;\n                if (style.fontWeight)\n                    values.weight = style.fontWeight;\n                if (style.fontStyle)\n                    values.style = style.fontStyle;\n            }\n            if (values.fontfamily)\n                values.family = values.fontfamily;\n            if (values.fontweight)\n                values.weight = values.fontweight;\n            if (values.fontstyle)\n                values.style = values.fontstyle;\n            if (values.weight && values.weight.match(/^\\d+$/)) {\n                values.weight = (parseInt(values.weight) > 600 ? 'bold' : 'normal');\n            }\n            if (values.family) {\n                variant = this.explicitVariant(values.family, values.weight, values.style);\n            }\n            else {\n                if (this.node.getProperty('variantForm'))\n                    variant = '-tex-variant';\n                variant = (CommonWrapper.BOLDVARIANTS[values.weight] || {})[variant] || variant;\n                variant = (CommonWrapper.ITALICVARIANTS[values.style] || {})[variant] || variant;\n            }\n        }\n        this.variant = variant;\n    };\n    CommonWrapper.prototype.explicitVariant = function (fontFamily, fontWeight, fontStyle) {\n        var style = this.styles;\n        if (!style)\n            style = this.styles = new Styles_js_1.Styles();\n        style.set('fontFamily', fontFamily);\n        if (fontWeight)\n            style.set('fontWeight', fontWeight);\n        if (fontStyle)\n            style.set('fontStyle', fontStyle);\n        return '-explicitFont';\n    };\n    CommonWrapper.prototype.getScale = function () {\n        var scale = 1, parent = this.parent;\n        var pscale = (parent ? parent.bbox.scale : 1);\n        var attributes = this.node.attributes;\n        var scriptlevel = Math.min(attributes.get('scriptlevel'), 2);\n        var fontsize = attributes.get('fontsize');\n        var mathsize = (this.node.isToken || this.node.isKind('mstyle') ?\n            attributes.get('mathsize') : attributes.getInherited('mathsize'));\n        if (scriptlevel !== 0) {\n            scale = Math.pow(attributes.get('scriptsizemultiplier'), scriptlevel);\n            var scriptminsize = this.length2em(attributes.get('scriptminsize'), .8, 1);\n            if (scale < scriptminsize)\n                scale = scriptminsize;\n        }\n        if (this.removedStyles && this.removedStyles.fontSize && !fontsize) {\n            fontsize = this.removedStyles.fontSize;\n        }\n        if (fontsize && !attributes.getExplicit('mathsize')) {\n            mathsize = fontsize;\n        }\n        if (mathsize !== '1') {\n            scale *= this.length2em(mathsize, 1, 1);\n        }\n        this.bbox.scale = scale;\n        this.bbox.rscale = scale / pscale;\n    };\n    CommonWrapper.prototype.getSpace = function () {\n        var isTop = this.isTopEmbellished();\n        var hasSpacing = this.node.hasSpacingAttributes();\n        if (this.jax.options.mathmlSpacing || hasSpacing) {\n            isTop && this.getMathMLSpacing();\n        }\n        else {\n            this.getTeXSpacing(isTop, hasSpacing);\n        }\n    };\n    CommonWrapper.prototype.getMathMLSpacing = function () {\n        var node = this.node.coreMO();\n        var child = node.coreParent();\n        var parent = child.parent;\n        if (!parent || !parent.isKind('mrow') || parent.childNodes.length === 1)\n            return;\n        var attributes = node.attributes;\n        var isScript = (attributes.get('scriptlevel') > 0);\n        this.bbox.L = (attributes.isSet('lspace') ?\n            Math.max(0, this.length2em(attributes.get('lspace'))) :\n            MathMLSpace(isScript, node.lspace));\n        this.bbox.R = (attributes.isSet('rspace') ?\n            Math.max(0, this.length2em(attributes.get('rspace'))) :\n            MathMLSpace(isScript, node.rspace));\n        var n = parent.childIndex(child);\n        if (n === 0)\n            return;\n        var prev = parent.childNodes[n - 1];\n        if (!prev.isEmbellished)\n            return;\n        var bbox = this.jax.nodeMap.get(prev).getBBox();\n        if (bbox.R) {\n            this.bbox.L = Math.max(0, this.bbox.L - bbox.R);\n        }\n    };\n    CommonWrapper.prototype.getTeXSpacing = function (isTop, hasSpacing) {\n        if (!hasSpacing) {\n            var space = this.node.texSpacing();\n            if (space) {\n                this.bbox.L = this.length2em(space);\n            }\n        }\n        if (isTop || hasSpacing) {\n            var attributes = this.node.coreMO().attributes;\n            if (attributes.isSet('lspace')) {\n                this.bbox.L = Math.max(0, this.length2em(attributes.get('lspace')));\n            }\n            if (attributes.isSet('rspace')) {\n                this.bbox.R = Math.max(0, this.length2em(attributes.get('rspace')));\n            }\n        }\n    };\n    CommonWrapper.prototype.isTopEmbellished = function () {\n        return (this.node.isEmbellished &&\n            !(this.node.parent && this.node.parent.isEmbellished));\n    };\n    CommonWrapper.prototype.core = function () {\n        return this.jax.nodeMap.get(this.node.core());\n    };\n    CommonWrapper.prototype.coreMO = function () {\n        return this.jax.nodeMap.get(this.node.coreMO());\n    };\n    CommonWrapper.prototype.getText = function () {\n        var e_4, _a;\n        var text = '';\n        if (this.node.isToken) {\n            try {\n                for (var _b = __values(this.node.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    if (child instanceof MmlNode_js_1.TextNode) {\n                        text += child.getText();\n                    }\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n        }\n        return text;\n    };\n    CommonWrapper.prototype.canStretch = function (direction) {\n        this.stretch = FontData_js_1.NOSTRETCH;\n        if (this.node.isEmbellished) {\n            var core = this.core();\n            if (core && core.node !== this.node) {\n                if (core.canStretch(direction)) {\n                    this.stretch = core.stretch;\n                }\n            }\n        }\n        return this.stretch.dir !== 0;\n    };\n    CommonWrapper.prototype.getAlignShift = function () {\n        var _a;\n        var _b = (_a = this.node.attributes).getList.apply(_a, __spreadArray([], __read(MmlNode_js_1.indentAttributes), false)), indentalign = _b.indentalign, indentshift = _b.indentshift, indentalignfirst = _b.indentalignfirst, indentshiftfirst = _b.indentshiftfirst;\n        if (indentalignfirst !== 'indentalign') {\n            indentalign = indentalignfirst;\n        }\n        if (indentalign === 'auto') {\n            indentalign = this.jax.options.displayAlign;\n        }\n        if (indentshiftfirst !== 'indentshift') {\n            indentshift = indentshiftfirst;\n        }\n        if (indentshift === 'auto') {\n            indentshift = this.jax.options.displayIndent;\n            if (indentalign === 'right' && !indentshift.match(/^\\s*0[a-z]*\\s*$/)) {\n                indentshift = ('-' + indentshift.trim()).replace(/^--/, '');\n            }\n        }\n        var shift = this.length2em(indentshift, this.metrics.containerWidth);\n        return [indentalign, shift];\n    };\n    CommonWrapper.prototype.getAlignX = function (W, bbox, align) {\n        return (align === 'right' ? W - (bbox.w + bbox.R) * bbox.rscale :\n            align === 'left' ? bbox.L * bbox.rscale :\n                (W - bbox.w * bbox.rscale) / 2);\n    };\n    CommonWrapper.prototype.getAlignY = function (H, D, h, d, align) {\n        return (align === 'top' ? H - h :\n            align === 'bottom' ? d - D :\n                align === 'center' ? ((H - h) - (D - d)) / 2 :\n                    0);\n    };\n    CommonWrapper.prototype.getWrapWidth = function (i) {\n        return this.childNodes[i].getBBox().w;\n    };\n    CommonWrapper.prototype.getChildAlign = function (_i) {\n        return 'left';\n    };\n    CommonWrapper.prototype.percent = function (m) {\n        return LENGTHS.percent(m);\n    };\n    CommonWrapper.prototype.em = function (m) {\n        return LENGTHS.em(m);\n    };\n    CommonWrapper.prototype.px = function (m, M) {\n        if (M === void 0) { M = -LENGTHS.BIGDIMEN; }\n        return LENGTHS.px(m, M, this.metrics.em);\n    };\n    CommonWrapper.prototype.length2em = function (length, size, scale) {\n        if (size === void 0) { size = 1; }\n        if (scale === void 0) { scale = null; }\n        if (scale === null) {\n            scale = this.bbox.scale;\n        }\n        return LENGTHS.length2em(length, size, scale, this.jax.pxPerEm);\n    };\n    CommonWrapper.prototype.unicodeChars = function (text, name) {\n        if (name === void 0) { name = this.variant; }\n        var chars = (0, string_js_1.unicodeChars)(text);\n        var variant = this.font.getVariant(name);\n        if (variant && variant.chars) {\n            var map_1 = variant.chars;\n            chars = chars.map(function (n) { return ((map_1[n] || [])[3] || {}).smp || n; });\n        }\n        return chars;\n    };\n    CommonWrapper.prototype.remapChars = function (chars) {\n        return chars;\n    };\n    CommonWrapper.prototype.mmlText = function (text) {\n        return this.node.factory.create('text').setText(text);\n    };\n    CommonWrapper.prototype.mmlNode = function (kind, properties, children) {\n        if (properties === void 0) { properties = {}; }\n        if (children === void 0) { children = []; }\n        return this.node.factory.create(kind, properties, children);\n    };\n    CommonWrapper.prototype.createMo = function (text) {\n        var mmlFactory = this.node.factory;\n        var textNode = mmlFactory.create('text').setText(text);\n        var mml = mmlFactory.create('mo', { stretchy: true }, [textNode]);\n        mml.inheritAttributesFrom(this.node);\n        var node = this.wrap(mml);\n        node.parent = this;\n        return node;\n    };\n    CommonWrapper.prototype.getVariantChar = function (variant, n) {\n        var char = this.font.getChar(variant, n) || [0, 0, 0, { unknown: true }];\n        if (char.length === 3) {\n            char[3] = {};\n        }\n        return char;\n    };\n    CommonWrapper.kind = 'unknown';\n    CommonWrapper.styles = {};\n    CommonWrapper.removeStyles = [\n        'fontSize', 'fontFamily', 'fontWeight',\n        'fontStyle', 'fontVariant', 'font'\n    ];\n    CommonWrapper.skipAttributes = {\n        fontfamily: true, fontsize: true, fontweight: true, fontstyle: true,\n        color: true, background: true,\n        'class': true, href: true, style: true,\n        xmlns: true\n    };\n    CommonWrapper.BOLDVARIANTS = {\n        bold: {\n            normal: 'bold',\n            italic: 'bold-italic',\n            fraktur: 'bold-fraktur',\n            script: 'bold-script',\n            'sans-serif': 'bold-sans-serif',\n            'sans-serif-italic': 'sans-serif-bold-italic'\n        },\n        normal: {\n            bold: 'normal',\n            'bold-italic': 'italic',\n            'bold-fraktur': 'fraktur',\n            'bold-script': 'script',\n            'bold-sans-serif': 'sans-serif',\n            'sans-serif-bold-italic': 'sans-serif-italic'\n        }\n    };\n    CommonWrapper.ITALICVARIANTS = {\n        italic: {\n            normal: 'italic',\n            bold: 'bold-italic',\n            'sans-serif': 'sans-serif-italic',\n            'bold-sans-serif': 'sans-serif-bold-italic'\n        },\n        normal: {\n            italic: 'normal',\n            'bold-italic': 'bold',\n            'sans-serif-italic': 'sans-serif',\n            'sans-serif-bold-italic': 'bold-sans-serif'\n        }\n    };\n    return CommonWrapper;\n}(Wrapper_js_1.AbstractWrapper));\nexports.CommonWrapper = CommonWrapper;\n//# sourceMappingURL=Wrapper.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonWrapperFactory = void 0;\nvar WrapperFactory_js_1 = require(\"../../core/Tree/WrapperFactory.js\");\nvar CommonWrapperFactory = (function (_super) {\n    __extends(CommonWrapperFactory, _super);\n    function CommonWrapperFactory() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.jax = null;\n        return _this;\n    }\n    Object.defineProperty(CommonWrapperFactory.prototype, \"Wrappers\", {\n        get: function () {\n            return this.node;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CommonWrapperFactory.defaultNodes = {};\n    return CommonWrapperFactory;\n}(WrapperFactory_js_1.AbstractWrapperFactory));\nexports.CommonWrapperFactory = CommonWrapperFactory;\n//# sourceMappingURL=WrapperFactory.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonTeXAtomMixin = void 0;\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nfunction CommonTeXAtomMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            _super.prototype.computeBBox.call(this, bbox, recompute);\n            if (this.childNodes[0] && this.childNodes[0].bbox.ic) {\n                bbox.ic = this.childNodes[0].bbox.ic;\n            }\n            if (this.node.texClass === MmlNode_js_1.TEXCLASS.VCENTER) {\n                var h = bbox.h, d = bbox.d;\n                var a = this.font.params.axis_height;\n                var dh = ((h + d) / 2 + a) - h;\n                bbox.h += dh;\n                bbox.d -= dh;\n            }\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonTeXAtomMixin = CommonTeXAtomMixin;\n//# sourceMappingURL=TeXAtom.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonTextNodeMixin = void 0;\nfunction CommonTextNodeMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            var e_1, _a;\n            if (_recompute === void 0) { _recompute = false; }\n            var variant = this.parent.variant;\n            var text = this.node.getText();\n            if (variant === '-explicitFont') {\n                var font = this.jax.getFontData(this.parent.styles);\n                var _b = this.jax.measureText(text, variant, font), w = _b.w, h = _b.h, d = _b.d;\n                bbox.h = h;\n                bbox.d = d;\n                bbox.w = w;\n            }\n            else {\n                var chars = this.remappedText(text, variant);\n                bbox.empty();\n                try {\n                    for (var chars_1 = __values(chars), chars_1_1 = chars_1.next(); !chars_1_1.done; chars_1_1 = chars_1.next()) {\n                        var char = chars_1_1.value;\n                        var _c = __read(this.getVariantChar(variant, char), 4), h = _c[0], d = _c[1], w = _c[2], data = _c[3];\n                        if (data.unknown) {\n                            var cbox = this.jax.measureText(String.fromCodePoint(char), variant);\n                            w = cbox.w;\n                            h = cbox.h;\n                            d = cbox.d;\n                        }\n                        bbox.w += w;\n                        if (h > bbox.h)\n                            bbox.h = h;\n                        if (d > bbox.d)\n                            bbox.d = d;\n                        bbox.ic = data.ic || 0;\n                        bbox.sk = data.sk || 0;\n                        bbox.dx = data.dx || 0;\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (chars_1_1 && !chars_1_1.done && (_a = chars_1.return)) _a.call(chars_1);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n                if (chars.length > 1) {\n                    bbox.sk = 0;\n                }\n                bbox.clean();\n            }\n        };\n        class_1.prototype.remappedText = function (text, variant) {\n            var c = this.parent.stretch.c;\n            return (c ? [c] : this.parent.remapChars(this.unicodeChars(text, variant)));\n        };\n        class_1.prototype.getStyles = function () { };\n        class_1.prototype.getVariant = function () { };\n        class_1.prototype.getScale = function () { };\n        class_1.prototype.getSpace = function () { };\n        return class_1;\n    }(Base));\n}\nexports.CommonTextNodeMixin = CommonTextNodeMixin;\n//# sourceMappingURL=TextNode.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMactionMixin = exports.TooltipData = void 0;\nvar string_js_1 = require(\"../../../util/string.js\");\nexports.TooltipData = {\n    dx: '.2em',\n    dy: '.1em',\n    postDelay: 600,\n    clearDelay: 100,\n    hoverTimer: new Map(),\n    clearTimer: new Map(),\n    stopTimers: function (node, data) {\n        if (data.clearTimer.has(node)) {\n            clearTimeout(data.clearTimer.get(node));\n            data.clearTimer.delete(node);\n        }\n        if (data.hoverTimer.has(node)) {\n            clearTimeout(data.hoverTimer.get(node));\n            data.hoverTimer.delete(node);\n        }\n    }\n};\nfunction CommonMactionMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            var actions = _this.constructor.actions;\n            var action = _this.node.attributes.get('actiontype');\n            var _a = __read(actions.get(action) || [(function (_node, _data) { }), {}], 2), handler = _a[0], data = _a[1];\n            _this.action = handler;\n            _this.data = data;\n            _this.getParameters();\n            return _this;\n        }\n        Object.defineProperty(class_1.prototype, \"selected\", {\n            get: function () {\n                var selection = this.node.attributes.get('selection');\n                var i = Math.max(1, Math.min(this.childNodes.length, selection)) - 1;\n                return this.childNodes[i] || this.wrap(this.node.selected);\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.getParameters = function () {\n            var offsets = this.node.attributes.get('data-offsets');\n            var _a = __read((0, string_js_1.split)(offsets || ''), 2), dx = _a[0], dy = _a[1];\n            this.dx = this.length2em(dx || exports.TooltipData.dx);\n            this.dy = this.length2em(dy || exports.TooltipData.dy);\n        };\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            bbox.updateFrom(this.selected.getOuterBBox());\n            this.selected.setChildPWidths(recompute);\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMactionMixin = CommonMactionMixin;\n//# sourceMappingURL=maction.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMathMixin = void 0;\nfunction CommonMathMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.getWrapWidth = function (_i) {\n            return (this.parent ? this.getBBox().w : this.metrics.containerWidth / this.jax.pxPerEm);\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMathMixin = CommonMathMixin;\n//# sourceMappingURL=math.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMencloseMixin = void 0;\nvar Notation = __importStar(require(\"../Notation.js\"));\nvar string_js_1 = require(\"../../../util/string.js\");\nfunction CommonMencloseMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.notations = {};\n            _this.renderChild = null;\n            _this.msqrt = null;\n            _this.padding = Notation.PADDING;\n            _this.thickness = Notation.THICKNESS;\n            _this.arrowhead = { x: Notation.ARROWX, y: Notation.ARROWY, dx: Notation.ARROWDX };\n            _this.TRBL = [0, 0, 0, 0];\n            _this.getParameters();\n            _this.getNotations();\n            _this.removeRedundantNotations();\n            _this.initializeNotations();\n            _this.TRBL = _this.getBBoxExtenders();\n            return _this;\n        }\n        class_1.prototype.getParameters = function () {\n            var attributes = this.node.attributes;\n            var padding = attributes.get('data-padding');\n            if (padding !== undefined) {\n                this.padding = this.length2em(padding, Notation.PADDING);\n            }\n            var thickness = attributes.get('data-thickness');\n            if (thickness !== undefined) {\n                this.thickness = this.length2em(thickness, Notation.THICKNESS);\n            }\n            var arrowhead = attributes.get('data-arrowhead');\n            if (arrowhead !== undefined) {\n                var _b = __read((0, string_js_1.split)(arrowhead), 3), x = _b[0], y = _b[1], dx = _b[2];\n                this.arrowhead = {\n                    x: (x ? parseFloat(x) : Notation.ARROWX),\n                    y: (y ? parseFloat(y) : Notation.ARROWY),\n                    dx: (dx ? parseFloat(dx) : Notation.ARROWDX)\n                };\n            }\n        };\n        class_1.prototype.getNotations = function () {\n            var e_1, _b;\n            var Notations = this.constructor.notations;\n            try {\n                for (var _c = __values((0, string_js_1.split)(this.node.attributes.get('notation'))), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_1 = _d.value;\n                    var notation = Notations.get(name_1);\n                    if (notation) {\n                        this.notations[name_1] = notation;\n                        if (notation.renderChild) {\n                            this.renderChild = notation.renderer;\n                        }\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        };\n        class_1.prototype.removeRedundantNotations = function () {\n            var e_2, _b, e_3, _c;\n            try {\n                for (var _d = __values(Object.keys(this.notations)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var name_2 = _e.value;\n                    if (this.notations[name_2]) {\n                        var remove = this.notations[name_2].remove || '';\n                        try {\n                            for (var _f = (e_3 = void 0, __values(remove.split(/ /))), _g = _f.next(); !_g.done; _g = _f.next()) {\n                                var notation = _g.value;\n                                delete this.notations[notation];\n                            }\n                        }\n                        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                        finally {\n                            try {\n                                if (_g && !_g.done && (_c = _f.return)) _c.call(_f);\n                            }\n                            finally { if (e_3) throw e_3.error; }\n                        }\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        };\n        class_1.prototype.initializeNotations = function () {\n            var e_4, _b;\n            try {\n                for (var _c = __values(Object.keys(this.notations)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_3 = _d.value;\n                    var init = this.notations[name_3].init;\n                    init && init(this);\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n        };\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            var _b = __read(this.TRBL, 4), T = _b[0], R = _b[1], B = _b[2], L = _b[3];\n            var child = this.childNodes[0].getBBox();\n            bbox.combine(child, L, 0);\n            bbox.h += T;\n            bbox.d += B;\n            bbox.w += R;\n            this.setChildPWidths(recompute);\n        };\n        class_1.prototype.getBBoxExtenders = function () {\n            var e_5, _b;\n            var TRBL = [0, 0, 0, 0];\n            try {\n                for (var _c = __values(Object.keys(this.notations)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_4 = _d.value;\n                    this.maximizeEntries(TRBL, this.notations[name_4].bbox(this));\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            return TRBL;\n        };\n        class_1.prototype.getPadding = function () {\n            var e_6, _b;\n            var _this = this;\n            var BTRBL = [0, 0, 0, 0];\n            try {\n                for (var _c = __values(Object.keys(this.notations)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_5 = _d.value;\n                    var border = this.notations[name_5].border;\n                    if (border) {\n                        this.maximizeEntries(BTRBL, border(this));\n                    }\n                }\n            }\n            catch (e_6_1) { e_6 = { error: e_6_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_6) throw e_6.error; }\n            }\n            return [0, 1, 2, 3].map(function (i) { return _this.TRBL[i] - BTRBL[i]; });\n        };\n        class_1.prototype.maximizeEntries = function (X, Y) {\n            for (var i = 0; i < X.length; i++) {\n                if (X[i] < Y[i]) {\n                    X[i] = Y[i];\n                }\n            }\n        };\n        class_1.prototype.getOffset = function (direction) {\n            var _b = __read(this.TRBL, 4), T = _b[0], R = _b[1], B = _b[2], L = _b[3];\n            var d = (direction === 'X' ? R - L : B - T) / 2;\n            return (Math.abs(d) > .001 ? d : 0);\n        };\n        class_1.prototype.getArgMod = function (w, h) {\n            return [Math.atan2(h, w), Math.sqrt(w * w + h * h)];\n        };\n        class_1.prototype.arrow = function (_w, _a, _double, _offset, _dist) {\n            if (_offset === void 0) { _offset = ''; }\n            if (_dist === void 0) { _dist = 0; }\n            return null;\n        };\n        class_1.prototype.arrowData = function () {\n            var _b = __read([this.padding, this.thickness], 2), p = _b[0], t = _b[1];\n            var r = t * (this.arrowhead.x + Math.max(1, this.arrowhead.dx));\n            var _c = this.childNodes[0].getBBox(), h = _c.h, d = _c.d, w = _c.w;\n            var H = h + d;\n            var R = Math.sqrt(H * H + w * w);\n            var x = Math.max(p, r * w / R);\n            var y = Math.max(p, r * H / R);\n            var _d = __read(this.getArgMod(w + 2 * x, H + 2 * y), 2), a = _d[0], W = _d[1];\n            return { a: a, W: W, x: x, y: y };\n        };\n        class_1.prototype.arrowAW = function () {\n            var _b = this.childNodes[0].getBBox(), h = _b.h, d = _b.d, w = _b.w;\n            var _c = __read(this.TRBL, 4), T = _c[0], R = _c[1], B = _c[2], L = _c[3];\n            return this.getArgMod(L + w + R, T + h + d + B);\n        };\n        class_1.prototype.createMsqrt = function (child) {\n            var mmlFactory = this.node.factory;\n            var mml = mmlFactory.create('msqrt');\n            mml.inheritAttributesFrom(this.node);\n            mml.childNodes[0] = child.node;\n            var node = this.wrap(mml);\n            node.parent = this;\n            return node;\n        };\n        class_1.prototype.sqrtTRBL = function () {\n            var bbox = this.msqrt.getBBox();\n            var cbox = this.msqrt.childNodes[0].getBBox();\n            return [bbox.h - cbox.h, 0, bbox.d - cbox.d, bbox.w - cbox.w];\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMencloseMixin = CommonMencloseMixin;\n//# sourceMappingURL=menclose.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMfencedMixin = void 0;\nfunction CommonMfencedMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.mrow = null;\n            _this.createMrow();\n            _this.addMrowChildren();\n            return _this;\n        }\n        class_1.prototype.createMrow = function () {\n            var mmlFactory = this.node.factory;\n            var mrow = mmlFactory.create('inferredMrow');\n            mrow.inheritAttributesFrom(this.node);\n            this.mrow = this.wrap(mrow);\n            this.mrow.parent = this;\n        };\n        class_1.prototype.addMrowChildren = function () {\n            var e_1, _a;\n            var mfenced = this.node;\n            var mrow = this.mrow;\n            this.addMo(mfenced.open);\n            if (this.childNodes.length) {\n                mrow.childNodes.push(this.childNodes[0]);\n            }\n            var i = 0;\n            try {\n                for (var _b = __values(this.childNodes.slice(1)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    this.addMo(mfenced.separators[i++]);\n                    mrow.childNodes.push(child);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            this.addMo(mfenced.close);\n            mrow.stretchChildren();\n        };\n        class_1.prototype.addMo = function (node) {\n            if (!node)\n                return;\n            var mo = this.wrap(node);\n            this.mrow.childNodes.push(mo);\n            mo.parent = this.mrow;\n        };\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            bbox.updateFrom(this.mrow.getOuterBBox());\n            this.setChildPWidths(recompute);\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMfencedMixin = CommonMfencedMixin;\n//# sourceMappingURL=mfenced.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMfracMixin = void 0;\nfunction CommonMfracMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.bevel = null;\n            _this.pad = (_this.node.getProperty('withDelims') ? 0 : _this.font.params.nulldelimiterspace);\n            if (_this.node.attributes.get('bevelled')) {\n                var H = _this.getBevelData(_this.isDisplay()).H;\n                var bevel = _this.bevel = _this.createMo('/');\n                bevel.node.attributes.set('symmetric', true);\n                bevel.canStretch(1);\n                bevel.getStretchedVariant([H], true);\n            }\n            return _this;\n        }\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            bbox.empty();\n            var _a = this.node.attributes.getList('linethickness', 'bevelled'), linethickness = _a.linethickness, bevelled = _a.bevelled;\n            var display = this.isDisplay();\n            var w = null;\n            if (bevelled) {\n                this.getBevelledBBox(bbox, display);\n            }\n            else {\n                var thickness = this.length2em(String(linethickness), .06);\n                w = -2 * this.pad;\n                if (thickness === 0) {\n                    this.getAtopBBox(bbox, display);\n                }\n                else {\n                    this.getFractionBBox(bbox, display, thickness);\n                    w -= .2;\n                }\n                w += bbox.w;\n            }\n            bbox.clean();\n            this.setChildPWidths(recompute, w);\n        };\n        class_1.prototype.getFractionBBox = function (bbox, display, t) {\n            var nbox = this.childNodes[0].getOuterBBox();\n            var dbox = this.childNodes[1].getOuterBBox();\n            var tex = this.font.params;\n            var a = tex.axis_height;\n            var _a = this.getTUV(display, t), T = _a.T, u = _a.u, v = _a.v;\n            bbox.combine(nbox, 0, a + T + Math.max(nbox.d * nbox.rscale, u));\n            bbox.combine(dbox, 0, a - T - Math.max(dbox.h * dbox.rscale, v));\n            bbox.w += 2 * this.pad + .2;\n        };\n        class_1.prototype.getTUV = function (display, t) {\n            var tex = this.font.params;\n            var a = tex.axis_height;\n            var T = (display ? 3.5 : 1.5) * t;\n            return { T: (display ? 3.5 : 1.5) * t,\n                u: (display ? tex.num1 : tex.num2) - a - T,\n                v: (display ? tex.denom1 : tex.denom2) + a - T };\n        };\n        class_1.prototype.getAtopBBox = function (bbox, display) {\n            var _a = this.getUVQ(display), u = _a.u, v = _a.v, nbox = _a.nbox, dbox = _a.dbox;\n            bbox.combine(nbox, 0, u);\n            bbox.combine(dbox, 0, -v);\n            bbox.w += 2 * this.pad;\n        };\n        class_1.prototype.getUVQ = function (display) {\n            var nbox = this.childNodes[0].getOuterBBox();\n            var dbox = this.childNodes[1].getOuterBBox();\n            var tex = this.font.params;\n            var _a = __read((display ? [tex.num1, tex.denom1] : [tex.num3, tex.denom2]), 2), u = _a[0], v = _a[1];\n            var p = (display ? 7 : 3) * tex.rule_thickness;\n            var q = (u - nbox.d * nbox.scale) - (dbox.h * dbox.scale - v);\n            if (q < p) {\n                u += (p - q) / 2;\n                v += (p - q) / 2;\n                q = p;\n            }\n            return { u: u, v: v, q: q, nbox: nbox, dbox: dbox };\n        };\n        class_1.prototype.getBevelledBBox = function (bbox, display) {\n            var _a = this.getBevelData(display), u = _a.u, v = _a.v, delta = _a.delta, nbox = _a.nbox, dbox = _a.dbox;\n            var lbox = this.bevel.getOuterBBox();\n            bbox.combine(nbox, 0, u);\n            bbox.combine(lbox, bbox.w - delta / 2, 0);\n            bbox.combine(dbox, bbox.w - delta / 2, v);\n        };\n        class_1.prototype.getBevelData = function (display) {\n            var nbox = this.childNodes[0].getOuterBBox();\n            var dbox = this.childNodes[1].getOuterBBox();\n            var delta = (display ? .4 : .15);\n            var H = Math.max(nbox.scale * (nbox.h + nbox.d), dbox.scale * (dbox.h + dbox.d)) + 2 * delta;\n            var a = this.font.params.axis_height;\n            var u = nbox.scale * (nbox.d - nbox.h) / 2 + a + delta;\n            var v = dbox.scale * (dbox.d - dbox.h) / 2 + a - delta;\n            return { H: H, delta: delta, u: u, v: v, nbox: nbox, dbox: dbox };\n        };\n        class_1.prototype.canStretch = function (_direction) {\n            return false;\n        };\n        class_1.prototype.isDisplay = function () {\n            var _a = this.node.attributes.getList('displaystyle', 'scriptlevel'), displaystyle = _a.displaystyle, scriptlevel = _a.scriptlevel;\n            return displaystyle && scriptlevel === 0;\n        };\n        class_1.prototype.getWrapWidth = function (i) {\n            var attributes = this.node.attributes;\n            if (attributes.get('bevelled')) {\n                return this.childNodes[i].getOuterBBox().w;\n            }\n            var w = this.getBBox().w;\n            var thickness = this.length2em(attributes.get('linethickness'));\n            return w - (thickness ? .2 : 0) - 2 * this.pad;\n        };\n        class_1.prototype.getChildAlign = function (i) {\n            var attributes = this.node.attributes;\n            return (attributes.get('bevelled') ? 'left' : attributes.get(['numalign', 'denomalign'][i]));\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMfracMixin = CommonMfracMixin;\n//# sourceMappingURL=mfrac.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMglyphMixin = void 0;\nfunction CommonMglyphMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.getParameters();\n            return _this;\n        }\n        class_1.prototype.getParameters = function () {\n            var _a = this.node.attributes.getList('width', 'height', 'valign', 'src', 'index'), width = _a.width, height = _a.height, valign = _a.valign, src = _a.src, index = _a.index;\n            if (src) {\n                this.width = (width === 'auto' ? 1 : this.length2em(width));\n                this.height = (height === 'auto' ? 1 : this.length2em(height));\n                this.valign = this.length2em(valign || '0');\n            }\n            else {\n                var text = String.fromCodePoint(parseInt(index));\n                var mmlFactory = this.node.factory;\n                this.charWrapper = this.wrap(mmlFactory.create('text').setText(text));\n                this.charWrapper.parent = this;\n            }\n        };\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            if (_recompute === void 0) { _recompute = false; }\n            if (this.charWrapper) {\n                bbox.updateFrom(this.charWrapper.getBBox());\n            }\n            else {\n                bbox.w = this.width;\n                bbox.h = this.height + this.valign;\n                bbox.d = -this.valign;\n            }\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMglyphMixin = CommonMglyphMixin;\n//# sourceMappingURL=mglyph.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMiMixin = void 0;\nfunction CommonMiMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            if (_recompute === void 0) { _recompute = false; }\n            _super.prototype.computeBBox.call(this, bbox);\n            this.copySkewIC(bbox);\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMiMixin = CommonMiMixin;\n//# sourceMappingURL=mi.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMmultiscriptsMixin = exports.ScriptNames = exports.NextScript = void 0;\nvar BBox_js_1 = require(\"../../../util/BBox.js\");\nexports.NextScript = {\n    base: 'subList',\n    subList: 'supList',\n    supList: 'subList',\n    psubList: 'psupList',\n    psupList: 'psubList',\n};\nexports.ScriptNames = ['sup', 'sup', 'psup', 'psub'];\nfunction CommonMmultiscriptsMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.scriptData = null;\n            _this.firstPrescript = 0;\n            _this.getScriptData();\n            return _this;\n        }\n        class_1.prototype.combinePrePost = function (pre, post) {\n            var bbox = new BBox_js_1.BBox(pre);\n            bbox.combine(post, 0, 0);\n            return bbox;\n        };\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            var scriptspace = this.font.params.scriptspace;\n            var data = this.scriptData;\n            var sub = this.combinePrePost(data.sub, data.psub);\n            var sup = this.combinePrePost(data.sup, data.psup);\n            var _a = __read(this.getUVQ(sub, sup), 2), u = _a[0], v = _a[1];\n            bbox.empty();\n            if (data.numPrescripts) {\n                bbox.combine(data.psup, scriptspace, u);\n                bbox.combine(data.psub, scriptspace, v);\n            }\n            bbox.append(data.base);\n            if (data.numScripts) {\n                var w = bbox.w;\n                bbox.combine(data.sup, w, u);\n                bbox.combine(data.sub, w, v);\n                bbox.w += scriptspace;\n            }\n            bbox.clean();\n            this.setChildPWidths(recompute);\n        };\n        class_1.prototype.getScriptData = function () {\n            var data = this.scriptData = {\n                base: null, sub: BBox_js_1.BBox.empty(), sup: BBox_js_1.BBox.empty(), psub: BBox_js_1.BBox.empty(), psup: BBox_js_1.BBox.empty(),\n                numPrescripts: 0, numScripts: 0\n            };\n            var lists = this.getScriptBBoxLists();\n            this.combineBBoxLists(data.sub, data.sup, lists.subList, lists.supList);\n            this.combineBBoxLists(data.psub, data.psup, lists.psubList, lists.psupList);\n            data.base = lists.base[0];\n            data.numPrescripts = lists.psubList.length;\n            data.numScripts = lists.subList.length;\n        };\n        class_1.prototype.getScriptBBoxLists = function () {\n            var e_1, _a;\n            var lists = {\n                base: [], subList: [], supList: [], psubList: [], psupList: []\n            };\n            var script = 'base';\n            try {\n                for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    if (child.node.isKind('mprescripts')) {\n                        script = 'psubList';\n                    }\n                    else {\n                        lists[script].push(child.getOuterBBox());\n                        script = exports.NextScript[script];\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            this.firstPrescript = lists.subList.length + lists.supList.length + 2;\n            this.padLists(lists.subList, lists.supList);\n            this.padLists(lists.psubList, lists.psupList);\n            return lists;\n        };\n        class_1.prototype.padLists = function (list1, list2) {\n            if (list1.length > list2.length) {\n                list2.push(BBox_js_1.BBox.empty());\n            }\n        };\n        class_1.prototype.combineBBoxLists = function (bbox1, bbox2, list1, list2) {\n            for (var i = 0; i < list1.length; i++) {\n                var _a = __read(this.getScaledWHD(list1[i]), 3), w1 = _a[0], h1 = _a[1], d1 = _a[2];\n                var _b = __read(this.getScaledWHD(list2[i]), 3), w2 = _b[0], h2 = _b[1], d2 = _b[2];\n                var w = Math.max(w1, w2);\n                bbox1.w += w;\n                bbox2.w += w;\n                if (h1 > bbox1.h)\n                    bbox1.h = h1;\n                if (d1 > bbox1.d)\n                    bbox1.d = d1;\n                if (h2 > bbox2.h)\n                    bbox2.h = h2;\n                if (d2 > bbox2.d)\n                    bbox2.d = d2;\n            }\n        };\n        class_1.prototype.getScaledWHD = function (bbox) {\n            var w = bbox.w, h = bbox.h, d = bbox.d, rscale = bbox.rscale;\n            return [w * rscale, h * rscale, d * rscale];\n        };\n        class_1.prototype.getUVQ = function (subbox, supbox) {\n            var _a;\n            if (!this.UVQ) {\n                var _b = __read([0, 0, 0], 3), u = _b[0], v = _b[1], q = _b[2];\n                if (subbox.h === 0 && subbox.d === 0) {\n                    u = this.getU();\n                }\n                else if (supbox.h === 0 && supbox.d === 0) {\n                    u = -this.getV();\n                }\n                else {\n                    _a = __read(_super.prototype.getUVQ.call(this, subbox, supbox), 3), u = _a[0], v = _a[1], q = _a[2];\n                }\n                this.UVQ = [u, v, q];\n            }\n            return this.UVQ;\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMmultiscriptsMixin = CommonMmultiscriptsMixin;\n//# sourceMappingURL=mmultiscripts.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMnMixin = void 0;\nfunction CommonMnMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.remapChars = function (chars) {\n            if (chars.length) {\n                var text = this.font.getRemappedChar('mn', chars[0]);\n                if (text) {\n                    var c = this.unicodeChars(text, this.variant);\n                    if (c.length === 1) {\n                        chars[0] = c[0];\n                    }\n                    else {\n                        chars = c.concat(chars.slice(1));\n                    }\n                }\n            }\n            return chars;\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMnMixin = CommonMnMixin;\n//# sourceMappingURL=mn.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMoMixin = exports.DirectionVH = void 0;\nvar BBox_js_1 = require(\"../../../util/BBox.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar FontData_js_1 = require(\"../FontData.js\");\nexports.DirectionVH = (_a = {},\n    _a[1] = 'v',\n    _a[2] = 'h',\n    _a);\nfunction CommonMoMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.size = null;\n            _this.isAccent = _this.node.isAccent;\n            return _this;\n        }\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            if (_recompute === void 0) { _recompute = false; }\n            this.protoBBox(bbox);\n            if (this.node.attributes.get('symmetric') &&\n                this.stretch.dir !== 2) {\n                var d = this.getCenterOffset(bbox);\n                bbox.h += d;\n                bbox.d -= d;\n            }\n            if (this.node.getProperty('mathaccent') &&\n                (this.stretch.dir === 0 || this.size >= 0)) {\n                bbox.w = 0;\n            }\n        };\n        class_1.prototype.protoBBox = function (bbox) {\n            var stretchy = (this.stretch.dir !== 0);\n            if (stretchy && this.size === null) {\n                this.getStretchedVariant([0]);\n            }\n            if (stretchy && this.size < 0)\n                return;\n            _super.prototype.computeBBox.call(this, bbox);\n            this.copySkewIC(bbox);\n        };\n        class_1.prototype.getAccentOffset = function () {\n            var bbox = BBox_js_1.BBox.empty();\n            this.protoBBox(bbox);\n            return -bbox.w / 2;\n        };\n        class_1.prototype.getCenterOffset = function (bbox) {\n            if (bbox === void 0) { bbox = null; }\n            if (!bbox) {\n                bbox = BBox_js_1.BBox.empty();\n                _super.prototype.computeBBox.call(this, bbox);\n            }\n            return ((bbox.h + bbox.d) / 2 + this.font.params.axis_height) - bbox.h;\n        };\n        class_1.prototype.getVariant = function () {\n            if (this.node.attributes.get('largeop')) {\n                this.variant = (this.node.attributes.get('displaystyle') ? '-largeop' : '-smallop');\n                return;\n            }\n            if (!this.node.attributes.getExplicit('mathvariant') &&\n                this.node.getProperty('pseudoscript') === false) {\n                this.variant = '-tex-variant';\n                return;\n            }\n            _super.prototype.getVariant.call(this);\n        };\n        class_1.prototype.canStretch = function (direction) {\n            if (this.stretch.dir !== 0) {\n                return this.stretch.dir === direction;\n            }\n            var attributes = this.node.attributes;\n            if (!attributes.get('stretchy'))\n                return false;\n            var c = this.getText();\n            if (Array.from(c).length !== 1)\n                return false;\n            var delim = this.font.getDelimiter(c.codePointAt(0));\n            this.stretch = (delim && delim.dir === direction ? delim : FontData_js_1.NOSTRETCH);\n            return this.stretch.dir !== 0;\n        };\n        class_1.prototype.getStretchedVariant = function (WH, exact) {\n            var e_1, _a;\n            if (exact === void 0) { exact = false; }\n            if (this.stretch.dir !== 0) {\n                var D = this.getWH(WH);\n                var min = this.getSize('minsize', 0);\n                var max = this.getSize('maxsize', Infinity);\n                var mathaccent = this.node.getProperty('mathaccent');\n                D = Math.max(min, Math.min(max, D));\n                var df = this.font.params.delimiterfactor / 1000;\n                var ds = this.font.params.delimitershortfall;\n                var m = (min || exact ? D : mathaccent ? Math.min(D / df, D + ds) : Math.max(D * df, D - ds));\n                var delim = this.stretch;\n                var c = delim.c || this.getText().codePointAt(0);\n                var i = 0;\n                if (delim.sizes) {\n                    try {\n                        for (var _b = __values(delim.sizes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                            var d = _c.value;\n                            if (d >= m) {\n                                if (mathaccent && i) {\n                                    i--;\n                                }\n                                this.variant = this.font.getSizeVariant(c, i);\n                                this.size = i;\n                                if (delim.schar && delim.schar[i]) {\n                                    this.stretch = __assign(__assign({}, this.stretch), { c: delim.schar[i] });\n                                }\n                                return;\n                            }\n                            i++;\n                        }\n                    }\n                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                    finally {\n                        try {\n                            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                        }\n                        finally { if (e_1) throw e_1.error; }\n                    }\n                }\n                if (delim.stretch) {\n                    this.size = -1;\n                    this.invalidateBBox();\n                    this.getStretchBBox(WH, this.checkExtendedHeight(D, delim), delim);\n                }\n                else {\n                    this.variant = this.font.getSizeVariant(c, i - 1);\n                    this.size = i - 1;\n                }\n            }\n        };\n        class_1.prototype.getSize = function (name, value) {\n            var attributes = this.node.attributes;\n            if (attributes.isSet(name)) {\n                value = this.length2em(attributes.get(name), 1, 1);\n            }\n            return value;\n        };\n        class_1.prototype.getWH = function (WH) {\n            if (WH.length === 0)\n                return 0;\n            if (WH.length === 1)\n                return WH[0];\n            var _a = __read(WH, 2), H = _a[0], D = _a[1];\n            var a = this.font.params.axis_height;\n            return (this.node.attributes.get('symmetric') ? 2 * Math.max(H - a, D + a) : H + D);\n        };\n        class_1.prototype.getStretchBBox = function (WHD, D, C) {\n            var _a;\n            if (C.hasOwnProperty('min') && C.min > D) {\n                D = C.min;\n            }\n            var _b = __read(C.HDW, 3), h = _b[0], d = _b[1], w = _b[2];\n            if (this.stretch.dir === 1) {\n                _a = __read(this.getBaseline(WHD, D, C), 2), h = _a[0], d = _a[1];\n            }\n            else {\n                w = D;\n            }\n            this.bbox.h = h;\n            this.bbox.d = d;\n            this.bbox.w = w;\n        };\n        class_1.prototype.getBaseline = function (WHD, HD, C) {\n            var hasWHD = (WHD.length === 2 && WHD[0] + WHD[1] === HD);\n            var symmetric = this.node.attributes.get('symmetric');\n            var _a = __read((hasWHD ? WHD : [HD, 0]), 2), H = _a[0], D = _a[1];\n            var _b = __read([H + D, 0], 2), h = _b[0], d = _b[1];\n            if (symmetric) {\n                var a = this.font.params.axis_height;\n                if (hasWHD) {\n                    h = 2 * Math.max(H - a, D + a);\n                }\n                d = h / 2 - a;\n            }\n            else if (hasWHD) {\n                d = D;\n            }\n            else {\n                var _c = __read((C.HDW || [.75, .25]), 2), ch = _c[0], cd = _c[1];\n                d = cd * (h / (ch + cd));\n            }\n            return [h - d, d];\n        };\n        class_1.prototype.checkExtendedHeight = function (D, C) {\n            if (C.fullExt) {\n                var _a = __read(C.fullExt, 2), extSize = _a[0], endSize = _a[1];\n                var n = Math.ceil(Math.max(0, D - endSize) / extSize);\n                D = endSize + n * extSize;\n            }\n            return D;\n        };\n        class_1.prototype.remapChars = function (chars) {\n            var primes = this.node.getProperty('primes');\n            if (primes) {\n                return (0, string_js_1.unicodeChars)(primes);\n            }\n            if (chars.length === 1) {\n                var parent_1 = this.node.coreParent().parent;\n                var isAccent = this.isAccent && !parent_1.isKind('mrow');\n                var map = (isAccent ? 'accent' : 'mo');\n                var text = this.font.getRemappedChar(map, chars[0]);\n                if (text) {\n                    chars = this.unicodeChars(text, this.variant);\n                }\n            }\n            return chars;\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMoMixin = CommonMoMixin;\n//# sourceMappingURL=mo.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMpaddedMixin = void 0;\nfunction CommonMpaddedMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.getDimens = function () {\n            var values = this.node.attributes.getList('width', 'height', 'depth', 'lspace', 'voffset');\n            var bbox = this.childNodes[0].getBBox();\n            var w = bbox.w, h = bbox.h, d = bbox.d;\n            var W = w, H = h, D = d, x = 0, y = 0, dx = 0;\n            if (values.width !== '')\n                w = this.dimen(values.width, bbox, 'w', 0);\n            if (values.height !== '')\n                h = this.dimen(values.height, bbox, 'h', 0);\n            if (values.depth !== '')\n                d = this.dimen(values.depth, bbox, 'd', 0);\n            if (values.voffset !== '')\n                y = this.dimen(values.voffset, bbox);\n            if (values.lspace !== '')\n                x = this.dimen(values.lspace, bbox);\n            var align = this.node.attributes.get('data-align');\n            if (align) {\n                dx = this.getAlignX(w, bbox, align);\n            }\n            return [H, D, W, h - H, d - D, w - W, x, y, dx];\n        };\n        class_1.prototype.dimen = function (length, bbox, d, m) {\n            if (d === void 0) { d = ''; }\n            if (m === void 0) { m = null; }\n            length = String(length);\n            var match = length.match(/width|height|depth/);\n            var size = (match ? bbox[match[0].charAt(0)] :\n                (d ? bbox[d] : 0));\n            var dimen = (this.length2em(length, size) || 0);\n            if (length.match(/^[-+]/) && d) {\n                dimen += size;\n            }\n            if (m != null) {\n                dimen = Math.max(m, dimen);\n            }\n            return dimen;\n        };\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            var _a = __read(this.getDimens(), 6), H = _a[0], D = _a[1], W = _a[2], dh = _a[3], dd = _a[4], dw = _a[5];\n            bbox.w = W + dw;\n            bbox.h = H + dh;\n            bbox.d = D + dd;\n            this.setChildPWidths(recompute, bbox.w);\n        };\n        class_1.prototype.getWrapWidth = function (_i) {\n            return this.getBBox().w;\n        };\n        class_1.prototype.getChildAlign = function (_i) {\n            return this.node.attributes.get('data-align') || 'left';\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMpaddedMixin = CommonMpaddedMixin;\n//# sourceMappingURL=mpadded.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMrootMixin = void 0;\nfunction CommonMrootMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        Object.defineProperty(class_1.prototype, \"surd\", {\n            get: function () {\n                return 2;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_1.prototype, \"root\", {\n            get: function () {\n                return 1;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.combineRootBBox = function (BBOX, sbox, H) {\n            var bbox = this.childNodes[this.root].getOuterBBox();\n            var h = this.getRootDimens(sbox, H)[1];\n            BBOX.combine(bbox, 0, h);\n        };\n        class_1.prototype.getRootDimens = function (sbox, H) {\n            var surd = this.childNodes[this.surd];\n            var bbox = this.childNodes[this.root].getOuterBBox();\n            var offset = (surd.size < 0 ? .5 : .6) * sbox.w;\n            var w = bbox.w, rscale = bbox.rscale;\n            var W = Math.max(w, offset / rscale);\n            var dx = Math.max(0, W - w);\n            var h = this.rootHeight(bbox, sbox, surd.size, H);\n            var x = W * rscale - offset;\n            return [x, h, dx];\n        };\n        class_1.prototype.rootHeight = function (rbox, sbox, size, H) {\n            var h = sbox.h + sbox.d;\n            var b = (size < 0 ? 1.9 : .55 * h) - (h - H);\n            return b + Math.max(0, rbox.d * rbox.rscale);\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMrootMixin = CommonMrootMixin;\n//# sourceMappingURL=mroot.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonInferredMrowMixin = exports.CommonMrowMixin = void 0;\nvar BBox_js_1 = require(\"../../../util/BBox.js\");\nfunction CommonMrowMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var e_1, _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.stretchChildren();\n            try {\n                for (var _b = __values(_this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    if (child.bbox.pwidth) {\n                        _this.bbox.pwidth = BBox_js_1.BBox.fullWidth;\n                        break;\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return _this;\n        }\n        Object.defineProperty(class_1.prototype, \"fixesPWidth\", {\n            get: function () {\n                return false;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.stretchChildren = function () {\n            var e_2, _a, e_3, _b, e_4, _c;\n            var stretchy = [];\n            try {\n                for (var _d = __values(this.childNodes), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var child = _e.value;\n                    if (child.canStretch(1)) {\n                        stretchy.push(child);\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            var count = stretchy.length;\n            var nodeCount = this.childNodes.length;\n            if (count && nodeCount > 1) {\n                var H = 0, D = 0;\n                var all = (count > 1 && count === nodeCount);\n                try {\n                    for (var _f = __values(this.childNodes), _g = _f.next(); !_g.done; _g = _f.next()) {\n                        var child = _g.value;\n                        var noStretch = (child.stretch.dir === 0);\n                        if (all || noStretch) {\n                            var _h = child.getOuterBBox(noStretch), h = _h.h, d = _h.d, rscale = _h.rscale;\n                            h *= rscale;\n                            d *= rscale;\n                            if (h > H)\n                                H = h;\n                            if (d > D)\n                                D = d;\n                        }\n                    }\n                }\n                catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                finally {\n                    try {\n                        if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                    }\n                    finally { if (e_3) throw e_3.error; }\n                }\n                try {\n                    for (var stretchy_1 = __values(stretchy), stretchy_1_1 = stretchy_1.next(); !stretchy_1_1.done; stretchy_1_1 = stretchy_1.next()) {\n                        var child = stretchy_1_1.value;\n                        child.coreMO().getStretchedVariant([H, D]);\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (stretchy_1_1 && !stretchy_1_1.done && (_c = stretchy_1.return)) _c.call(stretchy_1);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            }\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMrowMixin = CommonMrowMixin;\nfunction CommonInferredMrowMixin(Base) {\n    return (function (_super) {\n        __extends(class_2, _super);\n        function class_2() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_2.prototype.getScale = function () {\n            this.bbox.scale = this.parent.bbox.scale;\n            this.bbox.rscale = 1;\n        };\n        return class_2;\n    }(Base));\n}\nexports.CommonInferredMrowMixin = CommonInferredMrowMixin;\n//# sourceMappingURL=mrow.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMsMixin = void 0;\nfunction CommonMsMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            var attributes = _this.node.attributes;\n            var quotes = attributes.getList('lquote', 'rquote');\n            if (_this.variant !== 'monospace') {\n                if (!attributes.isSet('lquote') && quotes.lquote === '\"')\n                    quotes.lquote = '\\u201C';\n                if (!attributes.isSet('rquote') && quotes.rquote === '\"')\n                    quotes.rquote = '\\u201D';\n            }\n            _this.childNodes.unshift(_this.createText(quotes.lquote));\n            _this.childNodes.push(_this.createText(quotes.rquote));\n            return _this;\n        }\n        class_1.prototype.createText = function (text) {\n            var node = this.wrap(this.mmlText(text));\n            node.parent = this;\n            return node;\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMsMixin = CommonMsMixin;\n//# sourceMappingURL=ms.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMspaceMixin = void 0;\nfunction CommonMspaceMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            if (_recompute === void 0) { _recompute = false; }\n            var attributes = this.node.attributes;\n            bbox.w = this.length2em(attributes.get('width'), 0);\n            bbox.h = this.length2em(attributes.get('height'), 0);\n            bbox.d = this.length2em(attributes.get('depth'), 0);\n        };\n        class_1.prototype.handleVariant = function () {\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMspaceMixin = CommonMspaceMixin;\n//# sourceMappingURL=mspace.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMsqrtMixin = void 0;\nvar BBox_js_1 = require(\"../../../util/BBox.js\");\nfunction CommonMsqrtMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            var surd = _this.createMo('\\u221A');\n            surd.canStretch(1);\n            var _a = _this.childNodes[_this.base].getOuterBBox(), h = _a.h, d = _a.d;\n            var t = _this.font.params.rule_thickness;\n            var p = (_this.node.attributes.get('displaystyle') ? _this.font.params.x_height : t);\n            _this.surdH = h + d + 2 * t + p / 4;\n            surd.getStretchedVariant([_this.surdH - d, d], true);\n            return _this;\n        }\n        Object.defineProperty(class_1.prototype, \"base\", {\n            get: function () {\n                return 0;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_1.prototype, \"surd\", {\n            get: function () {\n                return 1;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_1.prototype, \"root\", {\n            get: function () {\n                return null;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.createMo = function (text) {\n            var node = _super.prototype.createMo.call(this, text);\n            this.childNodes.push(node);\n            return node;\n        };\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            var surdbox = this.childNodes[this.surd].getBBox();\n            var basebox = new BBox_js_1.BBox(this.childNodes[this.base].getOuterBBox());\n            var q = this.getPQ(surdbox)[1];\n            var t = this.font.params.rule_thickness;\n            var H = basebox.h + q + t;\n            var _a = __read(this.getRootDimens(surdbox, H), 1), x = _a[0];\n            bbox.h = H + t;\n            this.combineRootBBox(bbox, surdbox, H);\n            bbox.combine(surdbox, x, H - surdbox.h);\n            bbox.combine(basebox, x + surdbox.w, 0);\n            bbox.clean();\n            this.setChildPWidths(recompute);\n        };\n        class_1.prototype.combineRootBBox = function (_bbox, _sbox, _H) {\n        };\n        class_1.prototype.getPQ = function (sbox) {\n            var t = this.font.params.rule_thickness;\n            var p = (this.node.attributes.get('displaystyle') ? this.font.params.x_height : t);\n            var q = (sbox.h + sbox.d > this.surdH ?\n                ((sbox.h + sbox.d) - (this.surdH - 2 * t - p / 2)) / 2 :\n                t + p / 4);\n            return [p, q];\n        };\n        class_1.prototype.getRootDimens = function (_sbox, _H) {\n            return [0, 0, 0, 0];\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMsqrtMixin = CommonMsqrtMixin;\n//# sourceMappingURL=msqrt.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMsubsupMixin = exports.CommonMsupMixin = exports.CommonMsubMixin = void 0;\nfunction CommonMsubMixin(Base) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(class_1, _super);\n            function class_1() {\n                return _super !== null && _super.apply(this, arguments) || this;\n            }\n            Object.defineProperty(class_1.prototype, \"scriptChild\", {\n                get: function () {\n                    return this.childNodes[this.node.sub];\n                },\n                enumerable: false,\n                configurable: true\n            });\n            class_1.prototype.getOffset = function () {\n                return [0, -this.getV()];\n            };\n            return class_1;\n        }(Base)),\n        _a.useIC = false,\n        _a;\n}\nexports.CommonMsubMixin = CommonMsubMixin;\nfunction CommonMsupMixin(Base) {\n    return (function (_super) {\n        __extends(class_2, _super);\n        function class_2() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        Object.defineProperty(class_2.prototype, \"scriptChild\", {\n            get: function () {\n                return this.childNodes[this.node.sup];\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_2.prototype.getOffset = function () {\n            var x = this.getAdjustedIc() - (this.baseRemoveIc ? 0 : this.baseIc);\n            return [x, this.getU()];\n        };\n        return class_2;\n    }(Base));\n}\nexports.CommonMsupMixin = CommonMsupMixin;\nfunction CommonMsubsupMixin(Base) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(class_3, _super);\n            function class_3() {\n                var _this = _super !== null && _super.apply(this, arguments) || this;\n                _this.UVQ = null;\n                return _this;\n            }\n            Object.defineProperty(class_3.prototype, \"subChild\", {\n                get: function () {\n                    return this.childNodes[this.node.sub];\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(class_3.prototype, \"supChild\", {\n                get: function () {\n                    return this.childNodes[this.node.sup];\n                },\n                enumerable: false,\n                configurable: true\n            });\n            class_3.prototype.computeBBox = function (bbox, recompute) {\n                if (recompute === void 0) { recompute = false; }\n                var basebox = this.baseChild.getOuterBBox();\n                var _a = __read([this.subChild.getOuterBBox(), this.supChild.getOuterBBox()], 2), subbox = _a[0], supbox = _a[1];\n                bbox.empty();\n                bbox.append(basebox);\n                var w = this.getBaseWidth();\n                var x = this.getAdjustedIc();\n                var _b = __read(this.getUVQ(), 2), u = _b[0], v = _b[1];\n                bbox.combine(subbox, w, v);\n                bbox.combine(supbox, w + x, u);\n                bbox.w += this.font.params.scriptspace;\n                bbox.clean();\n                this.setChildPWidths(recompute);\n            };\n            class_3.prototype.getUVQ = function (subbox, supbox) {\n                if (subbox === void 0) { subbox = this.subChild.getOuterBBox(); }\n                if (supbox === void 0) { supbox = this.supChild.getOuterBBox(); }\n                var basebox = this.baseCore.getOuterBBox();\n                if (this.UVQ)\n                    return this.UVQ;\n                var tex = this.font.params;\n                var t = 3 * tex.rule_thickness;\n                var subscriptshift = this.length2em(this.node.attributes.get('subscriptshift'), tex.sub2);\n                var drop = this.baseCharZero(basebox.d * this.baseScale + tex.sub_drop * subbox.rscale);\n                var _a = __read([this.getU(), Math.max(drop, subscriptshift)], 2), u = _a[0], v = _a[1];\n                var q = (u - supbox.d * supbox.rscale) - (subbox.h * subbox.rscale - v);\n                if (q < t) {\n                    v += t - q;\n                    var p = (4 / 5) * tex.x_height - (u - supbox.d * supbox.rscale);\n                    if (p > 0) {\n                        u += p;\n                        v -= p;\n                    }\n                }\n                u = Math.max(this.length2em(this.node.attributes.get('superscriptshift'), u), u);\n                v = Math.max(this.length2em(this.node.attributes.get('subscriptshift'), v), v);\n                q = (u - supbox.d * supbox.rscale) - (subbox.h * subbox.rscale - v);\n                this.UVQ = [u, -v, q];\n                return this.UVQ;\n            };\n            return class_3;\n        }(Base)),\n        _a.useIC = false,\n        _a;\n}\nexports.CommonMsubsupMixin = CommonMsubsupMixin;\n//# sourceMappingURL=msubsup.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMtableMixin = void 0;\nvar BBox_js_1 = require(\"../../../util/BBox.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar numeric_js_1 = require(\"../../../util/numeric.js\");\nfunction CommonMtableMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.numCols = 0;\n            _this.numRows = 0;\n            _this.data = null;\n            _this.pwidthCells = [];\n            _this.pWidth = 0;\n            _this.numCols = (0, numeric_js_1.max)(_this.tableRows.map(function (row) { return row.numCells; }));\n            _this.numRows = _this.childNodes.length;\n            _this.hasLabels = _this.childNodes.reduce(function (value, row) { return value || row.node.isKind('mlabeledtr'); }, false);\n            _this.findContainer();\n            _this.isTop = !_this.container || (_this.container.node.isKind('math') && !_this.container.parent);\n            if (_this.isTop) {\n                _this.jax.table = _this;\n            }\n            _this.getPercentageWidth();\n            var attributes = _this.node.attributes;\n            _this.frame = attributes.get('frame') !== 'none';\n            _this.fLine = (_this.frame && attributes.get('frame') ? .07 : 0);\n            _this.fSpace = (_this.frame ? _this.convertLengths(_this.getAttributeArray('framespacing')) : [0, 0]);\n            _this.cSpace = _this.convertLengths(_this.getColumnAttributes('columnspacing'));\n            _this.rSpace = _this.convertLengths(_this.getRowAttributes('rowspacing'));\n            _this.cLines = _this.getColumnAttributes('columnlines').map(function (x) { return (x === 'none' ? 0 : .07); });\n            _this.rLines = _this.getRowAttributes('rowlines').map(function (x) { return (x === 'none' ? 0 : .07); });\n            _this.cWidths = _this.getColumnWidths();\n            _this.stretchRows();\n            _this.stretchColumns();\n            return _this;\n        }\n        Object.defineProperty(class_1.prototype, \"tableRows\", {\n            get: function () {\n                return this.childNodes;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.findContainer = function () {\n            var node = this;\n            var parent = node.parent;\n            while (parent && (parent.node.notParent || parent.node.isKind('mrow'))) {\n                node = parent;\n                parent = parent.parent;\n            }\n            this.container = parent;\n            this.containerI = node.node.childPosition();\n        };\n        class_1.prototype.getPercentageWidth = function () {\n            if (this.hasLabels) {\n                this.bbox.pwidth = BBox_js_1.BBox.fullWidth;\n            }\n            else {\n                var width = this.node.attributes.get('width');\n                if ((0, string_js_1.isPercent)(width)) {\n                    this.bbox.pwidth = width;\n                }\n            }\n        };\n        class_1.prototype.stretchRows = function () {\n            var equal = this.node.attributes.get('equalrows');\n            var HD = (equal ? this.getEqualRowHeight() : 0);\n            var _a = (equal ? this.getTableData() : { H: [0], D: [0] }), H = _a.H, D = _a.D;\n            var rows = this.tableRows;\n            for (var i = 0; i < this.numRows; i++) {\n                var hd = (equal ? [(HD + H[i] - D[i]) / 2, (HD - H[i] + D[i]) / 2] : null);\n                rows[i].stretchChildren(hd);\n            }\n        };\n        class_1.prototype.stretchColumns = function () {\n            for (var i = 0; i < this.numCols; i++) {\n                var width = (typeof this.cWidths[i] === 'number' ? this.cWidths[i] : null);\n                this.stretchColumn(i, width);\n            }\n        };\n        class_1.prototype.stretchColumn = function (i, W) {\n            var e_1, _a, e_2, _b, e_3, _c;\n            var stretchy = [];\n            try {\n                for (var _d = __values(this.tableRows), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var row = _e.value;\n                    var cell = row.getChild(i);\n                    if (cell) {\n                        var child = cell.childNodes[0];\n                        if (child.stretch.dir === 0 &&\n                            child.canStretch(2)) {\n                            stretchy.push(child);\n                        }\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            var count = stretchy.length;\n            var nodeCount = this.childNodes.length;\n            if (count && nodeCount > 1) {\n                if (W === null) {\n                    W = 0;\n                    var all = (count > 1 && count === nodeCount);\n                    try {\n                        for (var _f = __values(this.tableRows), _g = _f.next(); !_g.done; _g = _f.next()) {\n                            var row = _g.value;\n                            var cell = row.getChild(i);\n                            if (cell) {\n                                var child = cell.childNodes[0];\n                                var noStretch = (child.stretch.dir === 0);\n                                if (all || noStretch) {\n                                    var w = child.getBBox(noStretch).w;\n                                    if (w > W) {\n                                        W = w;\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n                try {\n                    for (var stretchy_1 = __values(stretchy), stretchy_1_1 = stretchy_1.next(); !stretchy_1_1.done; stretchy_1_1 = stretchy_1.next()) {\n                        var child = stretchy_1_1.value;\n                        child.coreMO().getStretchedVariant([W]);\n                    }\n                }\n                catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                finally {\n                    try {\n                        if (stretchy_1_1 && !stretchy_1_1.done && (_c = stretchy_1.return)) _c.call(stretchy_1);\n                    }\n                    finally { if (e_3) throw e_3.error; }\n                }\n            }\n        };\n        class_1.prototype.getTableData = function () {\n            if (this.data) {\n                return this.data;\n            }\n            var H = new Array(this.numRows).fill(0);\n            var D = new Array(this.numRows).fill(0);\n            var W = new Array(this.numCols).fill(0);\n            var NH = new Array(this.numRows);\n            var ND = new Array(this.numRows);\n            var LW = [0];\n            var rows = this.tableRows;\n            for (var j = 0; j < rows.length; j++) {\n                var M = 0;\n                var row = rows[j];\n                var align = row.node.attributes.get('rowalign');\n                for (var i = 0; i < row.numCells; i++) {\n                    var cell = row.getChild(i);\n                    M = this.updateHDW(cell, i, j, align, H, D, W, M);\n                    this.recordPWidthCell(cell, i);\n                }\n                NH[j] = H[j];\n                ND[j] = D[j];\n                if (row.labeled) {\n                    M = this.updateHDW(row.childNodes[0], 0, j, align, H, D, LW, M);\n                }\n                this.extendHD(j, H, D, M);\n                this.extendHD(j, NH, ND, M);\n            }\n            var L = LW[0];\n            this.data = { H: H, D: D, W: W, NH: NH, ND: ND, L: L };\n            return this.data;\n        };\n        class_1.prototype.updateHDW = function (cell, i, j, align, H, D, W, M) {\n            var _a = cell.getBBox(), h = _a.h, d = _a.d, w = _a.w;\n            var scale = cell.parent.bbox.rscale;\n            if (cell.parent.bbox.rscale !== 1) {\n                h *= scale;\n                d *= scale;\n                w *= scale;\n            }\n            if (this.node.getProperty('useHeight')) {\n                if (h < .75)\n                    h = .75;\n                if (d < .25)\n                    d = .25;\n            }\n            var m = 0;\n            align = cell.node.attributes.get('rowalign') || align;\n            if (align !== 'baseline' && align !== 'axis') {\n                m = h + d;\n                h = d = 0;\n            }\n            if (h > H[j])\n                H[j] = h;\n            if (d > D[j])\n                D[j] = d;\n            if (m > M)\n                M = m;\n            if (W && w > W[i])\n                W[i] = w;\n            return M;\n        };\n        class_1.prototype.extendHD = function (i, H, D, M) {\n            var d = (M - (H[i] + D[i])) / 2;\n            if (d < .00001)\n                return;\n            H[i] += d;\n            D[i] += d;\n        };\n        class_1.prototype.recordPWidthCell = function (cell, i) {\n            if (cell.childNodes[0] && cell.childNodes[0].getBBox().pwidth) {\n                this.pwidthCells.push([cell, i]);\n            }\n        };\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            if (_recompute === void 0) { _recompute = false; }\n            var _a = this.getTableData(), H = _a.H, D = _a.D;\n            var height, width;\n            if (this.node.attributes.get('equalrows')) {\n                var HD = this.getEqualRowHeight();\n                height = (0, numeric_js_1.sum)([].concat(this.rLines, this.rSpace)) + HD * this.numRows;\n            }\n            else {\n                height = (0, numeric_js_1.sum)(H.concat(D, this.rLines, this.rSpace));\n            }\n            height += 2 * (this.fLine + this.fSpace[1]);\n            var CW = this.getComputedWidths();\n            width = (0, numeric_js_1.sum)(CW.concat(this.cLines, this.cSpace)) + 2 * (this.fLine + this.fSpace[0]);\n            var w = this.node.attributes.get('width');\n            if (w !== 'auto') {\n                width = Math.max(this.length2em(w, 0) + 2 * this.fLine, width);\n            }\n            var _b = __read(this.getBBoxHD(height), 2), h = _b[0], d = _b[1];\n            bbox.h = h;\n            bbox.d = d;\n            bbox.w = width;\n            var _c = __read(this.getBBoxLR(), 2), L = _c[0], R = _c[1];\n            bbox.L = L;\n            bbox.R = R;\n            if (!(0, string_js_1.isPercent)(w)) {\n                this.setColumnPWidths();\n            }\n        };\n        class_1.prototype.setChildPWidths = function (_recompute, cwidth, _clear) {\n            var width = this.node.attributes.get('width');\n            if (!(0, string_js_1.isPercent)(width))\n                return false;\n            if (!this.hasLabels) {\n                this.bbox.pwidth = '';\n                this.container.bbox.pwidth = '';\n            }\n            var _a = this.bbox, w = _a.w, L = _a.L, R = _a.R;\n            var labelInWidth = this.node.attributes.get('data-width-includes-label');\n            var W = Math.max(w, this.length2em(width, Math.max(cwidth, L + w + R))) - (labelInWidth ? L + R : 0);\n            var cols = (this.node.attributes.get('equalcolumns') ?\n                Array(this.numCols).fill(this.percent(1 / Math.max(1, this.numCols))) :\n                this.getColumnAttributes('columnwidth', 0));\n            this.cWidths = this.getColumnWidthsFixed(cols, W);\n            var CW = this.getComputedWidths();\n            this.pWidth = (0, numeric_js_1.sum)(CW.concat(this.cLines, this.cSpace)) + 2 * (this.fLine + this.fSpace[0]);\n            if (this.isTop) {\n                this.bbox.w = this.pWidth;\n            }\n            this.setColumnPWidths();\n            if (this.pWidth !== w) {\n                this.parent.invalidateBBox();\n            }\n            return this.pWidth !== w;\n        };\n        class_1.prototype.setColumnPWidths = function () {\n            var e_4, _a;\n            var W = this.cWidths;\n            try {\n                for (var _b = __values(this.pwidthCells), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var _d = __read(_c.value, 2), cell = _d[0], i = _d[1];\n                    if (cell.setChildPWidths(false, W[i])) {\n                        cell.invalidateBBox();\n                        cell.getBBox();\n                    }\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n        };\n        class_1.prototype.getBBoxHD = function (height) {\n            var _a = __read(this.getAlignmentRow(), 2), align = _a[0], row = _a[1];\n            if (row === null) {\n                var a = this.font.params.axis_height;\n                var h2 = height / 2;\n                var HD = {\n                    top: [0, height],\n                    center: [h2, h2],\n                    bottom: [height, 0],\n                    baseline: [h2, h2],\n                    axis: [h2 + a, h2 - a]\n                };\n                return HD[align] || [h2, h2];\n            }\n            else {\n                var y = this.getVerticalPosition(row, align);\n                return [y, height - y];\n            }\n        };\n        class_1.prototype.getBBoxLR = function () {\n            if (this.hasLabels) {\n                var attributes = this.node.attributes;\n                var side = attributes.get('side');\n                var _a = __read(this.getPadAlignShift(side), 2), pad = _a[0], align = _a[1];\n                var labels = this.hasLabels && !!attributes.get('data-width-includes-label');\n                if (labels && this.frame && this.fSpace[0]) {\n                    pad -= this.fSpace[0];\n                }\n                return (align === 'center' && !labels ? [pad, pad] :\n                    side === 'left' ? [pad, 0] : [0, pad]);\n            }\n            return [0, 0];\n        };\n        class_1.prototype.getPadAlignShift = function (side) {\n            var L = this.getTableData().L;\n            var sep = this.length2em(this.node.attributes.get('minlabelspacing'));\n            var pad = L + sep;\n            var _a = __read((this.styles == null ? ['', ''] :\n                [this.styles.get('padding-left'), this.styles.get('padding-right')]), 2), lpad = _a[0], rpad = _a[1];\n            if (lpad || rpad) {\n                pad = Math.max(pad, this.length2em(lpad || '0'), this.length2em(rpad || '0'));\n            }\n            var _b = __read(this.getAlignShift(), 2), align = _b[0], shift = _b[1];\n            if (align === side) {\n                shift = (side === 'left' ? Math.max(pad, shift) - pad : Math.min(-pad, shift) + pad);\n            }\n            return [pad, align, shift];\n        };\n        class_1.prototype.getAlignShift = function () {\n            return (this.isTop ? _super.prototype.getAlignShift.call(this) :\n                [this.container.getChildAlign(this.containerI), 0]);\n        };\n        class_1.prototype.getWidth = function () {\n            return this.pWidth || this.getBBox().w;\n        };\n        class_1.prototype.getEqualRowHeight = function () {\n            var _a = this.getTableData(), H = _a.H, D = _a.D;\n            var HD = Array.from(H.keys()).map(function (i) { return H[i] + D[i]; });\n            return Math.max.apply(Math, HD);\n        };\n        class_1.prototype.getComputedWidths = function () {\n            var _this = this;\n            var W = this.getTableData().W;\n            var CW = Array.from(W.keys()).map(function (i) {\n                return (typeof _this.cWidths[i] === 'number' ? _this.cWidths[i] : W[i]);\n            });\n            if (this.node.attributes.get('equalcolumns')) {\n                CW = Array(CW.length).fill((0, numeric_js_1.max)(CW));\n            }\n            return CW;\n        };\n        class_1.prototype.getColumnWidths = function () {\n            var width = this.node.attributes.get('width');\n            if (this.node.attributes.get('equalcolumns')) {\n                return this.getEqualColumns(width);\n            }\n            var swidths = this.getColumnAttributes('columnwidth', 0);\n            if (width === 'auto') {\n                return this.getColumnWidthsAuto(swidths);\n            }\n            if ((0, string_js_1.isPercent)(width)) {\n                return this.getColumnWidthsPercent(swidths);\n            }\n            return this.getColumnWidthsFixed(swidths, this.length2em(width));\n        };\n        class_1.prototype.getEqualColumns = function (width) {\n            var n = Math.max(1, this.numCols);\n            var cwidth;\n            if (width === 'auto') {\n                var W = this.getTableData().W;\n                cwidth = (0, numeric_js_1.max)(W);\n            }\n            else if ((0, string_js_1.isPercent)(width)) {\n                cwidth = this.percent(1 / n);\n            }\n            else {\n                var w = (0, numeric_js_1.sum)([].concat(this.cLines, this.cSpace)) + 2 * this.fSpace[0];\n                cwidth = Math.max(0, this.length2em(width) - w) / n;\n            }\n            return Array(this.numCols).fill(cwidth);\n        };\n        class_1.prototype.getColumnWidthsAuto = function (swidths) {\n            var _this = this;\n            return swidths.map(function (x) {\n                if (x === 'auto' || x === 'fit')\n                    return null;\n                if ((0, string_js_1.isPercent)(x))\n                    return x;\n                return _this.length2em(x);\n            });\n        };\n        class_1.prototype.getColumnWidthsPercent = function (swidths) {\n            var _this = this;\n            var hasFit = swidths.indexOf('fit') >= 0;\n            var W = (hasFit ? this.getTableData() : { W: null }).W;\n            return Array.from(swidths.keys()).map(function (i) {\n                var x = swidths[i];\n                if (x === 'fit')\n                    return null;\n                if (x === 'auto')\n                    return (hasFit ? W[i] : null);\n                if ((0, string_js_1.isPercent)(x))\n                    return x;\n                return _this.length2em(x);\n            });\n        };\n        class_1.prototype.getColumnWidthsFixed = function (swidths, width) {\n            var _this = this;\n            var indices = Array.from(swidths.keys());\n            var fit = indices.filter(function (i) { return swidths[i] === 'fit'; });\n            var auto = indices.filter(function (i) { return swidths[i] === 'auto'; });\n            var n = fit.length || auto.length;\n            var W = (n ? this.getTableData() : { W: null }).W;\n            var cwidth = width - (0, numeric_js_1.sum)([].concat(this.cLines, this.cSpace)) - 2 * this.fSpace[0];\n            var dw = cwidth;\n            indices.forEach(function (i) {\n                var x = swidths[i];\n                dw -= (x === 'fit' || x === 'auto' ? W[i] : _this.length2em(x, cwidth));\n            });\n            var fw = (n && dw > 0 ? dw / n : 0);\n            return indices.map(function (i) {\n                var x = swidths[i];\n                if (x === 'fit')\n                    return W[i] + fw;\n                if (x === 'auto')\n                    return W[i] + (fit.length === 0 ? fw : 0);\n                return _this.length2em(x, cwidth);\n            });\n        };\n        class_1.prototype.getVerticalPosition = function (i, align) {\n            var equal = this.node.attributes.get('equalrows');\n            var _a = this.getTableData(), H = _a.H, D = _a.D;\n            var HD = (equal ? this.getEqualRowHeight() : 0);\n            var space = this.getRowHalfSpacing();\n            var y = this.fLine;\n            for (var j = 0; j < i; j++) {\n                y += space[j] + (equal ? HD : H[j] + D[j]) + space[j + 1] + this.rLines[j];\n            }\n            var _b = __read((equal ? [(HD + H[i] - D[i]) / 2, (HD - H[i] + D[i]) / 2] : [H[i], D[i]]), 2), h = _b[0], d = _b[1];\n            var offset = {\n                top: 0,\n                center: space[i] + (h + d) / 2,\n                bottom: space[i] + h + d + space[i + 1],\n                baseline: space[i] + h,\n                axis: space[i] + h - .25\n            };\n            y += offset[align] || 0;\n            return y;\n        };\n        class_1.prototype.getEmHalfSpacing = function (fspace, space, scale) {\n            if (scale === void 0) { scale = 1; }\n            var fspaceEm = this.em(fspace * scale);\n            var spaceEm = this.addEm(space, 2 / scale);\n            spaceEm.unshift(fspaceEm);\n            spaceEm.push(fspaceEm);\n            return spaceEm;\n        };\n        class_1.prototype.getRowHalfSpacing = function () {\n            var space = this.rSpace.map(function (x) { return x / 2; });\n            space.unshift(this.fSpace[1]);\n            space.push(this.fSpace[1]);\n            return space;\n        };\n        class_1.prototype.getColumnHalfSpacing = function () {\n            var space = this.cSpace.map(function (x) { return x / 2; });\n            space.unshift(this.fSpace[0]);\n            space.push(this.fSpace[0]);\n            return space;\n        };\n        class_1.prototype.getAlignmentRow = function () {\n            var _a = __read((0, string_js_1.split)(this.node.attributes.get('align')), 2), align = _a[0], row = _a[1];\n            if (row == null)\n                return [align, null];\n            var i = parseInt(row);\n            if (i < 0)\n                i += this.numRows + 1;\n            return [align, i < 1 || i > this.numRows ? null : i - 1];\n        };\n        class_1.prototype.getColumnAttributes = function (name, i) {\n            if (i === void 0) { i = 1; }\n            var n = this.numCols - i;\n            var columns = this.getAttributeArray(name);\n            if (columns.length === 0)\n                return null;\n            while (columns.length < n) {\n                columns.push(columns[columns.length - 1]);\n            }\n            if (columns.length > n) {\n                columns.splice(n);\n            }\n            return columns;\n        };\n        class_1.prototype.getRowAttributes = function (name, i) {\n            if (i === void 0) { i = 1; }\n            var n = this.numRows - i;\n            var rows = this.getAttributeArray(name);\n            if (rows.length === 0)\n                return null;\n            while (rows.length < n) {\n                rows.push(rows[rows.length - 1]);\n            }\n            if (rows.length > n) {\n                rows.splice(n);\n            }\n            return rows;\n        };\n        class_1.prototype.getAttributeArray = function (name) {\n            var value = this.node.attributes.get(name);\n            if (!value)\n                return [this.node.attributes.getDefault(name)];\n            return (0, string_js_1.split)(value);\n        };\n        class_1.prototype.addEm = function (list, n) {\n            var _this = this;\n            if (n === void 0) { n = 1; }\n            if (!list)\n                return null;\n            return list.map(function (x) { return _this.em(x / n); });\n        };\n        class_1.prototype.convertLengths = function (list) {\n            var _this = this;\n            if (!list)\n                return null;\n            return list.map(function (x) { return _this.length2em(x); });\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMtableMixin = CommonMtableMixin;\n//# sourceMappingURL=mtable.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMtdMixin = void 0;\nfunction CommonMtdMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        Object.defineProperty(class_1.prototype, \"fixesPWidth\", {\n            get: function () {\n                return false;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.invalidateBBox = function () {\n            this.bboxComputed = false;\n        };\n        class_1.prototype.getWrapWidth = function (_j) {\n            var table = this.parent.parent;\n            var row = this.parent;\n            var i = this.node.childPosition() - (row.labeled ? 1 : 0);\n            return (typeof (table.cWidths[i]) === 'number' ? table.cWidths[i] : table.getTableData().W[i]);\n        };\n        class_1.prototype.getChildAlign = function (_i) {\n            return this.node.attributes.get('columnalign');\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMtdMixin = CommonMtdMixin;\n//# sourceMappingURL=mtd.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMtextMixin = void 0;\nfunction CommonMtextMixin(Base) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(class_1, _super);\n            function class_1() {\n                return _super !== null && _super.apply(this, arguments) || this;\n            }\n            class_1.prototype.getVariant = function () {\n                var options = this.jax.options;\n                var data = this.jax.math.outputData;\n                var merror = ((!!data.merrorFamily || !!options.merrorFont) && this.node.Parent.isKind('merror'));\n                if (!!data.mtextFamily || !!options.mtextFont || merror) {\n                    var variant = this.node.attributes.get('mathvariant');\n                    var font = this.constructor.INHERITFONTS[variant] || this.jax.font.getCssFont(variant);\n                    var family = font[0] || (merror ? data.merrorFamily || options.merrorFont :\n                        data.mtextFamily || options.mtextFont);\n                    this.variant = this.explicitVariant(family, font[2] ? 'bold' : '', font[1] ? 'italic' : '');\n                    return;\n                }\n                _super.prototype.getVariant.call(this);\n            };\n            return class_1;\n        }(Base)),\n        _a.INHERITFONTS = {\n            normal: ['', false, false],\n            bold: ['', false, true],\n            italic: ['', true, false],\n            'bold-italic': ['', true, true]\n        },\n        _a;\n}\nexports.CommonMtextMixin = CommonMtextMixin;\n//# sourceMappingURL=mtext.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMlabeledtrMixin = exports.CommonMtrMixin = void 0;\nfunction CommonMtrMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        Object.defineProperty(class_1.prototype, \"fixesPWidth\", {\n            get: function () {\n                return false;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_1.prototype, \"numCells\", {\n            get: function () {\n                return this.childNodes.length;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_1.prototype, \"labeled\", {\n            get: function () {\n                return false;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_1.prototype, \"tableCells\", {\n            get: function () {\n                return this.childNodes;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.getChild = function (i) {\n            return this.childNodes[i];\n        };\n        class_1.prototype.getChildBBoxes = function () {\n            return this.childNodes.map(function (cell) { return cell.getBBox(); });\n        };\n        class_1.prototype.stretchChildren = function (HD) {\n            var e_1, _a, e_2, _b, e_3, _c;\n            if (HD === void 0) { HD = null; }\n            var stretchy = [];\n            var children = (this.labeled ? this.childNodes.slice(1) : this.childNodes);\n            try {\n                for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {\n                    var mtd = children_1_1.value;\n                    var child = mtd.childNodes[0];\n                    if (child.canStretch(1)) {\n                        stretchy.push(child);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (children_1_1 && !children_1_1.done && (_a = children_1.return)) _a.call(children_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            var count = stretchy.length;\n            var nodeCount = this.childNodes.length;\n            if (count && nodeCount > 1) {\n                if (HD === null) {\n                    var H = 0, D = 0;\n                    var all = (count > 1 && count === nodeCount);\n                    try {\n                        for (var children_2 = __values(children), children_2_1 = children_2.next(); !children_2_1.done; children_2_1 = children_2.next()) {\n                            var mtd = children_2_1.value;\n                            var child = mtd.childNodes[0];\n                            var noStretch = (child.stretch.dir === 0);\n                            if (all || noStretch) {\n                                var _d = child.getBBox(noStretch), h = _d.h, d = _d.d;\n                                if (h > H) {\n                                    H = h;\n                                }\n                                if (d > D) {\n                                    D = d;\n                                }\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (children_2_1 && !children_2_1.done && (_b = children_2.return)) _b.call(children_2);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                    HD = [H, D];\n                }\n                try {\n                    for (var stretchy_1 = __values(stretchy), stretchy_1_1 = stretchy_1.next(); !stretchy_1_1.done; stretchy_1_1 = stretchy_1.next()) {\n                        var child = stretchy_1_1.value;\n                        child.coreMO().getStretchedVariant(HD);\n                    }\n                }\n                catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                finally {\n                    try {\n                        if (stretchy_1_1 && !stretchy_1_1.done && (_c = stretchy_1.return)) _c.call(stretchy_1);\n                    }\n                    finally { if (e_3) throw e_3.error; }\n                }\n            }\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMtrMixin = CommonMtrMixin;\nfunction CommonMlabeledtrMixin(Base) {\n    return (function (_super) {\n        __extends(class_2, _super);\n        function class_2() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        Object.defineProperty(class_2.prototype, \"numCells\", {\n            get: function () {\n                return Math.max(0, this.childNodes.length - 1);\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_2.prototype, \"labeled\", {\n            get: function () {\n                return true;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_2.prototype, \"tableCells\", {\n            get: function () {\n                return this.childNodes.slice(1);\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_2.prototype.getChild = function (i) {\n            return this.childNodes[i + 1];\n        };\n        class_2.prototype.getChildBBoxes = function () {\n            return this.childNodes.slice(1).map(function (cell) { return cell.getBBox(); });\n        };\n        return class_2;\n    }(Base));\n}\nexports.CommonMlabeledtrMixin = CommonMlabeledtrMixin;\n//# sourceMappingURL=mtr.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonMunderoverMixin = exports.CommonMoverMixin = exports.CommonMunderMixin = void 0;\nfunction CommonMunderMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.stretchChildren();\n            return _this;\n        }\n        Object.defineProperty(class_1.prototype, \"scriptChild\", {\n            get: function () {\n                return this.childNodes[this.node.under];\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_1.prototype.computeBBox = function (bbox, recompute) {\n            if (recompute === void 0) { recompute = false; }\n            if (this.hasMovableLimits()) {\n                _super.prototype.computeBBox.call(this, bbox, recompute);\n                return;\n            }\n            bbox.empty();\n            var basebox = this.baseChild.getOuterBBox();\n            var underbox = this.scriptChild.getOuterBBox();\n            var v = this.getUnderKV(basebox, underbox)[1];\n            var delta = (this.isLineBelow ? 0 : this.getDelta(true));\n            var _a = __read(this.getDeltaW([basebox, underbox], [0, -delta]), 2), bw = _a[0], uw = _a[1];\n            bbox.combine(basebox, bw, 0);\n            bbox.combine(underbox, uw, v);\n            bbox.d += this.font.params.big_op_spacing5;\n            bbox.clean();\n            this.setChildPWidths(recompute);\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonMunderMixin = CommonMunderMixin;\nfunction CommonMoverMixin(Base) {\n    return (function (_super) {\n        __extends(class_2, _super);\n        function class_2() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.stretchChildren();\n            return _this;\n        }\n        Object.defineProperty(class_2.prototype, \"scriptChild\", {\n            get: function () {\n                return this.childNodes[this.node.over];\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_2.prototype.computeBBox = function (bbox) {\n            if (this.hasMovableLimits()) {\n                _super.prototype.computeBBox.call(this, bbox);\n                return;\n            }\n            bbox.empty();\n            var basebox = this.baseChild.getOuterBBox();\n            var overbox = this.scriptChild.getOuterBBox();\n            if (this.node.attributes.get('accent')) {\n                basebox.h = Math.max(basebox.h, this.font.params.x_height * basebox.scale);\n            }\n            var u = this.getOverKU(basebox, overbox)[1];\n            var delta = (this.isLineAbove ? 0 : this.getDelta());\n            var _a = __read(this.getDeltaW([basebox, overbox], [0, delta]), 2), bw = _a[0], ow = _a[1];\n            bbox.combine(basebox, bw, 0);\n            bbox.combine(overbox, ow, u);\n            bbox.h += this.font.params.big_op_spacing5;\n            bbox.clean();\n        };\n        return class_2;\n    }(Base));\n}\nexports.CommonMoverMixin = CommonMoverMixin;\nfunction CommonMunderoverMixin(Base) {\n    return (function (_super) {\n        __extends(class_3, _super);\n        function class_3() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n            _this.stretchChildren();\n            return _this;\n        }\n        Object.defineProperty(class_3.prototype, \"underChild\", {\n            get: function () {\n                return this.childNodes[this.node.under];\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_3.prototype, \"overChild\", {\n            get: function () {\n                return this.childNodes[this.node.over];\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_3.prototype, \"subChild\", {\n            get: function () {\n                return this.underChild;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(class_3.prototype, \"supChild\", {\n            get: function () {\n                return this.overChild;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        class_3.prototype.computeBBox = function (bbox) {\n            if (this.hasMovableLimits()) {\n                _super.prototype.computeBBox.call(this, bbox);\n                return;\n            }\n            bbox.empty();\n            var overbox = this.overChild.getOuterBBox();\n            var basebox = this.baseChild.getOuterBBox();\n            var underbox = this.underChild.getOuterBBox();\n            if (this.node.attributes.get('accent')) {\n                basebox.h = Math.max(basebox.h, this.font.params.x_height * basebox.scale);\n            }\n            var u = this.getOverKU(basebox, overbox)[1];\n            var v = this.getUnderKV(basebox, underbox)[1];\n            var delta = this.getDelta();\n            var _a = __read(this.getDeltaW([basebox, underbox, overbox], [0, this.isLineBelow ? 0 : -delta, this.isLineAbove ? 0 : delta]), 3), bw = _a[0], uw = _a[1], ow = _a[2];\n            bbox.combine(basebox, bw, 0);\n            bbox.combine(overbox, ow, u);\n            bbox.combine(underbox, uw, v);\n            var z = this.font.params.big_op_spacing5;\n            bbox.h += z;\n            bbox.d += z;\n            bbox.clean();\n        };\n        return class_3;\n    }(Base));\n}\nexports.CommonMunderoverMixin = CommonMunderoverMixin;\n//# sourceMappingURL=munderover.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonScriptbaseMixin = void 0;\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nfunction CommonScriptbaseMixin(Base) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(class_1, _super);\n            function class_1() {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n                _this.baseScale = 1;\n                _this.baseIc = 0;\n                _this.baseRemoveIc = false;\n                _this.baseIsChar = false;\n                _this.baseHasAccentOver = null;\n                _this.baseHasAccentUnder = null;\n                _this.isLineAbove = false;\n                _this.isLineBelow = false;\n                _this.isMathAccent = false;\n                var core = _this.baseCore = _this.getBaseCore();\n                if (!core)\n                    return _this;\n                _this.setBaseAccentsFor(core);\n                _this.baseScale = _this.getBaseScale();\n                _this.baseIc = _this.getBaseIc();\n                _this.baseIsChar = _this.isCharBase();\n                _this.isMathAccent = _this.baseIsChar &&\n                    (_this.scriptChild && !!_this.scriptChild.coreMO().node.getProperty('mathaccent'));\n                _this.checkLineAccents();\n                _this.baseRemoveIc = !_this.isLineAbove && !_this.isLineBelow &&\n                    (!_this.constructor.useIC || _this.isMathAccent);\n                return _this;\n            }\n            Object.defineProperty(class_1.prototype, \"baseChild\", {\n                get: function () {\n                    return this.childNodes[this.node.base];\n                },\n                enumerable: false,\n                configurable: true\n            });\n            Object.defineProperty(class_1.prototype, \"scriptChild\", {\n                get: function () {\n                    return this.childNodes[1];\n                },\n                enumerable: false,\n                configurable: true\n            });\n            class_1.prototype.getBaseCore = function () {\n                var core = this.getSemanticBase() || this.childNodes[0];\n                while (core &&\n                    ((core.childNodes.length === 1 &&\n                        (core.node.isKind('mrow') ||\n                            (core.node.isKind('TeXAtom') && core.node.texClass !== MmlNode_js_1.TEXCLASS.VCENTER) ||\n                            core.node.isKind('mstyle') || core.node.isKind('mpadded') ||\n                            core.node.isKind('mphantom') || core.node.isKind('semantics'))) ||\n                        (core.node.isKind('munderover') && core.isMathAccent))) {\n                    this.setBaseAccentsFor(core);\n                    core = core.childNodes[0];\n                }\n                if (!core) {\n                    this.baseHasAccentOver = this.baseHasAccentUnder = false;\n                }\n                return core || this.childNodes[0];\n            };\n            class_1.prototype.setBaseAccentsFor = function (core) {\n                if (core.node.isKind('munderover')) {\n                    if (this.baseHasAccentOver === null) {\n                        this.baseHasAccentOver = !!core.node.attributes.get('accent');\n                    }\n                    if (this.baseHasAccentUnder === null) {\n                        this.baseHasAccentUnder = !!core.node.attributes.get('accentunder');\n                    }\n                }\n            };\n            class_1.prototype.getSemanticBase = function () {\n                var fence = this.node.attributes.getExplicit('data-semantic-fencepointer');\n                return this.getBaseFence(this.baseChild, fence);\n            };\n            class_1.prototype.getBaseFence = function (fence, id) {\n                var e_1, _a;\n                if (!fence || !fence.node.attributes || !id) {\n                    return null;\n                }\n                if (fence.node.attributes.getExplicit('data-semantic-id') === id) {\n                    return fence;\n                }\n                try {\n                    for (var _b = __values(fence.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                        var child = _c.value;\n                        var result = this.getBaseFence(child, id);\n                        if (result) {\n                            return result;\n                        }\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n                return null;\n            };\n            class_1.prototype.getBaseScale = function () {\n                var child = this.baseCore;\n                var scale = 1;\n                while (child && child !== this) {\n                    var bbox = child.getOuterBBox();\n                    scale *= bbox.rscale;\n                    child = child.parent;\n                }\n                return scale;\n            };\n            class_1.prototype.getBaseIc = function () {\n                return this.baseCore.getOuterBBox().ic * this.baseScale;\n            };\n            class_1.prototype.getAdjustedIc = function () {\n                var bbox = this.baseCore.getOuterBBox();\n                return (bbox.ic ? 1.05 * bbox.ic + .05 : 0) * this.baseScale;\n            };\n            class_1.prototype.isCharBase = function () {\n                var base = this.baseCore;\n                return (((base.node.isKind('mo') && base.size === null) ||\n                    base.node.isKind('mi') || base.node.isKind('mn')) &&\n                    base.bbox.rscale === 1 && Array.from(base.getText()).length === 1);\n            };\n            class_1.prototype.checkLineAccents = function () {\n                if (!this.node.isKind('munderover'))\n                    return;\n                if (this.node.isKind('mover')) {\n                    this.isLineAbove = this.isLineAccent(this.scriptChild);\n                }\n                else if (this.node.isKind('munder')) {\n                    this.isLineBelow = this.isLineAccent(this.scriptChild);\n                }\n                else {\n                    var mml = this;\n                    this.isLineAbove = this.isLineAccent(mml.overChild);\n                    this.isLineBelow = this.isLineAccent(mml.underChild);\n                }\n            };\n            class_1.prototype.isLineAccent = function (script) {\n                var node = script.coreMO().node;\n                return (node.isToken && node.getText() === '\\u2015');\n            };\n            class_1.prototype.getBaseWidth = function () {\n                var bbox = this.baseChild.getOuterBBox();\n                return bbox.w * bbox.rscale - (this.baseRemoveIc ? this.baseIc : 0) + this.font.params.extra_ic;\n            };\n            class_1.prototype.computeBBox = function (bbox, recompute) {\n                if (recompute === void 0) { recompute = false; }\n                var w = this.getBaseWidth();\n                var _a = __read(this.getOffset(), 2), x = _a[0], y = _a[1];\n                bbox.append(this.baseChild.getOuterBBox());\n                bbox.combine(this.scriptChild.getOuterBBox(), w + x, y);\n                bbox.w += this.font.params.scriptspace;\n                bbox.clean();\n                this.setChildPWidths(recompute);\n            };\n            class_1.prototype.getOffset = function () {\n                return [0, 0];\n            };\n            class_1.prototype.baseCharZero = function (n) {\n                var largeop = !!this.baseCore.node.attributes.get('largeop');\n                var scale = this.baseScale;\n                return (this.baseIsChar && !largeop && scale === 1 ? 0 : n);\n            };\n            class_1.prototype.getV = function () {\n                var bbox = this.baseCore.getOuterBBox();\n                var sbox = this.scriptChild.getOuterBBox();\n                var tex = this.font.params;\n                var subscriptshift = this.length2em(this.node.attributes.get('subscriptshift'), tex.sub1);\n                return Math.max(this.baseCharZero(bbox.d * this.baseScale + tex.sub_drop * sbox.rscale), subscriptshift, sbox.h * sbox.rscale - (4 / 5) * tex.x_height);\n            };\n            class_1.prototype.getU = function () {\n                var bbox = this.baseCore.getOuterBBox();\n                var sbox = this.scriptChild.getOuterBBox();\n                var tex = this.font.params;\n                var attr = this.node.attributes.getList('displaystyle', 'superscriptshift');\n                var prime = this.node.getProperty('texprimestyle');\n                var p = prime ? tex.sup3 : (attr.displaystyle ? tex.sup1 : tex.sup2);\n                var superscriptshift = this.length2em(attr.superscriptshift, p);\n                return Math.max(this.baseCharZero(bbox.h * this.baseScale - tex.sup_drop * sbox.rscale), superscriptshift, sbox.d * sbox.rscale + (1 / 4) * tex.x_height);\n            };\n            class_1.prototype.hasMovableLimits = function () {\n                var display = this.node.attributes.get('displaystyle');\n                var mo = this.baseChild.coreMO().node;\n                return (!display && !!mo.attributes.get('movablelimits'));\n            };\n            class_1.prototype.getOverKU = function (basebox, overbox) {\n                var accent = this.node.attributes.get('accent');\n                var tex = this.font.params;\n                var d = overbox.d * overbox.rscale;\n                var t = tex.rule_thickness * tex.separation_factor;\n                var delta = (this.baseHasAccentOver ? t : 0);\n                var T = (this.isLineAbove ? 3 * tex.rule_thickness : t);\n                var k = (accent ? T : Math.max(tex.big_op_spacing1, tex.big_op_spacing3 - Math.max(0, d))) - delta;\n                return [k, basebox.h * basebox.rscale + k + d];\n            };\n            class_1.prototype.getUnderKV = function (basebox, underbox) {\n                var accent = this.node.attributes.get('accentunder');\n                var tex = this.font.params;\n                var h = underbox.h * underbox.rscale;\n                var t = tex.rule_thickness * tex.separation_factor;\n                var delta = (this.baseHasAccentUnder ? t : 0);\n                var T = (this.isLineBelow ? 3 * tex.rule_thickness : t);\n                var k = (accent ? T : Math.max(tex.big_op_spacing2, tex.big_op_spacing4 - h)) - delta;\n                return [k, -(basebox.d * basebox.rscale + k + h)];\n            };\n            class_1.prototype.getDeltaW = function (boxes, delta) {\n                var e_2, _a, e_3, _b;\n                if (delta === void 0) { delta = [0, 0, 0]; }\n                var align = this.node.attributes.get('align');\n                var widths = boxes.map(function (box) { return box.w * box.rscale; });\n                widths[0] -= (this.baseRemoveIc && !this.baseCore.node.attributes.get('largeop') ? this.baseIc : 0);\n                var w = Math.max.apply(Math, __spreadArray([], __read(widths), false));\n                var dw = [];\n                var m = 0;\n                try {\n                    for (var _c = __values(widths.keys()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                        var i = _d.value;\n                        dw[i] = (align === 'center' ? (w - widths[i]) / 2 :\n                            align === 'right' ? w - widths[i] : 0) + delta[i];\n                        if (dw[i] < m) {\n                            m = -dw[i];\n                        }\n                    }\n                }\n                catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                finally {\n                    try {\n                        if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                    }\n                    finally { if (e_2) throw e_2.error; }\n                }\n                if (m) {\n                    try {\n                        for (var _e = __values(dw.keys()), _f = _e.next(); !_f.done; _f = _e.next()) {\n                            var i = _f.value;\n                            dw[i] += m;\n                        }\n                    }\n                    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                    finally {\n                        try {\n                            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                        }\n                        finally { if (e_3) throw e_3.error; }\n                    }\n                }\n                [1, 2].map(function (i) { return dw[i] += (boxes[i] ? boxes[i].dx * boxes[0].scale : 0); });\n                return dw;\n            };\n            class_1.prototype.getDelta = function (noskew) {\n                if (noskew === void 0) { noskew = false; }\n                var accent = this.node.attributes.get('accent');\n                var _a = this.baseCore.getOuterBBox(), sk = _a.sk, ic = _a.ic;\n                return ((accent && !noskew ? sk : 0) + this.font.skewIcFactor * ic) * this.baseScale;\n            };\n            class_1.prototype.stretchChildren = function () {\n                var e_4, _a, e_5, _b, e_6, _c;\n                var stretchy = [];\n                try {\n                    for (var _d = __values(this.childNodes), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var child = _e.value;\n                        if (child.canStretch(2)) {\n                            stretchy.push(child);\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n                var count = stretchy.length;\n                var nodeCount = this.childNodes.length;\n                if (count && nodeCount > 1) {\n                    var W = 0;\n                    var all = (count > 1 && count === nodeCount);\n                    try {\n                        for (var _f = __values(this.childNodes), _g = _f.next(); !_g.done; _g = _f.next()) {\n                            var child = _g.value;\n                            var noStretch = (child.stretch.dir === 0);\n                            if (all || noStretch) {\n                                var _h = child.getOuterBBox(noStretch), w = _h.w, rscale = _h.rscale;\n                                if (w * rscale > W)\n                                    W = w * rscale;\n                            }\n                        }\n                    }\n                    catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                    finally {\n                        try {\n                            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                        }\n                        finally { if (e_5) throw e_5.error; }\n                    }\n                    try {\n                        for (var stretchy_1 = __values(stretchy), stretchy_1_1 = stretchy_1.next(); !stretchy_1_1.done; stretchy_1_1 = stretchy_1.next()) {\n                            var child = stretchy_1_1.value;\n                            child.coreMO().getStretchedVariant([W / child.bbox.rscale]);\n                        }\n                    }\n                    catch (e_6_1) { e_6 = { error: e_6_1 }; }\n                    finally {\n                        try {\n                            if (stretchy_1_1 && !stretchy_1_1.done && (_c = stretchy_1.return)) _c.call(stretchy_1);\n                        }\n                        finally { if (e_6) throw e_6.error; }\n                    }\n                }\n            };\n            return class_1;\n        }(Base)),\n        _a.useIC = true,\n        _a;\n}\nexports.CommonScriptbaseMixin = CommonScriptbaseMixin;\n//# sourceMappingURL=scriptbase.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonSemanticsMixin = void 0;\nfunction CommonSemanticsMixin(Base) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.computeBBox = function (bbox, _recompute) {\n            if (_recompute === void 0) { _recompute = false; }\n            if (this.childNodes.length) {\n                var _a = this.childNodes[0].getBBox(), w = _a.w, h = _a.h, d = _a.d;\n                bbox.w = w;\n                bbox.h = h;\n                bbox.d = d;\n            }\n        };\n        return class_1;\n    }(Base));\n}\nexports.CommonSemanticsMixin = CommonSemanticsMixin;\n//# sourceMappingURL=semantics.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BBox = void 0;\nvar lengths_js_1 = require(\"./lengths.js\");\nvar BBox = (function () {\n    function BBox(def) {\n        if (def === void 0) { def = { w: 0, h: -lengths_js_1.BIGDIMEN, d: -lengths_js_1.BIGDIMEN }; }\n        this.w = def.w || 0;\n        this.h = ('h' in def ? def.h : -lengths_js_1.BIGDIMEN);\n        this.d = ('d' in def ? def.d : -lengths_js_1.BIGDIMEN);\n        this.L = this.R = this.ic = this.sk = this.dx = 0;\n        this.scale = this.rscale = 1;\n        this.pwidth = '';\n    }\n    BBox.zero = function () {\n        return new BBox({ h: 0, d: 0, w: 0 });\n    };\n    BBox.empty = function () {\n        return new BBox();\n    };\n    BBox.prototype.empty = function () {\n        this.w = 0;\n        this.h = this.d = -lengths_js_1.BIGDIMEN;\n        return this;\n    };\n    BBox.prototype.clean = function () {\n        if (this.w === -lengths_js_1.BIGDIMEN)\n            this.w = 0;\n        if (this.h === -lengths_js_1.BIGDIMEN)\n            this.h = 0;\n        if (this.d === -lengths_js_1.BIGDIMEN)\n            this.d = 0;\n    };\n    BBox.prototype.rescale = function (scale) {\n        this.w *= scale;\n        this.h *= scale;\n        this.d *= scale;\n    };\n    BBox.prototype.combine = function (cbox, x, y) {\n        if (x === void 0) { x = 0; }\n        if (y === void 0) { y = 0; }\n        var rscale = cbox.rscale;\n        var w = x + rscale * (cbox.w + cbox.L + cbox.R);\n        var h = y + rscale * cbox.h;\n        var d = rscale * cbox.d - y;\n        if (w > this.w)\n            this.w = w;\n        if (h > this.h)\n            this.h = h;\n        if (d > this.d)\n            this.d = d;\n    };\n    BBox.prototype.append = function (cbox) {\n        var scale = cbox.rscale;\n        this.w += scale * (cbox.w + cbox.L + cbox.R);\n        if (scale * cbox.h > this.h) {\n            this.h = scale * cbox.h;\n        }\n        if (scale * cbox.d > this.d) {\n            this.d = scale * cbox.d;\n        }\n    };\n    BBox.prototype.updateFrom = function (cbox) {\n        this.h = cbox.h;\n        this.d = cbox.d;\n        this.w = cbox.w;\n        if (cbox.pwidth) {\n            this.pwidth = cbox.pwidth;\n        }\n    };\n    BBox.fullWidth = '100%';\n    BBox.StyleAdjust = [\n        ['borderTopWidth', 'h'],\n        ['borderRightWidth', 'w'],\n        ['borderBottomWidth', 'd'],\n        ['borderLeftWidth', 'w', 0],\n        ['paddingTop', 'h'],\n        ['paddingRight', 'w'],\n        ['paddingBottom', 'd'],\n        ['paddingLeft', 'w', 0]\n    ];\n    return BBox;\n}());\nexports.BBox = BBox;\n//# sourceMappingURL=BBox.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FunctionList = void 0;\nvar PrioritizedList_js_1 = require(\"./PrioritizedList.js\");\nvar FunctionList = (function (_super) {\n    __extends(FunctionList, _super);\n    function FunctionList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FunctionList.prototype.execute = function () {\n        var e_1, _a;\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                var result = item.item.apply(item, __spreadArray([], __read(data), false));\n                if (result === false) {\n                    return false;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return true;\n    };\n    FunctionList.prototype.asyncExecute = function () {\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        var i = -1;\n        var items = this.items;\n        return new Promise(function (ok, fail) {\n            (function execute() {\n                var _a;\n                while (++i < items.length) {\n                    var result = (_a = items[i]).item.apply(_a, __spreadArray([], __read(data), false));\n                    if (result instanceof Promise) {\n                        result.then(execute).catch(function (err) { return fail(err); });\n                        return;\n                    }\n                    if (result === false) {\n                        ok(false);\n                        return;\n                    }\n                }\n                ok(true);\n            })();\n        });\n    };\n    return FunctionList;\n}(PrioritizedList_js_1.PrioritizedList));\nexports.FunctionList = FunctionList;\n//# sourceMappingURL=FunctionList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PrioritizedList = void 0;\nvar PrioritizedList = (function () {\n    function PrioritizedList() {\n        this.items = [];\n        this.items = [];\n    }\n    PrioritizedList.prototype[Symbol.iterator] = function () {\n        var i = 0;\n        var items = this.items;\n        return {\n            next: function () {\n                return { value: items[i++], done: (i > items.length) };\n            }\n        };\n    };\n    PrioritizedList.prototype.add = function (item, priority) {\n        if (priority === void 0) { priority = PrioritizedList.DEFAULTPRIORITY; }\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && priority < this.items[i].priority);\n        this.items.splice(i + 1, 0, { item: item, priority: priority });\n        return item;\n    };\n    PrioritizedList.prototype.remove = function (item) {\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && this.items[i].item !== item);\n        if (i >= 0) {\n            this.items.splice(i, 1);\n        }\n    };\n    PrioritizedList.DEFAULTPRIORITY = 5;\n    return PrioritizedList;\n}());\nexports.PrioritizedList = PrioritizedList;\n//# sourceMappingURL=PrioritizedList.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CssStyles = void 0;\nvar CssStyles = (function () {\n    function CssStyles(styles) {\n        if (styles === void 0) { styles = null; }\n        this.styles = {};\n        this.addStyles(styles);\n    }\n    Object.defineProperty(CssStyles.prototype, \"cssText\", {\n        get: function () {\n            return this.getStyleString();\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CssStyles.prototype.addStyles = function (styles) {\n        var e_1, _a;\n        if (!styles)\n            return;\n        try {\n            for (var _b = __values(Object.keys(styles)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var style = _c.value;\n                if (!this.styles[style]) {\n                    this.styles[style] = {};\n                }\n                Object.assign(this.styles[style], styles[style]);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    CssStyles.prototype.removeStyles = function () {\n        var e_2, _a;\n        var selectors = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            selectors[_i] = arguments[_i];\n        }\n        try {\n            for (var selectors_1 = __values(selectors), selectors_1_1 = selectors_1.next(); !selectors_1_1.done; selectors_1_1 = selectors_1.next()) {\n                var selector = selectors_1_1.value;\n                delete this.styles[selector];\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (selectors_1_1 && !selectors_1_1.done && (_a = selectors_1.return)) _a.call(selectors_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    CssStyles.prototype.clear = function () {\n        this.styles = {};\n    };\n    CssStyles.prototype.getStyleString = function () {\n        return this.getStyleRules().join('\\n\\n');\n    };\n    CssStyles.prototype.getStyleRules = function () {\n        var e_3, _a;\n        var selectors = Object.keys(this.styles);\n        var defs = new Array(selectors.length);\n        var i = 0;\n        try {\n            for (var selectors_2 = __values(selectors), selectors_2_1 = selectors_2.next(); !selectors_2_1.done; selectors_2_1 = selectors_2.next()) {\n                var selector = selectors_2_1.value;\n                defs[i++] = selector + ' {\\n' + this.getStyleDefString(this.styles[selector]) + '\\n}';\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (selectors_2_1 && !selectors_2_1.done && (_a = selectors_2.return)) _a.call(selectors_2);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return defs;\n    };\n    CssStyles.prototype.getStyleDefString = function (styles) {\n        var e_4, _a;\n        var properties = Object.keys(styles);\n        var values = new Array(properties.length);\n        var i = 0;\n        try {\n            for (var properties_1 = __values(properties), properties_1_1 = properties_1.next(); !properties_1_1.done; properties_1_1 = properties_1.next()) {\n                var property = properties_1_1.value;\n                values[i++] = '  ' + property + ': ' + styles[property] + ';';\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (properties_1_1 && !properties_1_1.done && (_a = properties_1.return)) _a.call(properties_1);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return values.join('\\n');\n    };\n    return CssStyles;\n}());\nexports.CssStyles = CssStyles;\n//# sourceMappingURL=StyleList.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Styles = void 0;\nvar TRBL = ['top', 'right', 'bottom', 'left'];\nvar WSC = ['width', 'style', 'color'];\nfunction splitSpaces(text) {\n    var parts = text.split(/((?:'[^']*'|\"[^\"]*\"|,[\\s\\n]|[^\\s\\n])*)/g);\n    var split = [];\n    while (parts.length > 1) {\n        parts.shift();\n        split.push(parts.shift());\n    }\n    return split;\n}\nfunction splitTRBL(name) {\n    var e_1, _a;\n    var parts = splitSpaces(this.styles[name]);\n    if (parts.length === 0) {\n        parts.push('');\n    }\n    if (parts.length === 1) {\n        parts.push(parts[0]);\n    }\n    if (parts.length === 2) {\n        parts.push(parts[0]);\n    }\n    if (parts.length === 3) {\n        parts.push(parts[1]);\n    }\n    try {\n        for (var _b = __values(Styles.connect[name].children), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var child = _c.value;\n            this.setStyle(this.childName(name, child), parts.shift());\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nfunction combineTRBL(name) {\n    var e_2, _a;\n    var children = Styles.connect[name].children;\n    var parts = [];\n    try {\n        for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {\n            var child = children_1_1.value;\n            var part = this.styles[name + '-' + child];\n            if (!part) {\n                delete this.styles[name];\n                return;\n            }\n            parts.push(part);\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (children_1_1 && !children_1_1.done && (_a = children_1.return)) _a.call(children_1);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    if (parts[3] === parts[1]) {\n        parts.pop();\n        if (parts[2] === parts[0]) {\n            parts.pop();\n            if (parts[1] === parts[0]) {\n                parts.pop();\n            }\n        }\n    }\n    this.styles[name] = parts.join(' ');\n}\nfunction splitSame(name) {\n    var e_3, _a;\n    try {\n        for (var _b = __values(Styles.connect[name].children), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var child = _c.value;\n            this.setStyle(this.childName(name, child), this.styles[name]);\n        }\n    }\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_3) throw e_3.error; }\n    }\n}\nfunction combineSame(name) {\n    var e_4, _a;\n    var children = __spreadArray([], __read(Styles.connect[name].children), false);\n    var value = this.styles[this.childName(name, children.shift())];\n    try {\n        for (var children_2 = __values(children), children_2_1 = children_2.next(); !children_2_1.done; children_2_1 = children_2.next()) {\n            var child = children_2_1.value;\n            if (this.styles[this.childName(name, child)] !== value) {\n                delete this.styles[name];\n                return;\n            }\n        }\n    }\n    catch (e_4_1) { e_4 = { error: e_4_1 }; }\n    finally {\n        try {\n            if (children_2_1 && !children_2_1.done && (_a = children_2.return)) _a.call(children_2);\n        }\n        finally { if (e_4) throw e_4.error; }\n    }\n    this.styles[name] = value;\n}\nvar BORDER = {\n    width: /^(?:[\\d.]+(?:[a-z]+)|thin|medium|thick|inherit|initial|unset)$/,\n    style: /^(?:none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset|inherit|initial|unset)$/\n};\nfunction splitWSC(name) {\n    var e_5, _a, e_6, _b;\n    var parts = { width: '', style: '', color: '' };\n    try {\n        for (var _c = __values(splitSpaces(this.styles[name])), _d = _c.next(); !_d.done; _d = _c.next()) {\n            var part = _d.value;\n            if (part.match(BORDER.width) && parts.width === '') {\n                parts.width = part;\n            }\n            else if (part.match(BORDER.style) && parts.style === '') {\n                parts.style = part;\n            }\n            else {\n                parts.color = part;\n            }\n        }\n    }\n    catch (e_5_1) { e_5 = { error: e_5_1 }; }\n    finally {\n        try {\n            if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n        }\n        finally { if (e_5) throw e_5.error; }\n    }\n    try {\n        for (var _e = __values(Styles.connect[name].children), _f = _e.next(); !_f.done; _f = _e.next()) {\n            var child = _f.value;\n            this.setStyle(this.childName(name, child), parts[child]);\n        }\n    }\n    catch (e_6_1) { e_6 = { error: e_6_1 }; }\n    finally {\n        try {\n            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n        }\n        finally { if (e_6) throw e_6.error; }\n    }\n}\nfunction combineWSC(name) {\n    var e_7, _a;\n    var parts = [];\n    try {\n        for (var _b = __values(Styles.connect[name].children), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var child = _c.value;\n            var value = this.styles[this.childName(name, child)];\n            if (value) {\n                parts.push(value);\n            }\n        }\n    }\n    catch (e_7_1) { e_7 = { error: e_7_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_7) throw e_7.error; }\n    }\n    if (parts.length) {\n        this.styles[name] = parts.join(' ');\n    }\n    else {\n        delete this.styles[name];\n    }\n}\nvar FONT = {\n    style: /^(?:normal|italic|oblique|inherit|initial|unset)$/,\n    variant: new RegExp('^(?:' +\n        ['normal|none',\n            'inherit|initial|unset',\n            'common-ligatures|no-common-ligatures',\n            'discretionary-ligatures|no-discretionary-ligatures',\n            'historical-ligatures|no-historical-ligatures',\n            'contextual|no-contextual',\n            '(?:stylistic|character-variant|swash|ornaments|annotation)\\\\([^)]*\\\\)',\n            'small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps',\n            'lining-nums|oldstyle-nums|proportional-nums|tabular-nums',\n            'diagonal-fractions|stacked-fractions',\n            'ordinal|slashed-zero',\n            'jis78|jis83|jis90|jis04|simplified|traditional',\n            'full-width|proportional-width',\n            'ruby'].join('|') + ')$'),\n    weight: /^(?:normal|bold|bolder|lighter|[1-9]00|inherit|initial|unset)$/,\n    stretch: new RegExp('^(?:' +\n        ['normal',\n            '(?:(?:ultra|extra|semi)-)?condensed',\n            '(?:(?:semi|extra|ulta)-)?expanded',\n            'inherit|initial|unset'].join('|') + ')$'),\n    size: new RegExp('^(?:' +\n        ['xx-small|x-small|small|medium|large|x-large|xx-large|larger|smaller',\n            '[\\d.]+%|[\\d.]+[a-z]+',\n            'inherit|initial|unset'].join('|') + ')' +\n        '(?:\\/(?:normal|[\\d.\\+](?:%|[a-z]+)?))?$')\n};\nfunction splitFont(name) {\n    var e_8, _a, e_9, _b;\n    var parts = splitSpaces(this.styles[name]);\n    var value = {\n        style: '', variant: [], weight: '', stretch: '',\n        size: '', family: '', 'line-height': ''\n    };\n    try {\n        for (var parts_1 = __values(parts), parts_1_1 = parts_1.next(); !parts_1_1.done; parts_1_1 = parts_1.next()) {\n            var part = parts_1_1.value;\n            value.family = part;\n            try {\n                for (var _c = (e_9 = void 0, __values(Object.keys(FONT))), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var name_1 = _d.value;\n                    if ((Array.isArray(value[name_1]) || value[name_1] === '') && part.match(FONT[name_1])) {\n                        if (name_1 === 'size') {\n                            var _e = __read(part.split(/\\//), 2), size = _e[0], height = _e[1];\n                            value[name_1] = size;\n                            if (height) {\n                                value['line-height'] = height;\n                            }\n                        }\n                        else if (value.size === '') {\n                            if (Array.isArray(value[name_1])) {\n                                value[name_1].push(part);\n                            }\n                            else {\n                                value[name_1] = part;\n                            }\n                        }\n                    }\n                }\n            }\n            catch (e_9_1) { e_9 = { error: e_9_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n                }\n                finally { if (e_9) throw e_9.error; }\n            }\n        }\n    }\n    catch (e_8_1) { e_8 = { error: e_8_1 }; }\n    finally {\n        try {\n            if (parts_1_1 && !parts_1_1.done && (_a = parts_1.return)) _a.call(parts_1);\n        }\n        finally { if (e_8) throw e_8.error; }\n    }\n    saveFontParts(name, value);\n    delete this.styles[name];\n}\nfunction saveFontParts(name, value) {\n    var e_10, _a;\n    try {\n        for (var _b = __values(Styles.connect[name].children), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var child = _c.value;\n            var cname = this.childName(name, child);\n            if (Array.isArray(value[child])) {\n                var values = value[child];\n                if (values.length) {\n                    this.styles[cname] = values.join(' ');\n                }\n            }\n            else if (value[child] !== '') {\n                this.styles[cname] = value[child];\n            }\n        }\n    }\n    catch (e_10_1) { e_10 = { error: e_10_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_10) throw e_10.error; }\n    }\n}\nfunction combineFont(_name) { }\nvar Styles = (function () {\n    function Styles(cssText) {\n        if (cssText === void 0) { cssText = ''; }\n        this.parse(cssText);\n    }\n    Object.defineProperty(Styles.prototype, \"cssText\", {\n        get: function () {\n            var e_11, _a;\n            var styles = [];\n            try {\n                for (var _b = __values(Object.keys(this.styles)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var name_2 = _c.value;\n                    var parent_1 = this.parentName(name_2);\n                    if (!this.styles[parent_1]) {\n                        styles.push(name_2 + ': ' + this.styles[name_2] + ';');\n                    }\n                }\n            }\n            catch (e_11_1) { e_11 = { error: e_11_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_11) throw e_11.error; }\n            }\n            return styles.join(' ');\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Styles.prototype.set = function (name, value) {\n        name = this.normalizeName(name);\n        this.setStyle(name, value);\n        if (Styles.connect[name] && !Styles.connect[name].combine) {\n            this.combineChildren(name);\n            delete this.styles[name];\n        }\n        while (name.match(/-/)) {\n            name = this.parentName(name);\n            if (!Styles.connect[name])\n                break;\n            Styles.connect[name].combine.call(this, name);\n        }\n    };\n    Styles.prototype.get = function (name) {\n        name = this.normalizeName(name);\n        return (this.styles.hasOwnProperty(name) ? this.styles[name] : '');\n    };\n    Styles.prototype.setStyle = function (name, value) {\n        this.styles[name] = value;\n        if (Styles.connect[name] && Styles.connect[name].children) {\n            Styles.connect[name].split.call(this, name);\n        }\n        if (value === '') {\n            delete this.styles[name];\n        }\n    };\n    Styles.prototype.combineChildren = function (name) {\n        var e_12, _a;\n        var parent = this.parentName(name);\n        try {\n            for (var _b = __values(Styles.connect[name].children), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                var cname = this.childName(parent, child);\n                Styles.connect[cname].combine.call(this, cname);\n            }\n        }\n        catch (e_12_1) { e_12 = { error: e_12_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_12) throw e_12.error; }\n        }\n    };\n    Styles.prototype.parentName = function (name) {\n        var parent = name.replace(/-[^-]*$/, '');\n        return (name === parent ? '' : parent);\n    };\n    Styles.prototype.childName = function (name, child) {\n        if (child.match(/-/)) {\n            return child;\n        }\n        if (Styles.connect[name] && !Styles.connect[name].combine) {\n            child += name.replace(/.*-/, '-');\n            name = this.parentName(name);\n        }\n        return name + '-' + child;\n    };\n    Styles.prototype.normalizeName = function (name) {\n        return name.replace(/[A-Z]/g, function (c) { return '-' + c.toLowerCase(); });\n    };\n    Styles.prototype.parse = function (cssText) {\n        if (cssText === void 0) { cssText = ''; }\n        var PATTERN = this.constructor.pattern;\n        this.styles = {};\n        var parts = cssText.replace(PATTERN.comment, '').split(PATTERN.style);\n        while (parts.length > 1) {\n            var _a = __read(parts.splice(0, 3), 3), space = _a[0], name_3 = _a[1], value = _a[2];\n            if (space.match(/[^\\s\\n]/))\n                return;\n            this.set(name_3, value);\n        }\n    };\n    Styles.pattern = {\n        style: /([-a-z]+)[\\s\\n]*:[\\s\\n]*((?:'[^']*'|\"[^\"]*\"|\\n|.)*?)[\\s\\n]*(?:;|$)/g,\n        comment: /\\/\\*[^]*?\\*\\//g\n    };\n    Styles.connect = {\n        padding: {\n            children: TRBL,\n            split: splitTRBL,\n            combine: combineTRBL\n        },\n        border: {\n            children: TRBL,\n            split: splitSame,\n            combine: combineSame\n        },\n        'border-top': {\n            children: WSC,\n            split: splitWSC,\n            combine: combineWSC\n        },\n        'border-right': {\n            children: WSC,\n            split: splitWSC,\n            combine: combineWSC\n        },\n        'border-bottom': {\n            children: WSC,\n            split: splitWSC,\n            combine: combineWSC\n        },\n        'border-left': {\n            children: WSC,\n            split: splitWSC,\n            combine: combineWSC\n        },\n        'border-width': {\n            children: TRBL,\n            split: splitTRBL,\n            combine: null\n        },\n        'border-style': {\n            children: TRBL,\n            split: splitTRBL,\n            combine: null\n        },\n        'border-color': {\n            children: TRBL,\n            split: splitTRBL,\n            combine: null\n        },\n        font: {\n            children: ['style', 'variant', 'weight', 'stretch', 'line-height', 'size', 'family'],\n            split: splitFont,\n            combine: combineFont\n        }\n    };\n    return Styles;\n}());\nexports.Styles = Styles;\n//# sourceMappingURL=Styles.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.max = exports.sum = void 0;\nfunction sum(A) {\n    return A.reduce(function (a, b) { return a + b; }, 0);\n}\nexports.sum = sum;\nfunction max(A) {\n    return A.reduce(function (a, b) { return Math.max(a, b); }, 0);\n}\nexports.max = max;\n//# sourceMappingURL=numeric.js.map"], "names": [], "sourceRoot": ""}