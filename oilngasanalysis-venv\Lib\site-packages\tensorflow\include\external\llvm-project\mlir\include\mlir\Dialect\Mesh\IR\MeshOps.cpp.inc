/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Op Definitions                                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|* From: MeshOps.td                                                           *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifdef GET_OP_LIST
#undef GET_OP_LIST

::mlir::mesh::AllGatherOp,
::mlir::mesh::AllReduceOp,
::mlir::mesh::AllSliceOp,
::mlir::mesh::AllToAllOp,
::mlir::mesh::BroadcastOp,
::mlir::mesh::GatherOp,
::mlir::mesh::MeshOp,
::mlir::mesh::MeshShapeOp,
::mlir::mesh::NeighborsLinearIndicesOp,
::mlir::mesh::ProcessLinearIndexOp,
::mlir::mesh::ProcessMultiIndexOp,
::mlir::mesh::RecvOp,
::mlir::mesh::ReduceOp,
::mlir::mesh::ReduceScatterOp,
::mlir::mesh::ScatterOp,
::mlir::mesh::SendOp,
::mlir::mesh::ShardOp,
::mlir::mesh::ShardShapeOp,
::mlir::mesh::ShardingOp,
::mlir::mesh::ShiftOp,
::mlir::mesh::UpdateHaloOp
#endif  // GET_OP_LIST

#ifdef GET_OP_CLASSES
#undef GET_OP_CLASSES


//===----------------------------------------------------------------------===//
// Local Utility Method Definitions
//===----------------------------------------------------------------------===//

namespace mlir {
namespace mesh {

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps1(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((((::llvm::isa<::mlir::TensorType>(type))) && (((::llvm::cast<::mlir::ShapedType>(type).hasRank())) && ((::llvm::cast<::mlir::ShapedType>(type).getRank() >= 1)))) && ([](::mlir::Type elementType) { return (true); }(::llvm::cast<::mlir::ShapedType>(type).getElementType())))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be non-0-ranked.tensor of any type values, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps2(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!(((::llvm::isa<::mlir::RankedTensorType>(type))) && ([](::mlir::Type elementType) { return (true); }(::llvm::cast<::mlir::ShapedType>(type).getElementType())))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be ranked tensor of any type values, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps3(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::IndexType>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be variadic of index, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps4(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::IndexType>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be index, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps5(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::mesh::ShardingType>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be sharding definition, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps6(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((type.isSignlessInteger(64)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be variadic of 64-bit signless integer, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_MeshOps7(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!(((((::llvm::isa<::mlir::MemRefType>(type))) && ([](::mlir::Type elementType) { return (true); }(::llvm::cast<::mlir::ShapedType>(type).getElementType()))) && (((::llvm::cast<::mlir::ShapedType>(type).hasRank())) && ((::llvm::cast<::mlir::ShapedType>(type).getRank() >= 1)))) || ((((::llvm::isa<::mlir::TensorType>(type))) && (((::llvm::cast<::mlir::ShapedType>(type).hasRank())) && ((::llvm::cast<::mlir::ShapedType>(type).getRank() >= 1)))) && ([](::mlir::Type elementType) { return (true); }(::llvm::cast<::mlir::ShapedType>(type).getElementType()))))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be non-0-ranked.memref of any type values or non-0-ranked.tensor of any type values, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps1(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: flat symbol reference attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps1(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps1(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps2(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: i16 dense array attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps2(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps2(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps3(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::IntegerAttr>(attr))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(attr).getType())))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: index attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps3(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps3(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps4(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::mesh::ReductionKindAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: Reduction of an iterator/mesh dimension.";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps4(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps4(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps5(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: i64 dense array attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps5(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps5(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps6(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::StringAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: string attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps6(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps6(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps7(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::UnitAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: unit attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps7(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps7(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps8(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::mesh::MeshAxesArrayAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: ";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps8(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps8(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps9(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::IntegerAttr>(attr))) && ((::llvm::cast<::mlir::IntegerAttr>(attr).getType().isSignlessInteger(64)))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: 64-bit signless integer attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_MeshOps9(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_MeshOps9(attr, attrName, [op]() {
    return op->emitOpError();
  });
}
} // namespace mesh
} // namespace mlir
namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::AllGatherOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
AllGatherOpGenericAdaptorBase::AllGatherOpGenericAdaptorBase(AllGatherOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef AllGatherOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr AllGatherOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> AllGatherOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt AllGatherOpGenericAdaptorBase::getGatherAxis() {
  auto attr = getGatherAxisAttr();
  return attr.getValue();
}

} // namespace detail
AllGatherOpAdaptor::AllGatherOpAdaptor(AllGatherOp op) : AllGatherOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult AllGatherOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_gather_axis = getProperties().gather_axis; (void)tblgen_gather_axis;
  if (!tblgen_gather_axis) return emitError(loc, "'mesh.all_gather' op ""requires attribute 'gather_axis'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.all_gather' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.all_gather' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.all_gather' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_gather_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_gather_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_gather_axis).getType())))))
    return emitError(loc, "'mesh.all_gather' op ""attribute 'gather_axis' failed to satisfy constraint: index attribute");
  return ::mlir::success();
}

::llvm::LogicalResult AllGatherOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.gather_axis;
       auto attr = dict.get("gather_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `gather_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute AllGatherOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.gather_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("gather_axis",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code AllGatherOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.gather_axis.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> AllGatherOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "gather_axis")
      return prop.gather_axis;

    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;
  return std::nullopt;
}

void AllGatherOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "gather_axis") {
       prop.gather_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.gather_axis)>>(value);
       return;
    }

    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }
}

void AllGatherOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.gather_axis) attrs.append("gather_axis", prop.gather_axis);

    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);
}

::llvm::LogicalResult AllGatherOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getGatherAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "gather_axis", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult AllGatherOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.gather_axis)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllGatherOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.gather_axis);
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
}

::llvm::StringRef AllGatherOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> AllGatherOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt AllGatherOp::getGatherAxis() {
  auto attr = getGatherAxisAttr();
  return attr.getValue();
}

void AllGatherOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void AllGatherOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void AllGatherOp::setGatherAxis(::llvm::APInt attrValue) {
  getProperties().gather_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void AllGatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr gather_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().gather_axis = gather_axis;
  odsState.addTypes(result);
}

void AllGatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr gather_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().gather_axis = gather_axis;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllGatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt gather_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().gather_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), gather_axis);
  odsState.addTypes(result);
}

void AllGatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt gather_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().gather_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), gather_axis);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllGatherOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<AllGatherOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void AllGatherOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult AllGatherOp::verifyInvariantsImpl() {
  auto tblgen_gather_axis = getProperties().gather_axis; (void)tblgen_gather_axis;
  if (!tblgen_gather_axis) return emitOpError("requires attribute 'gather_axis'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_gather_axis, "gather_axis")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult AllGatherOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult AllGatherOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::IntegerAttr gather_axisAttr;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<AllGatherOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<AllGatherOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("gather_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(gather_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (gather_axisAttr) result.getOrAddProperties<AllGatherOp::Properties>().gather_axis = gather_axisAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllGatherOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "gather_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getGatherAxisAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("gather_axis");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void AllGatherOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::AllGatherOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::AllReduceOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
AllReduceOpGenericAdaptorBase::AllReduceOpGenericAdaptorBase(AllReduceOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef AllReduceOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr AllReduceOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> AllReduceOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::mlir::mesh::ReductionKindAttr AllReduceOpGenericAdaptorBase::getReductionAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::mesh::ReductionKindAttr>(getProperties().reduction);
  return attr;
}

::mlir::mesh::ReductionKind AllReduceOpGenericAdaptorBase::getReduction() {
  auto attr = getReductionAttr();
  return attr.getValue();
}

} // namespace detail
AllReduceOpAdaptor::AllReduceOpAdaptor(AllReduceOp op) : AllReduceOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult AllReduceOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.all_reduce' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_reduction = getProperties().reduction; (void)tblgen_reduction;

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.all_reduce' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.all_reduce' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_reduction && !((::llvm::isa<::mlir::mesh::ReductionKindAttr>(tblgen_reduction))))
    return emitError(loc, "'mesh.all_reduce' op ""attribute 'reduction' failed to satisfy constraint: Reduction of an iterator/mesh dimension.");
  return ::mlir::success();
}

::llvm::LogicalResult AllReduceOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.reduction;
       auto attr = dict.get("reduction");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `reduction` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute AllReduceOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.reduction;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("reduction",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code AllReduceOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.reduction.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> AllReduceOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "reduction")
      return prop.reduction;
  return std::nullopt;
}

void AllReduceOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "reduction") {
       prop.reduction = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.reduction)>>(value);
       return;
    }
}

void AllReduceOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.reduction) attrs.append("reduction", prop.reduction);
}

::llvm::LogicalResult AllReduceOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getReductionAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(attr, "reduction", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult AllReduceOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.reduction)))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllReduceOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);

  writer.writeOptionalAttribute(prop.reduction);
}

::llvm::StringRef AllReduceOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> AllReduceOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::mlir::mesh::ReductionKind AllReduceOp::getReduction() {
  auto attr = getReductionAttr();
  return attr.getValue();
}

void AllReduceOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void AllReduceOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void AllReduceOp::setReduction(::mlir::mesh::ReductionKind attrValue) {
  getProperties().reduction = ::mlir::mesh::ReductionKindAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void AllReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKindAttr reduction) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (reduction) {
    odsState.getOrAddProperties<Properties>().reduction = reduction;
  }
  odsState.addTypes(result);
}

void AllReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKindAttr reduction) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (reduction) {
    odsState.getOrAddProperties<Properties>().reduction = reduction;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKind reduction) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), reduction);
  odsState.addTypes(result);
}

void AllReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKind reduction) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), reduction);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllReduceOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<AllReduceOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void AllReduceOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
  if (!properties.reduction)
    properties.reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), ::mlir::mesh::ReductionKind::Sum);
}

::llvm::LogicalResult AllReduceOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_reduction = getProperties().reduction; (void)tblgen_reduction;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(*this, tblgen_reduction, "reduction")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult AllReduceOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult AllReduceOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::mesh::ReductionKindAttr reductionAttr;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<AllReduceOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<AllReduceOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (::mlir::succeeded(parser.parseOptionalKeyword("reduction"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(reductionAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (reductionAttr) result.getOrAddProperties<AllReduceOp::Properties>().reduction = reductionAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::RankedTensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::RankedTensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllReduceOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  if (getReductionAttr() != ::mlir::mesh::ReductionKindAttr::get(::mlir::OpBuilder((*this)->getContext()).getContext(), ::mlir::mesh::ReductionKind::Sum)) {
    _odsPrinter << ' ' << "reduction";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getReductionAttr());
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("reduction");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getReductionAttr();
     if(attr && (attr == ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), ::mlir::mesh::ReductionKind::Sum)))
       elidedAttrs.push_back("reduction");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::RankedTensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::RankedTensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void AllReduceOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::AllReduceOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::AllSliceOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
AllSliceOpGenericAdaptorBase::AllSliceOpGenericAdaptorBase(AllSliceOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef AllSliceOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr AllSliceOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> AllSliceOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt AllSliceOpGenericAdaptorBase::getSliceAxis() {
  auto attr = getSliceAxisAttr();
  return attr.getValue();
}

} // namespace detail
AllSliceOpAdaptor::AllSliceOpAdaptor(AllSliceOp op) : AllSliceOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult AllSliceOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.all_slice' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_slice_axis = getProperties().slice_axis; (void)tblgen_slice_axis;
  if (!tblgen_slice_axis) return emitError(loc, "'mesh.all_slice' op ""requires attribute 'slice_axis'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.all_slice' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.all_slice' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_slice_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_slice_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_slice_axis).getType())))))
    return emitError(loc, "'mesh.all_slice' op ""attribute 'slice_axis' failed to satisfy constraint: index attribute");
  return ::mlir::success();
}

::llvm::LogicalResult AllSliceOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.slice_axis;
       auto attr = dict.get("slice_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `slice_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute AllSliceOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.slice_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("slice_axis",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code AllSliceOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.slice_axis.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> AllSliceOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "slice_axis")
      return prop.slice_axis;
  return std::nullopt;
}

void AllSliceOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "slice_axis") {
       prop.slice_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.slice_axis)>>(value);
       return;
    }
}

void AllSliceOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.slice_axis) attrs.append("slice_axis", prop.slice_axis);
}

::llvm::LogicalResult AllSliceOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSliceAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "slice_axis", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult AllSliceOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.slice_axis)))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllSliceOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
  writer.writeAttribute(prop.slice_axis);
}

::llvm::StringRef AllSliceOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> AllSliceOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt AllSliceOp::getSliceAxis() {
  auto attr = getSliceAxisAttr();
  return attr.getValue();
}

void AllSliceOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void AllSliceOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void AllSliceOp::setSliceAxis(::llvm::APInt attrValue) {
  getProperties().slice_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void AllSliceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr slice_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().slice_axis = slice_axis;
  odsState.addTypes(result);
}

void AllSliceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr slice_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().slice_axis = slice_axis;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllSliceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt slice_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().slice_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), slice_axis);
  odsState.addTypes(result);
}

void AllSliceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt slice_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().slice_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), slice_axis);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllSliceOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<AllSliceOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void AllSliceOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult AllSliceOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_slice_axis = getProperties().slice_axis; (void)tblgen_slice_axis;
  if (!tblgen_slice_axis) return emitOpError("requires attribute 'slice_axis'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_slice_axis, "slice_axis")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult AllSliceOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult AllSliceOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::IntegerAttr slice_axisAttr;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<AllSliceOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<AllSliceOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("slice_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(slice_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (slice_axisAttr) result.getOrAddProperties<AllSliceOp::Properties>().slice_axis = slice_axisAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllSliceOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "slice_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getSliceAxisAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("slice_axis");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void AllSliceOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::AllSliceOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::AllToAllOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
AllToAllOpGenericAdaptorBase::AllToAllOpGenericAdaptorBase(AllToAllOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef AllToAllOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr AllToAllOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> AllToAllOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt AllToAllOpGenericAdaptorBase::getSplitAxis() {
  auto attr = getSplitAxisAttr();
  return attr.getValue();
}

::llvm::APInt AllToAllOpGenericAdaptorBase::getConcatAxis() {
  auto attr = getConcatAxisAttr();
  return attr.getValue();
}

} // namespace detail
AllToAllOpAdaptor::AllToAllOpAdaptor(AllToAllOp op) : AllToAllOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult AllToAllOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_concat_axis = getProperties().concat_axis; (void)tblgen_concat_axis;
  if (!tblgen_concat_axis) return emitError(loc, "'mesh.all_to_all' op ""requires attribute 'concat_axis'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.all_to_all' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_split_axis = getProperties().split_axis; (void)tblgen_split_axis;
  if (!tblgen_split_axis) return emitError(loc, "'mesh.all_to_all' op ""requires attribute 'split_axis'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.all_to_all' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.all_to_all' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_split_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_split_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_split_axis).getType())))))
    return emitError(loc, "'mesh.all_to_all' op ""attribute 'split_axis' failed to satisfy constraint: index attribute");

  if (tblgen_concat_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_concat_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_concat_axis).getType())))))
    return emitError(loc, "'mesh.all_to_all' op ""attribute 'concat_axis' failed to satisfy constraint: index attribute");
  return ::mlir::success();
}

::llvm::LogicalResult AllToAllOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.concat_axis;
       auto attr = dict.get("concat_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `concat_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.split_axis;
       auto attr = dict.get("split_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `split_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute AllToAllOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.concat_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("concat_axis",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.split_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("split_axis",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code AllToAllOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.concat_axis.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.split_axis.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> AllToAllOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "concat_axis")
      return prop.concat_axis;

    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "split_axis")
      return prop.split_axis;
  return std::nullopt;
}

void AllToAllOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "concat_axis") {
       prop.concat_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.concat_axis)>>(value);
       return;
    }

    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "split_axis") {
       prop.split_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.split_axis)>>(value);
       return;
    }
}

void AllToAllOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.concat_axis) attrs.append("concat_axis", prop.concat_axis);

    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.split_axis) attrs.append("split_axis", prop.split_axis);
}

::llvm::LogicalResult AllToAllOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getConcatAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "concat_axis", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSplitAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "split_axis", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult AllToAllOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.concat_axis)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.split_axis)))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllToAllOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.concat_axis);
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
  writer.writeAttribute(prop.split_axis);
}

::llvm::StringRef AllToAllOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> AllToAllOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt AllToAllOp::getSplitAxis() {
  auto attr = getSplitAxisAttr();
  return attr.getValue();
}

::llvm::APInt AllToAllOp::getConcatAxis() {
  auto attr = getConcatAxisAttr();
  return attr.getValue();
}

void AllToAllOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void AllToAllOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void AllToAllOp::setSplitAxis(::llvm::APInt attrValue) {
  getProperties().split_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void AllToAllOp::setConcatAxis(::llvm::APInt attrValue) {
  getProperties().concat_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void AllToAllOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr split_axis, ::mlir::IntegerAttr concat_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().split_axis = split_axis;
  odsState.getOrAddProperties<Properties>().concat_axis = concat_axis;
  odsState.addTypes(result);
}

void AllToAllOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr split_axis, ::mlir::IntegerAttr concat_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().split_axis = split_axis;
  odsState.getOrAddProperties<Properties>().concat_axis = concat_axis;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllToAllOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt split_axis, ::llvm::APInt concat_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().split_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), split_axis);
  odsState.getOrAddProperties<Properties>().concat_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), concat_axis);
  odsState.addTypes(result);
}

void AllToAllOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt split_axis, ::llvm::APInt concat_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().split_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), split_axis);
  odsState.getOrAddProperties<Properties>().concat_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), concat_axis);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AllToAllOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<AllToAllOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void AllToAllOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult AllToAllOp::verifyInvariantsImpl() {
  auto tblgen_concat_axis = getProperties().concat_axis; (void)tblgen_concat_axis;
  if (!tblgen_concat_axis) return emitOpError("requires attribute 'concat_axis'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_split_axis = getProperties().split_axis; (void)tblgen_split_axis;
  if (!tblgen_split_axis) return emitOpError("requires attribute 'split_axis'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_split_axis, "split_axis")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_concat_axis, "concat_axis")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult AllToAllOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult AllToAllOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::IntegerAttr split_axisAttr;
  ::mlir::IntegerAttr concat_axisAttr;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<AllToAllOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<AllToAllOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("split_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(split_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (split_axisAttr) result.getOrAddProperties<AllToAllOp::Properties>().split_axis = split_axisAttr;
  if (parser.parseKeyword("concat_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(concat_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (concat_axisAttr) result.getOrAddProperties<AllToAllOp::Properties>().concat_axis = concat_axisAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void AllToAllOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "split_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getSplitAxisAttr());
  _odsPrinter << ' ' << "concat_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getConcatAxisAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("split_axis");
  elidedAttrs.push_back("concat_axis");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void AllToAllOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::AllToAllOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::BroadcastOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
BroadcastOpGenericAdaptorBase::BroadcastOpGenericAdaptorBase(BroadcastOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> BroadcastOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef BroadcastOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr BroadcastOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> BroadcastOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::ArrayRef<int64_t> BroadcastOpGenericAdaptorBase::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

} // namespace detail
BroadcastOpAdaptor::BroadcastOpAdaptor(BroadcastOp op) : BroadcastOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult BroadcastOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.broadcast' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitError(loc, "'mesh.broadcast' op ""requires attribute 'root'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.broadcast' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.broadcast' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_root && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_root))))
    return emitError(loc, "'mesh.broadcast' op ""attribute 'root' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> BroadcastOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange BroadcastOp::getRootDynamicMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult BroadcastOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.root;
       auto attr = dict.get("root");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `root` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute BroadcastOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.root;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("root",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code BroadcastOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.root.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> BroadcastOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "root")
      return prop.root;
  return std::nullopt;
}

void BroadcastOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "root") {
       prop.root = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.root)>>(value);
       return;
    }
}

void BroadcastOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.root) attrs.append("root", prop.root);
}

::llvm::LogicalResult BroadcastOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRootAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "root", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult BroadcastOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.root)))
    return ::mlir::failure();
  return ::mlir::success();
}

void BroadcastOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
  writer.writeAttribute(prop.root);
}

::llvm::StringRef BroadcastOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> BroadcastOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::ArrayRef<int64_t> BroadcastOp::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

void BroadcastOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void BroadcastOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void BroadcastOp::setRoot(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().root = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void BroadcastOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().root = root;
  odsState.addTypes(result);
}

void BroadcastOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().root = root;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void BroadcastOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  odsState.addTypes(result);
}

void BroadcastOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void BroadcastOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<BroadcastOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void BroadcastOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult BroadcastOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitOpError("requires attribute 'root'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_root, "root")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) && (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()))))
    return emitOpError("failed to verify that all of {input, result} have same shape");
  if (!(((getElementTypeOrSelf((*this->getODSOperands(0).begin()))) == (getElementTypeOrSelf((*this->getODSResults(0).begin()))) && (getElementTypeOrSelf((*this->getODSResults(0).begin()))) == (getElementTypeOrSelf((*this->getODSOperands(0).begin()))))))
    return emitOpError("failed to verify that all of {input, result} have same element type");
  return ::mlir::success();
}

::llvm::LogicalResult BroadcastOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult BroadcastOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> root_dynamicOperands;
  ::llvm::SMLoc root_dynamicOperandsLoc;
  (void)root_dynamicOperandsLoc;
  ::mlir::DenseI64ArrayAttr rootAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<BroadcastOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<BroadcastOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("root"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    root_dynamicOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, root_dynamicOperands, rootAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<BroadcastOp::Properties>().root = rootAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(inputOperands, root_dynamicOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void BroadcastOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "root";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  printDynamicIndexList(_odsPrinter, *this, getRootDynamic(), getRootAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("root");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

void BroadcastOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::BroadcastOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::GatherOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
GatherOpGenericAdaptorBase::GatherOpGenericAdaptorBase(GatherOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> GatherOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef GatherOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr GatherOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> GatherOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt GatherOpGenericAdaptorBase::getGatherAxis() {
  auto attr = getGatherAxisAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> GatherOpGenericAdaptorBase::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

} // namespace detail
GatherOpAdaptor::GatherOpAdaptor(GatherOp op) : GatherOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GatherOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_gather_axis = getProperties().gather_axis; (void)tblgen_gather_axis;
  if (!tblgen_gather_axis) return emitError(loc, "'mesh.gather' op ""requires attribute 'gather_axis'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.gather' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitError(loc, "'mesh.gather' op ""requires attribute 'root'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.gather' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.gather' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_gather_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_gather_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_gather_axis).getType())))))
    return emitError(loc, "'mesh.gather' op ""attribute 'gather_axis' failed to satisfy constraint: index attribute");

  if (tblgen_root && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_root))))
    return emitError(loc, "'mesh.gather' op ""attribute 'root' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> GatherOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange GatherOp::getRootDynamicMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult GatherOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.gather_axis;
       auto attr = dict.get("gather_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `gather_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.root;
       auto attr = dict.get("root");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `root` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GatherOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.gather_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("gather_axis",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.root;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("root",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GatherOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.gather_axis.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.root.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GatherOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "gather_axis")
      return prop.gather_axis;

    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "root")
      return prop.root;
  return std::nullopt;
}

void GatherOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "gather_axis") {
       prop.gather_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.gather_axis)>>(value);
       return;
    }

    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "root") {
       prop.root = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.root)>>(value);
       return;
    }
}

void GatherOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.gather_axis) attrs.append("gather_axis", prop.gather_axis);

    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.root) attrs.append("root", prop.root);
}

::llvm::LogicalResult GatherOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getGatherAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "gather_axis", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRootAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "root", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GatherOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.gather_axis)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.root)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GatherOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.gather_axis);
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
  writer.writeAttribute(prop.root);
}

::llvm::StringRef GatherOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> GatherOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt GatherOp::getGatherAxis() {
  auto attr = getGatherAxisAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> GatherOp::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

void GatherOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void GatherOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void GatherOp::setGatherAxis(::llvm::APInt attrValue) {
  getProperties().gather_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void GatherOp::setRoot(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().root = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void GatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr gather_axis, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().gather_axis = gather_axis;
  odsState.getOrAddProperties<Properties>().root = root;
  odsState.addTypes(result);
}

void GatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr gather_axis, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().gather_axis = gather_axis;
  odsState.getOrAddProperties<Properties>().root = root;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt gather_axis, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().gather_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), gather_axis);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  odsState.addTypes(result);
}

void GatherOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt gather_axis, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().gather_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), gather_axis);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GatherOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GatherOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void GatherOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult GatherOp::verifyInvariantsImpl() {
  auto tblgen_gather_axis = getProperties().gather_axis; (void)tblgen_gather_axis;
  if (!tblgen_gather_axis) return emitOpError("requires attribute 'gather_axis'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitOpError("requires attribute 'root'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_gather_axis, "gather_axis")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_root, "root")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getRank()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getRank()) && (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getRank()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getRank()))))
    return emitOpError("failed to verify that all of {input, result} have same rank");
  if (!(((getElementTypeOrSelf((*this->getODSOperands(0).begin()))) == (getElementTypeOrSelf((*this->getODSResults(0).begin()))) && (getElementTypeOrSelf((*this->getODSResults(0).begin()))) == (getElementTypeOrSelf((*this->getODSOperands(0).begin()))))))
    return emitOpError("failed to verify that all of {input, result} have same element type");
  return ::mlir::success();
}

::llvm::LogicalResult GatherOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult GatherOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::IntegerAttr gather_axisAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> root_dynamicOperands;
  ::llvm::SMLoc root_dynamicOperandsLoc;
  (void)root_dynamicOperandsLoc;
  ::mlir::DenseI64ArrayAttr rootAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<GatherOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<GatherOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("gather_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(gather_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (gather_axisAttr) result.getOrAddProperties<GatherOp::Properties>().gather_axis = gather_axisAttr;
  if (parser.parseKeyword("root"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    root_dynamicOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, root_dynamicOperands, rootAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<GatherOp::Properties>().root = rootAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(inputOperands, root_dynamicOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GatherOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "gather_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getGatherAxisAttr());
  _odsPrinter << ' ' << "root";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  printDynamicIndexList(_odsPrinter, *this, getRootDynamic(), getRootAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("gather_axis");
  elidedAttrs.push_back("root");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

void GatherOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::GatherOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::MeshOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
MeshOpGenericAdaptorBase::MeshOpGenericAdaptorBase(MeshOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef MeshOpGenericAdaptorBase::getSymName() {
  auto attr = getSymNameAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> MeshOpGenericAdaptorBase::getShape() {
  auto attr = getShapeAttr();
  return attr;
}

} // namespace detail
MeshOpAdaptor::MeshOpAdaptor(MeshOp op) : MeshOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult MeshOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_shape = getProperties().shape; (void)tblgen_shape;
  if (!tblgen_shape) return emitError(loc, "'mesh.mesh' op ""requires attribute 'shape'");
  auto tblgen_sym_name = getProperties().sym_name; (void)tblgen_sym_name;
  if (!tblgen_sym_name) return emitError(loc, "'mesh.mesh' op ""requires attribute 'sym_name'");

  if (tblgen_sym_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_sym_name))))
    return emitError(loc, "'mesh.mesh' op ""attribute 'sym_name' failed to satisfy constraint: string attribute");

  if (tblgen_shape && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_shape))))
    return emitError(loc, "'mesh.mesh' op ""attribute 'shape' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

::llvm::LogicalResult MeshOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.shape;
       auto attr = dict.get("shape");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `shape` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.sym_name;
       auto attr = dict.get("sym_name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `sym_name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute MeshOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.shape;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("shape",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.sym_name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("sym_name",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code MeshOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.shape.getAsOpaquePointer()), 
    llvm::hash_value(prop.sym_name.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> MeshOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "shape")
      return prop.shape;

    if (name == "sym_name")
      return prop.sym_name;
  return std::nullopt;
}

void MeshOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "shape") {
       prop.shape = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.shape)>>(value);
       return;
    }

    if (name == "sym_name") {
       prop.sym_name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.sym_name)>>(value);
       return;
    }
}

void MeshOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.shape) attrs.append("shape", prop.shape);

    if (prop.sym_name) attrs.append("sym_name", prop.sym_name);
}

::llvm::LogicalResult MeshOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getShapeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "shape", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSymNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps6(attr, "sym_name", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult MeshOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.shape)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.sym_name)))
    return ::mlir::failure();
  return ::mlir::success();
}

void MeshOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.shape);
  writer.writeAttribute(prop.sym_name);
}

::llvm::StringRef MeshOp::getSymName() {
  auto attr = getSymNameAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> MeshOp::getShape() {
  auto attr = getShapeAttr();
  return attr;
}

void MeshOp::setSymName(::llvm::StringRef attrValue) {
  getProperties().sym_name = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void MeshOp::setShape(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().shape = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void MeshOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::StringAttr sym_name, ::mlir::DenseI64ArrayAttr shape) {
  odsState.getOrAddProperties<Properties>().sym_name = sym_name;
  odsState.getOrAddProperties<Properties>().shape = shape;
}

void MeshOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::StringAttr sym_name, ::mlir::DenseI64ArrayAttr shape) {
  odsState.getOrAddProperties<Properties>().sym_name = sym_name;
  odsState.getOrAddProperties<Properties>().shape = shape;
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MeshOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::llvm::StringRef sym_name, ::llvm::ArrayRef<int64_t> shape) {
  odsState.getOrAddProperties<Properties>().sym_name = odsBuilder.getStringAttr(sym_name);
  odsState.getOrAddProperties<Properties>().shape = odsBuilder.getDenseI64ArrayAttr(shape);
}

void MeshOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef sym_name, ::llvm::ArrayRef<int64_t> shape) {
  odsState.getOrAddProperties<Properties>().sym_name = odsBuilder.getStringAttr(sym_name);
  odsState.getOrAddProperties<Properties>().shape = odsBuilder.getDenseI64ArrayAttr(shape);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MeshOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<MeshOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult MeshOp::verifyInvariantsImpl() {
  auto tblgen_shape = getProperties().shape; (void)tblgen_shape;
  if (!tblgen_shape) return emitOpError("requires attribute 'shape'");
  auto tblgen_sym_name = getProperties().sym_name; (void)tblgen_sym_name;
  if (!tblgen_sym_name) return emitOpError("requires attribute 'sym_name'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps6(*this, tblgen_sym_name, "sym_name")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_shape, "shape")))
    return ::mlir::failure();
  return ::mlir::success();
}

::llvm::LogicalResult MeshOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult MeshOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::StringAttr sym_nameAttr;
  ::mlir::DenseI64ArrayAttr shapeAttr;

  if (parser.parseSymbolName(sym_nameAttr))
    return ::mlir::failure();
  if (sym_nameAttr) result.getOrAddProperties<MeshOp::Properties>().sym_name = sym_nameAttr;
  if (parser.parseLParen())
    return ::mlir::failure();
  if (parser.parseKeyword("shape"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    auto odsResult = parseDimensionList(parser, shapeAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<MeshOp::Properties>().shape = shapeAttr;
  }
  if (parser.parseRParen())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  return ::mlir::success();
}

void MeshOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printSymbolName(getSymNameAttr().getValue());
  _odsPrinter << "(";
  _odsPrinter << "shape";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  printDimensionList(_odsPrinter, *this, getShapeAttr());
  _odsPrinter << ")";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("sym_name");
  elidedAttrs.push_back("shape");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::MeshOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::MeshShapeOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
MeshShapeOpGenericAdaptorBase::MeshShapeOpGenericAdaptorBase(MeshShapeOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef MeshShapeOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr MeshShapeOpGenericAdaptorBase::getAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().axes);
  return attr;
}

::llvm::ArrayRef<int16_t> MeshShapeOpGenericAdaptorBase::getAxes() {
  auto attr = getAxesAttr();
  return attr;
}

} // namespace detail
MeshShapeOpAdaptor::MeshShapeOpAdaptor(MeshShapeOp op) : MeshShapeOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult MeshShapeOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_axes = getProperties().axes; (void)tblgen_axes;
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.mesh_shape' op ""requires attribute 'mesh'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.mesh_shape' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_axes))))
    return emitError(loc, "'mesh.mesh_shape' op ""attribute 'axes' failed to satisfy constraint: i16 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> MeshShapeOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult MeshShapeOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.axes;
       auto attr = dict.get("axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute MeshShapeOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code MeshShapeOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> MeshShapeOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "axes")
      return prop.axes;

    if (name == "mesh")
      return prop.mesh;
  return std::nullopt;
}

void MeshShapeOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "axes") {
       prop.axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.axes)>>(value);
       return;
    }

    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }
}

void MeshShapeOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.axes) attrs.append("axes", prop.axes);

    if (prop.mesh) attrs.append("mesh", prop.mesh);
}

::llvm::LogicalResult MeshShapeOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult MeshShapeOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();
  return ::mlir::success();
}

void MeshShapeOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.axes);
  writer.writeAttribute(prop.mesh);
}

::llvm::StringRef MeshShapeOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> MeshShapeOp::getAxes() {
  auto attr = getAxesAttr();
  return attr;
}

void MeshShapeOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void MeshShapeOp::setAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void MeshShapeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr axes) {
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (axes) {
    odsState.getOrAddProperties<Properties>().axes = axes;
  }
  odsState.addTypes(result);
}

void MeshShapeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> axes) {
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().axes = odsBuilder.getDenseI16ArrayAttr(axes);
  odsState.addTypes(result);
}

void MeshShapeOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<MeshShapeOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void MeshShapeOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.axes)
    properties.axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult MeshShapeOp::verifyInvariantsImpl() {
  auto tblgen_axes = getProperties().axes; (void)tblgen_axes;
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_axes, "axes")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult MeshShapeOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult MeshShapeOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr axesAttr;
  ::llvm::SmallVector<::mlir::Type, 1> resultTypes;

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<MeshShapeOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (axesAttr) result.getOrAddProperties<MeshShapeOp::Properties>().axes = axesAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  if (parser.parseTypeList(resultTypes))
    return ::mlir::failure();
  result.addTypes(resultTypes);
  return ::mlir::success();
}

void MeshShapeOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getAxesAttr());
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("axes");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter << getResult().getTypes();
}

void MeshShapeOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::MeshShapeOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::NeighborsLinearIndicesOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
NeighborsLinearIndicesOpGenericAdaptorBase::NeighborsLinearIndicesOpGenericAdaptorBase(NeighborsLinearIndicesOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> NeighborsLinearIndicesOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef NeighborsLinearIndicesOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> NeighborsLinearIndicesOpGenericAdaptorBase::getSplitAxes() {
  auto attr = getSplitAxesAttr();
  return attr;
}

} // namespace detail
NeighborsLinearIndicesOpAdaptor::NeighborsLinearIndicesOpAdaptor(NeighborsLinearIndicesOp op) : NeighborsLinearIndicesOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult NeighborsLinearIndicesOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.neighbors_linear_indices' op ""requires attribute 'mesh'");
  auto tblgen_split_axes = getProperties().split_axes; (void)tblgen_split_axes;
  if (!tblgen_split_axes) return emitError(loc, "'mesh.neighbors_linear_indices' op ""requires attribute 'split_axes'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.neighbors_linear_indices' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_split_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_split_axes))))
    return emitError(loc, "'mesh.neighbors_linear_indices' op ""attribute 'split_axes' failed to satisfy constraint: i16 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> NeighborsLinearIndicesOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange NeighborsLinearIndicesOp::getDeviceMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult NeighborsLinearIndicesOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.split_axes;
       auto attr = dict.get("split_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `split_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute NeighborsLinearIndicesOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.split_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("split_axes",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code NeighborsLinearIndicesOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.split_axes.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> NeighborsLinearIndicesOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "split_axes")
      return prop.split_axes;
  return std::nullopt;
}

void NeighborsLinearIndicesOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "split_axes") {
       prop.split_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.split_axes)>>(value);
       return;
    }
}

void NeighborsLinearIndicesOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.split_axes) attrs.append("split_axes", prop.split_axes);
}

::llvm::LogicalResult NeighborsLinearIndicesOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSplitAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "split_axes", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult NeighborsLinearIndicesOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.split_axes)))
    return ::mlir::failure();
  return ::mlir::success();
}

void NeighborsLinearIndicesOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);
  writer.writeAttribute(prop.split_axes);
}

::llvm::StringRef NeighborsLinearIndicesOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> NeighborsLinearIndicesOp::getSplitAxes() {
  auto attr = getSplitAxesAttr();
  return attr;
}

void NeighborsLinearIndicesOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void NeighborsLinearIndicesOp::setSplitAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().split_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type neighbor_down, ::mlir::Type neighbor_up, ::mlir::FlatSymbolRefAttr mesh, ::mlir::ValueRange device, ::mlir::DenseI16ArrayAttr split_axes) {
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  odsState.addTypes(neighbor_down);
  odsState.addTypes(neighbor_up);
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::FlatSymbolRefAttr mesh, ::mlir::ValueRange device, ::mlir::DenseI16ArrayAttr split_axes) {
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(NeighborsLinearIndicesOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::ValueRange device, ::mlir::DenseI16ArrayAttr split_axes) {
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  assert(resultTypes.size() == 2u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type neighbor_down, ::mlir::Type neighbor_up, ::llvm::StringRef mesh, ::mlir::ValueRange device, ::llvm::ArrayRef<int16_t> split_axes) {
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = odsBuilder.getDenseI16ArrayAttr(split_axes);
  odsState.addTypes(neighbor_down);
  odsState.addTypes(neighbor_up);
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::llvm::StringRef mesh, ::mlir::ValueRange device, ::llvm::ArrayRef<int16_t> split_axes) {
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = odsBuilder.getDenseI16ArrayAttr(split_axes);

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(NeighborsLinearIndicesOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::mlir::ValueRange device, ::llvm::ArrayRef<int16_t> split_axes) {
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = odsBuilder.getDenseI16ArrayAttr(split_axes);
  assert(resultTypes.size() == 2u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 2u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<NeighborsLinearIndicesOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void NeighborsLinearIndicesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<NeighborsLinearIndicesOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
  ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
  if (::mlir::succeeded(NeighborsLinearIndicesOp::inferReturnTypes(odsBuilder.getContext(),
          odsState.location, operands,
          odsState.attributes.getDictionary(odsState.getContext()),
          odsState.getRawProperties(),
          odsState.regions, inferredReturnTypes))) {
    assert(inferredReturnTypes.size() == 2u && "mismatched number of return types");
    odsState.addTypes(inferredReturnTypes);
  } else {
    ::llvm::report_fatal_error("Failed to infer result type(s).");
  }
}

::llvm::LogicalResult NeighborsLinearIndicesOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_split_axes = getProperties().split_axes; (void)tblgen_split_axes;
  if (!tblgen_split_axes) return emitOpError("requires attribute 'split_axes'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_split_axes, "split_axes")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSResults(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult NeighborsLinearIndicesOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::llvm::LogicalResult NeighborsLinearIndicesOp::inferReturnTypes(::mlir::MLIRContext *context, ::std::optional<::mlir::Location> location, ::mlir::ValueRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::Type>&inferredReturnTypes) {
  inferredReturnTypes.resize(2);
  ::mlir::Builder odsBuilder(context);
  ::mlir::Type odsInferredType0 = odsBuilder.getIndexType();
  ::mlir::Type odsInferredType1 = odsBuilder.getIndexType();
  inferredReturnTypes[0] = odsInferredType0;
  inferredReturnTypes[1] = odsInferredType1;
  return ::mlir::success();
}

::mlir::ParseResult NeighborsLinearIndicesOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> deviceOperands;
  ::llvm::SMLoc deviceOperandsLoc;
  (void)deviceOperandsLoc;
  ::mlir::DenseI16ArrayAttr split_axesAttr;
  ::llvm::SmallVector<::mlir::Type, 1> allResultTypes;
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<NeighborsLinearIndicesOp::Properties>().mesh = meshAttr;
  if (parser.parseLSquare())
    return ::mlir::failure();

  deviceOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(deviceOperands))
    return ::mlir::failure();
  if (parser.parseRSquare())
    return ::mlir::failure();
  if (parser.parseKeyword("split_axes"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(split_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (split_axesAttr) result.getOrAddProperties<NeighborsLinearIndicesOp::Properties>().split_axes = split_axesAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  if (parser.parseTypeList(allResultTypes))
    return ::mlir::failure();
  ::mlir::Type odsBuildableType0 = parser.getBuilder().getIndexType();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(deviceOperands, odsBuildableType0, deviceOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void NeighborsLinearIndicesOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  _odsPrinter << "[";
  _odsPrinter << getDevice();
  _odsPrinter << "]";
  _odsPrinter << ' ' << "split_axes";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
_odsPrinter.printStrippedAttrOrType(getSplitAxesAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("split_axes");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter << getOperation()->getResultTypes();
}

void NeighborsLinearIndicesOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::NeighborsLinearIndicesOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ProcessLinearIndexOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ProcessLinearIndexOpGenericAdaptorBase::ProcessLinearIndexOpGenericAdaptorBase(ProcessLinearIndexOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef ProcessLinearIndexOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

} // namespace detail
ProcessLinearIndexOpAdaptor::ProcessLinearIndexOpAdaptor(ProcessLinearIndexOp op) : ProcessLinearIndexOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ProcessLinearIndexOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.process_linear_index' op ""requires attribute 'mesh'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.process_linear_index' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ProcessLinearIndexOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ProcessLinearIndexOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ProcessLinearIndexOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ProcessLinearIndexOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;
  return std::nullopt;
}

void ProcessLinearIndexOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }
}

void ProcessLinearIndexOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);
}

::llvm::LogicalResult ProcessLinearIndexOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ProcessLinearIndexOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ProcessLinearIndexOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);
}

::llvm::StringRef ProcessLinearIndexOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

void ProcessLinearIndexOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh) {
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.addTypes(result);
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::FlatSymbolRefAttr mesh) {
  odsState.getOrAddProperties<Properties>().mesh = mesh;

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(ProcessLinearIndexOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh) {
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh) {
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.addTypes(result);
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::llvm::StringRef mesh) {
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(ProcessLinearIndexOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh) {
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ProcessLinearIndexOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ProcessLinearIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ProcessLinearIndexOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
  ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
  if (::mlir::succeeded(ProcessLinearIndexOp::inferReturnTypes(odsBuilder.getContext(),
          odsState.location, operands,
          odsState.attributes.getDictionary(odsState.getContext()),
          odsState.getRawProperties(),
          odsState.regions, inferredReturnTypes))) {
    assert(inferredReturnTypes.size() == 1u && "mismatched number of return types");
    odsState.addTypes(inferredReturnTypes);
  } else {
    ::llvm::report_fatal_error("Failed to infer result type(s).");
  }
}

::llvm::LogicalResult ProcessLinearIndexOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ProcessLinearIndexOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::llvm::LogicalResult ProcessLinearIndexOp::inferReturnTypes(::mlir::MLIRContext *context, ::std::optional<::mlir::Location> location, ::mlir::ValueRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::Type>&inferredReturnTypes) {
  inferredReturnTypes.resize(1);
  ::mlir::Builder odsBuilder(context);
  ::mlir::Type odsInferredType0 = odsBuilder.getIndexType();
  inferredReturnTypes[0] = odsInferredType0;
  return ::mlir::success();
}

::mlir::ParseResult ProcessLinearIndexOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ProcessLinearIndexOp::Properties>().mesh = meshAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::IndexType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  return ::mlir::success();
}

void ProcessLinearIndexOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::IndexType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void ProcessLinearIndexOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ProcessLinearIndexOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ProcessMultiIndexOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ProcessMultiIndexOpGenericAdaptorBase::ProcessMultiIndexOpGenericAdaptorBase(ProcessMultiIndexOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef ProcessMultiIndexOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr ProcessMultiIndexOpGenericAdaptorBase::getAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().axes);
  return attr;
}

::llvm::ArrayRef<int16_t> ProcessMultiIndexOpGenericAdaptorBase::getAxes() {
  auto attr = getAxesAttr();
  return attr;
}

} // namespace detail
ProcessMultiIndexOpAdaptor::ProcessMultiIndexOpAdaptor(ProcessMultiIndexOp op) : ProcessMultiIndexOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ProcessMultiIndexOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_axes = getProperties().axes; (void)tblgen_axes;
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.process_multi_index' op ""requires attribute 'mesh'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.process_multi_index' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_axes))))
    return emitError(loc, "'mesh.process_multi_index' op ""attribute 'axes' failed to satisfy constraint: i16 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ProcessMultiIndexOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult ProcessMultiIndexOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.axes;
       auto attr = dict.get("axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ProcessMultiIndexOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ProcessMultiIndexOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ProcessMultiIndexOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "axes")
      return prop.axes;

    if (name == "mesh")
      return prop.mesh;
  return std::nullopt;
}

void ProcessMultiIndexOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "axes") {
       prop.axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.axes)>>(value);
       return;
    }

    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }
}

void ProcessMultiIndexOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.axes) attrs.append("axes", prop.axes);

    if (prop.mesh) attrs.append("mesh", prop.mesh);
}

::llvm::LogicalResult ProcessMultiIndexOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ProcessMultiIndexOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ProcessMultiIndexOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.axes);
  writer.writeAttribute(prop.mesh);
}

::llvm::StringRef ProcessMultiIndexOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> ProcessMultiIndexOp::getAxes() {
  auto attr = getAxesAttr();
  return attr;
}

void ProcessMultiIndexOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ProcessMultiIndexOp::setAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void ProcessMultiIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr axes) {
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (axes) {
    odsState.getOrAddProperties<Properties>().axes = axes;
  }
  odsState.addTypes(result);
}

void ProcessMultiIndexOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> axes) {
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().axes = odsBuilder.getDenseI16ArrayAttr(axes);
  odsState.addTypes(result);
}

void ProcessMultiIndexOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ProcessMultiIndexOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ProcessMultiIndexOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.axes)
    properties.axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult ProcessMultiIndexOp::verifyInvariantsImpl() {
  auto tblgen_axes = getProperties().axes; (void)tblgen_axes;
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_axes, "axes")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ProcessMultiIndexOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ProcessMultiIndexOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr axesAttr;
  ::llvm::SmallVector<::mlir::Type, 1> resultTypes;
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ProcessMultiIndexOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (axesAttr) result.getOrAddProperties<ProcessMultiIndexOp::Properties>().axes = axesAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  if (parser.parseTypeList(resultTypes))
    return ::mlir::failure();
  result.addTypes(resultTypes);
  return ::mlir::success();
}

void ProcessMultiIndexOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getAxesAttr());
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("axes");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter << getResult().getTypes();
}

void ProcessMultiIndexOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ProcessMultiIndexOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::RecvOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
RecvOpGenericAdaptorBase::RecvOpGenericAdaptorBase(RecvOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> RecvOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef RecvOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr RecvOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> RecvOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::std::optional<::llvm::ArrayRef<int64_t>> RecvOpGenericAdaptorBase::getSource() {
  auto attr = getSourceAttr();
  return attr ? ::std::optional<::llvm::ArrayRef<int64_t>>(attr) : (::std::nullopt);
}

} // namespace detail
RecvOpAdaptor::RecvOpAdaptor(RecvOp op) : RecvOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult RecvOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.recv' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_source = getProperties().source; (void)tblgen_source;

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.recv' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.recv' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_source && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_source))))
    return emitError(loc, "'mesh.recv' op ""attribute 'source' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> RecvOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange RecvOp::getSourceDynamicMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult RecvOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.source;
       auto attr = dict.get("source");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `source` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute RecvOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.source;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("source",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code RecvOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.source.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> RecvOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "source")
      return prop.source;
  return std::nullopt;
}

void RecvOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "source") {
       prop.source = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.source)>>(value);
       return;
    }
}

void RecvOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.source) attrs.append("source", prop.source);
}

::llvm::LogicalResult RecvOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSourceAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "source", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult RecvOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.source)))
    return ::mlir::failure();
  return ::mlir::success();
}

void RecvOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);

  writer.writeOptionalAttribute(prop.source);
}

::llvm::StringRef RecvOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> RecvOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::std::optional<::llvm::ArrayRef<int64_t>> RecvOp::getSource() {
  auto attr = getSourceAttr();
  return attr ? ::std::optional<::llvm::ArrayRef<int64_t>>(attr) : (::std::nullopt);
}

void RecvOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void RecvOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void RecvOp::setSource(::std::optional<::llvm::ArrayRef<int64_t>> attrValue) {
    auto &odsProp = getProperties().source;
    if (attrValue)
      odsProp = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(*attrValue);
    else
      odsProp = nullptr;
}

void RecvOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, /*optional*/::mlir::DenseI64ArrayAttr source, ::mlir::ValueRange source_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(source_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (source) {
    odsState.getOrAddProperties<Properties>().source = source;
  }
  odsState.addTypes(result);
}

void RecvOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, /*optional*/::mlir::DenseI64ArrayAttr source, ::mlir::ValueRange source_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(source_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (source) {
    odsState.getOrAddProperties<Properties>().source = source;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void RecvOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, /*optional*/::mlir::DenseI64ArrayAttr source, ::mlir::ValueRange source_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(source_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  if (source) {
    odsState.getOrAddProperties<Properties>().source = source;
  }
  odsState.addTypes(result);
}

void RecvOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, /*optional*/::mlir::DenseI64ArrayAttr source, ::mlir::ValueRange source_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(source_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  if (source) {
    odsState.getOrAddProperties<Properties>().source = source;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void RecvOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<RecvOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void RecvOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult RecvOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_source = getProperties().source; (void)tblgen_source;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_source, "source")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) && (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()))))
    return emitOpError("failed to verify that all of {input, result} have same shape");
  if (!(((getElementTypeOrSelf((*this->getODSOperands(0).begin()))) == (getElementTypeOrSelf((*this->getODSResults(0).begin()))) && (getElementTypeOrSelf((*this->getODSResults(0).begin()))) == (getElementTypeOrSelf((*this->getODSOperands(0).begin()))))))
    return emitOpError("failed to verify that all of {input, result} have same element type");
  return ::mlir::success();
}

::llvm::LogicalResult RecvOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult RecvOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> source_dynamicOperands;
  ::llvm::SMLoc source_dynamicOperandsLoc;
  (void)source_dynamicOperandsLoc;
  ::mlir::DenseI64ArrayAttr sourceAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<RecvOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<RecvOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (::mlir::succeeded(parser.parseOptionalKeyword("source"))) {
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    source_dynamicOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, source_dynamicOperands, sourceAttr);
    if (odsResult) return ::mlir::failure();
    if (sourceAttr)
      result.getOrAddProperties<RecvOp::Properties>().source = sourceAttr;
  }
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(inputOperands, source_dynamicOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void RecvOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  if (((!getSourceDynamic().empty()) || (getSourceAttr()))) {
    _odsPrinter << ' ' << "source";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
    printDynamicIndexList(_odsPrinter, *this, getSourceDynamic(), getSourceAttr());
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("source");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::RecvOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ReduceOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ReduceOpGenericAdaptorBase::ReduceOpGenericAdaptorBase(ReduceOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> ReduceOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef ReduceOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr ReduceOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> ReduceOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::mlir::mesh::ReductionKindAttr ReduceOpGenericAdaptorBase::getReductionAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::mesh::ReductionKindAttr>(getProperties().reduction);
  return attr;
}

::mlir::mesh::ReductionKind ReduceOpGenericAdaptorBase::getReduction() {
  auto attr = getReductionAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> ReduceOpGenericAdaptorBase::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

} // namespace detail
ReduceOpAdaptor::ReduceOpAdaptor(ReduceOp op) : ReduceOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ReduceOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.reduce' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_reduction = getProperties().reduction; (void)tblgen_reduction;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitError(loc, "'mesh.reduce' op ""requires attribute 'root'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.reduce' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.reduce' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_reduction && !((::llvm::isa<::mlir::mesh::ReductionKindAttr>(tblgen_reduction))))
    return emitError(loc, "'mesh.reduce' op ""attribute 'reduction' failed to satisfy constraint: Reduction of an iterator/mesh dimension.");

  if (tblgen_root && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_root))))
    return emitError(loc, "'mesh.reduce' op ""attribute 'root' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ReduceOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange ReduceOp::getRootDynamicMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult ReduceOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.reduction;
       auto attr = dict.get("reduction");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `reduction` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.root;
       auto attr = dict.get("root");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `root` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ReduceOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.reduction;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("reduction",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.root;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("root",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ReduceOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.reduction.getAsOpaquePointer()), 
    llvm::hash_value(prop.root.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ReduceOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "reduction")
      return prop.reduction;

    if (name == "root")
      return prop.root;
  return std::nullopt;
}

void ReduceOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "reduction") {
       prop.reduction = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.reduction)>>(value);
       return;
    }

    if (name == "root") {
       prop.root = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.root)>>(value);
       return;
    }
}

void ReduceOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.reduction) attrs.append("reduction", prop.reduction);

    if (prop.root) attrs.append("root", prop.root);
}

::llvm::LogicalResult ReduceOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getReductionAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(attr, "reduction", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRootAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "root", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ReduceOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.reduction)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.root)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ReduceOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);

  writer.writeOptionalAttribute(prop.reduction);
  writer.writeAttribute(prop.root);
}

::llvm::StringRef ReduceOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> ReduceOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::mlir::mesh::ReductionKind ReduceOp::getReduction() {
  auto attr = getReductionAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> ReduceOp::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

void ReduceOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ReduceOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void ReduceOp::setReduction(::mlir::mesh::ReductionKind attrValue) {
  getProperties().reduction = ::mlir::mesh::ReductionKindAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ReduceOp::setRoot(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().root = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void ReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKindAttr reduction, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (reduction) {
    odsState.getOrAddProperties<Properties>().reduction = reduction;
  }
  odsState.getOrAddProperties<Properties>().root = root;
  odsState.addTypes(result);
}

void ReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKindAttr reduction, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (reduction) {
    odsState.getOrAddProperties<Properties>().reduction = reduction;
  }
  odsState.getOrAddProperties<Properties>().root = root;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKind reduction, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), reduction);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  odsState.addTypes(result);
}

void ReduceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKind reduction, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), reduction);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ReduceOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ReduceOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ReduceOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
  if (!properties.reduction)
    properties.reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), ::mlir::mesh::ReductionKind::Sum);
}

::llvm::LogicalResult ReduceOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_reduction = getProperties().reduction; (void)tblgen_reduction;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitOpError("requires attribute 'root'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(*this, tblgen_reduction, "reduction")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_root, "root")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) && (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()))))
    return emitOpError("failed to verify that all of {input, result} have same shape");
  return ::mlir::success();
}

::llvm::LogicalResult ReduceOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ReduceOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::mesh::ReductionKindAttr reductionAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> root_dynamicOperands;
  ::llvm::SMLoc root_dynamicOperandsLoc;
  (void)root_dynamicOperandsLoc;
  ::mlir::DenseI64ArrayAttr rootAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ReduceOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<ReduceOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (::mlir::succeeded(parser.parseOptionalKeyword("reduction"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(reductionAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (reductionAttr) result.getOrAddProperties<ReduceOp::Properties>().reduction = reductionAttr;
  }
  if (parser.parseKeyword("root"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    root_dynamicOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, root_dynamicOperands, rootAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<ReduceOp::Properties>().root = rootAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(inputOperands, root_dynamicOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ReduceOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  if (getReductionAttr() != ::mlir::mesh::ReductionKindAttr::get(::mlir::OpBuilder((*this)->getContext()).getContext(), ::mlir::mesh::ReductionKind::Sum)) {
    _odsPrinter << ' ' << "reduction";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getReductionAttr());
  }
  _odsPrinter << ' ' << "root";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  printDynamicIndexList(_odsPrinter, *this, getRootDynamic(), getRootAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("reduction");
  elidedAttrs.push_back("root");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getReductionAttr();
     if(attr && (attr == ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), ::mlir::mesh::ReductionKind::Sum)))
       elidedAttrs.push_back("reduction");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

void ReduceOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ReduceOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ReduceScatterOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ReduceScatterOpGenericAdaptorBase::ReduceScatterOpGenericAdaptorBase(ReduceScatterOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef ReduceScatterOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr ReduceScatterOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> ReduceScatterOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::mlir::mesh::ReductionKindAttr ReduceScatterOpGenericAdaptorBase::getReductionAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::mesh::ReductionKindAttr>(getProperties().reduction);
  return attr;
}

::mlir::mesh::ReductionKind ReduceScatterOpGenericAdaptorBase::getReduction() {
  auto attr = getReductionAttr();
  return attr.getValue();
}

::llvm::APInt ReduceScatterOpGenericAdaptorBase::getScatterAxis() {
  auto attr = getScatterAxisAttr();
  return attr.getValue();
}

} // namespace detail
ReduceScatterOpAdaptor::ReduceScatterOpAdaptor(ReduceScatterOp op) : ReduceScatterOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ReduceScatterOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.reduce_scatter' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_reduction = getProperties().reduction; (void)tblgen_reduction;
  auto tblgen_scatter_axis = getProperties().scatter_axis; (void)tblgen_scatter_axis;
  if (!tblgen_scatter_axis) return emitError(loc, "'mesh.reduce_scatter' op ""requires attribute 'scatter_axis'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.reduce_scatter' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.reduce_scatter' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_reduction && !((::llvm::isa<::mlir::mesh::ReductionKindAttr>(tblgen_reduction))))
    return emitError(loc, "'mesh.reduce_scatter' op ""attribute 'reduction' failed to satisfy constraint: Reduction of an iterator/mesh dimension.");

  if (tblgen_scatter_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_scatter_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_scatter_axis).getType())))))
    return emitError(loc, "'mesh.reduce_scatter' op ""attribute 'scatter_axis' failed to satisfy constraint: index attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ReduceScatterOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.reduction;
       auto attr = dict.get("reduction");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `reduction` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.scatter_axis;
       auto attr = dict.get("scatter_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `scatter_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ReduceScatterOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.reduction;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("reduction",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.scatter_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("scatter_axis",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ReduceScatterOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.reduction.getAsOpaquePointer()), 
    llvm::hash_value(prop.scatter_axis.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ReduceScatterOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "reduction")
      return prop.reduction;

    if (name == "scatter_axis")
      return prop.scatter_axis;
  return std::nullopt;
}

void ReduceScatterOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "reduction") {
       prop.reduction = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.reduction)>>(value);
       return;
    }

    if (name == "scatter_axis") {
       prop.scatter_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.scatter_axis)>>(value);
       return;
    }
}

void ReduceScatterOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.reduction) attrs.append("reduction", prop.reduction);

    if (prop.scatter_axis) attrs.append("scatter_axis", prop.scatter_axis);
}

::llvm::LogicalResult ReduceScatterOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getReductionAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(attr, "reduction", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getScatterAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "scatter_axis", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ReduceScatterOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.reduction)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.scatter_axis)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ReduceScatterOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);

  writer.writeOptionalAttribute(prop.reduction);
  writer.writeAttribute(prop.scatter_axis);
}

::llvm::StringRef ReduceScatterOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> ReduceScatterOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::mlir::mesh::ReductionKind ReduceScatterOp::getReduction() {
  auto attr = getReductionAttr();
  return attr.getValue();
}

::llvm::APInt ReduceScatterOp::getScatterAxis() {
  auto attr = getScatterAxisAttr();
  return attr.getValue();
}

void ReduceScatterOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ReduceScatterOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void ReduceScatterOp::setReduction(::mlir::mesh::ReductionKind attrValue) {
  getProperties().reduction = ::mlir::mesh::ReductionKindAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ReduceScatterOp::setScatterAxis(::llvm::APInt attrValue) {
  getProperties().scatter_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void ReduceScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKindAttr reduction, ::mlir::IntegerAttr scatter_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (reduction) {
    odsState.getOrAddProperties<Properties>().reduction = reduction;
  }
  odsState.getOrAddProperties<Properties>().scatter_axis = scatter_axis;
  odsState.addTypes(result);
}

void ReduceScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKindAttr reduction, ::mlir::IntegerAttr scatter_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  if (reduction) {
    odsState.getOrAddProperties<Properties>().reduction = reduction;
  }
  odsState.getOrAddProperties<Properties>().scatter_axis = scatter_axis;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ReduceScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKind reduction, ::llvm::APInt scatter_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), reduction);
  odsState.getOrAddProperties<Properties>().scatter_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), scatter_axis);
  odsState.addTypes(result);
}

void ReduceScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::mlir::mesh::ReductionKind reduction, ::llvm::APInt scatter_axis) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), reduction);
  odsState.getOrAddProperties<Properties>().scatter_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), scatter_axis);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ReduceScatterOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ReduceScatterOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ReduceScatterOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
  if (!properties.reduction)
    properties.reduction = ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), ::mlir::mesh::ReductionKind::Sum);
}

::llvm::LogicalResult ReduceScatterOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_reduction = getProperties().reduction; (void)tblgen_reduction;
  auto tblgen_scatter_axis = getProperties().scatter_axis; (void)tblgen_scatter_axis;
  if (!tblgen_scatter_axis) return emitOpError("requires attribute 'scatter_axis'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(*this, tblgen_reduction, "reduction")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_scatter_axis, "scatter_axis")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ReduceScatterOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ReduceScatterOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::mesh::ReductionKindAttr reductionAttr;
  ::mlir::IntegerAttr scatter_axisAttr;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ReduceScatterOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<ReduceScatterOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (::mlir::succeeded(parser.parseOptionalKeyword("reduction"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(reductionAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (reductionAttr) result.getOrAddProperties<ReduceScatterOp::Properties>().reduction = reductionAttr;
  }
  if (parser.parseKeyword("scatter_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(scatter_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (scatter_axisAttr) result.getOrAddProperties<ReduceScatterOp::Properties>().scatter_axis = scatter_axisAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::RankedTensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ReduceScatterOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  if (getReductionAttr() != ::mlir::mesh::ReductionKindAttr::get(::mlir::OpBuilder((*this)->getContext()).getContext(), ::mlir::mesh::ReductionKind::Sum)) {
    _odsPrinter << ' ' << "reduction";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getReductionAttr());
  }
  _odsPrinter << ' ' << "scatter_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getScatterAxisAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("reduction");
  elidedAttrs.push_back("scatter_axis");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getReductionAttr();
     if(attr && (attr == ::mlir::mesh::ReductionKindAttr::get(odsBuilder.getContext(), ::mlir::mesh::ReductionKind::Sum)))
       elidedAttrs.push_back("reduction");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::RankedTensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void ReduceScatterOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ReduceScatterOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ScatterOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ScatterOpGenericAdaptorBase::ScatterOpGenericAdaptorBase(ScatterOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> ScatterOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef ScatterOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr ScatterOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> ScatterOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt ScatterOpGenericAdaptorBase::getScatterAxis() {
  auto attr = getScatterAxisAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> ScatterOpGenericAdaptorBase::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

} // namespace detail
ScatterOpAdaptor::ScatterOpAdaptor(ScatterOp op) : ScatterOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ScatterOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.scatter' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitError(loc, "'mesh.scatter' op ""requires attribute 'root'");
  auto tblgen_scatter_axis = getProperties().scatter_axis; (void)tblgen_scatter_axis;
  if (!tblgen_scatter_axis) return emitError(loc, "'mesh.scatter' op ""requires attribute 'scatter_axis'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.scatter' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.scatter' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_scatter_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_scatter_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_scatter_axis).getType())))))
    return emitError(loc, "'mesh.scatter' op ""attribute 'scatter_axis' failed to satisfy constraint: index attribute");

  if (tblgen_root && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_root))))
    return emitError(loc, "'mesh.scatter' op ""attribute 'root' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ScatterOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange ScatterOp::getRootDynamicMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult ScatterOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.root;
       auto attr = dict.get("root");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `root` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.scatter_axis;
       auto attr = dict.get("scatter_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `scatter_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ScatterOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.root;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("root",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.scatter_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("scatter_axis",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ScatterOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.root.getAsOpaquePointer()), 
    llvm::hash_value(prop.scatter_axis.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ScatterOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "root")
      return prop.root;

    if (name == "scatter_axis")
      return prop.scatter_axis;
  return std::nullopt;
}

void ScatterOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "root") {
       prop.root = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.root)>>(value);
       return;
    }

    if (name == "scatter_axis") {
       prop.scatter_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.scatter_axis)>>(value);
       return;
    }
}

void ScatterOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.root) attrs.append("root", prop.root);

    if (prop.scatter_axis) attrs.append("scatter_axis", prop.scatter_axis);
}

::llvm::LogicalResult ScatterOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRootAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "root", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getScatterAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "scatter_axis", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ScatterOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.root)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.scatter_axis)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ScatterOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
  writer.writeAttribute(prop.root);
  writer.writeAttribute(prop.scatter_axis);
}

::llvm::StringRef ScatterOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> ScatterOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt ScatterOp::getScatterAxis() {
  auto attr = getScatterAxisAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int64_t> ScatterOp::getRoot() {
  auto attr = getRootAttr();
  return attr;
}

void ScatterOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ScatterOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void ScatterOp::setScatterAxis(::llvm::APInt attrValue) {
  getProperties().scatter_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void ScatterOp::setRoot(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().root = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void ScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr scatter_axis, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().scatter_axis = scatter_axis;
  odsState.getOrAddProperties<Properties>().root = root;
  odsState.addTypes(result);
}

void ScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr scatter_axis, ::mlir::DenseI64ArrayAttr root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().scatter_axis = scatter_axis;
  odsState.getOrAddProperties<Properties>().root = root;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt scatter_axis, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().scatter_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), scatter_axis);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  odsState.addTypes(result);
}

void ScatterOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt scatter_axis, ::llvm::ArrayRef<int64_t> root, ::mlir::ValueRange root_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(root_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().scatter_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), scatter_axis);
  odsState.getOrAddProperties<Properties>().root = odsBuilder.getDenseI64ArrayAttr(root);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ScatterOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ScatterOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ScatterOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult ScatterOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_root = getProperties().root; (void)tblgen_root;
  if (!tblgen_root) return emitOpError("requires attribute 'root'");
  auto tblgen_scatter_axis = getProperties().scatter_axis; (void)tblgen_scatter_axis;
  if (!tblgen_scatter_axis) return emitOpError("requires attribute 'scatter_axis'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_scatter_axis, "scatter_axis")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_root, "root")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getRank()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getRank()) && (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getRank()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getRank()))))
    return emitOpError("failed to verify that all of {input, result} have same rank");
  if (!(((getElementTypeOrSelf((*this->getODSOperands(0).begin()))) == (getElementTypeOrSelf((*this->getODSResults(0).begin()))) && (getElementTypeOrSelf((*this->getODSResults(0).begin()))) == (getElementTypeOrSelf((*this->getODSOperands(0).begin()))))))
    return emitOpError("failed to verify that all of {input, result} have same element type");
  return ::mlir::success();
}

::llvm::LogicalResult ScatterOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ScatterOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::IntegerAttr scatter_axisAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> root_dynamicOperands;
  ::llvm::SMLoc root_dynamicOperandsLoc;
  (void)root_dynamicOperandsLoc;
  ::mlir::DenseI64ArrayAttr rootAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ScatterOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<ScatterOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("scatter_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(scatter_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (scatter_axisAttr) result.getOrAddProperties<ScatterOp::Properties>().scatter_axis = scatter_axisAttr;
  if (parser.parseKeyword("root"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    root_dynamicOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, root_dynamicOperands, rootAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<ScatterOp::Properties>().root = rootAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(inputOperands, root_dynamicOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ScatterOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "scatter_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getScatterAxisAttr());
  _odsPrinter << ' ' << "root";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  printDynamicIndexList(_odsPrinter, *this, getRootDynamic(), getRootAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("scatter_axis");
  elidedAttrs.push_back("root");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

void ScatterOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ScatterOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::SendOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
SendOpGenericAdaptorBase::SendOpGenericAdaptorBase(SendOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> SendOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef SendOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr SendOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> SendOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::ArrayRef<int64_t> SendOpGenericAdaptorBase::getDestination() {
  auto attr = getDestinationAttr();
  return attr;
}

} // namespace detail
SendOpAdaptor::SendOpAdaptor(SendOp op) : SendOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult SendOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_destination = getProperties().destination; (void)tblgen_destination;
  if (!tblgen_destination) return emitError(loc, "'mesh.send' op ""requires attribute 'destination'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.send' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.send' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.send' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_destination && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_destination))))
    return emitError(loc, "'mesh.send' op ""attribute 'destination' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> SendOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange SendOp::getDestinationDynamicMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult SendOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.destination;
       auto attr = dict.get("destination");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `destination` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute SendOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.destination;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("destination",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code SendOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.destination.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> SendOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "destination")
      return prop.destination;

    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;
  return std::nullopt;
}

void SendOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "destination") {
       prop.destination = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.destination)>>(value);
       return;
    }

    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }
}

void SendOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.destination) attrs.append("destination", prop.destination);

    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);
}

::llvm::LogicalResult SendOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getDestinationAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "destination", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult SendOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.destination)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();
  return ::mlir::success();
}

void SendOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.destination);
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
}

::llvm::StringRef SendOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> SendOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::ArrayRef<int64_t> SendOp::getDestination() {
  auto attr = getDestinationAttr();
  return attr;
}

void SendOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void SendOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void SendOp::setDestination(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().destination = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void SendOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::DenseI64ArrayAttr destination, ::mlir::ValueRange destination_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(destination_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().destination = destination;
  odsState.addTypes(result);
}

void SendOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::DenseI64ArrayAttr destination, ::mlir::ValueRange destination_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(destination_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().destination = destination;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void SendOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::ArrayRef<int64_t> destination, ::mlir::ValueRange destination_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(destination_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().destination = odsBuilder.getDenseI64ArrayAttr(destination);
  odsState.addTypes(result);
}

void SendOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::ArrayRef<int64_t> destination, ::mlir::ValueRange destination_dynamic) {
  odsState.addOperands(input);
  odsState.addOperands(destination_dynamic);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().destination = odsBuilder.getDenseI64ArrayAttr(destination);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void SendOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<SendOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void SendOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult SendOp::verifyInvariantsImpl() {
  auto tblgen_destination = getProperties().destination; (void)tblgen_destination;
  if (!tblgen_destination) return emitOpError("requires attribute 'destination'");
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_destination, "destination")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) && (::llvm::cast<::mlir::ShapedType>((*this->getODSResults(0).begin()).getType()).getShape()) == (::llvm::cast<::mlir::ShapedType>((*this->getODSOperands(0).begin()).getType()).getShape()))))
    return emitOpError("failed to verify that all of {input, result} have same shape");
  if (!(((getElementTypeOrSelf((*this->getODSOperands(0).begin()))) == (getElementTypeOrSelf((*this->getODSResults(0).begin()))) && (getElementTypeOrSelf((*this->getODSResults(0).begin()))) == (getElementTypeOrSelf((*this->getODSOperands(0).begin()))))))
    return emitOpError("failed to verify that all of {input, result} have same element type");
  return ::mlir::success();
}

::llvm::LogicalResult SendOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult SendOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> destination_dynamicOperands;
  ::llvm::SMLoc destination_dynamicOperandsLoc;
  (void)destination_dynamicOperandsLoc;
  ::mlir::DenseI64ArrayAttr destinationAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<SendOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<SendOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("destination"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    destination_dynamicOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, destination_dynamicOperands, destinationAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<SendOp::Properties>().destination = destinationAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(inputOperands, destination_dynamicOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void SendOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "destination";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  printDynamicIndexList(_odsPrinter, *this, getDestinationDynamic(), getDestinationAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("destination");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::SendOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ShardOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ShardOpGenericAdaptorBase::ShardOpGenericAdaptorBase(ShardOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::UnitAttr ShardOpGenericAdaptorBase::getAnnotateForUsersAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().annotate_for_users);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ShardOpGenericAdaptorBase::getAnnotateForUsers() {
  auto attr = getAnnotateForUsersAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
ShardOpAdaptor::ShardOpAdaptor(ShardOp op) : ShardOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ShardOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_annotate_for_users = getProperties().annotate_for_users; (void)tblgen_annotate_for_users;

  if (tblgen_annotate_for_users && !((::llvm::isa<::mlir::UnitAttr>(tblgen_annotate_for_users))))
    return emitError(loc, "'mesh.shard' op ""attribute 'annotate_for_users' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ShardOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.annotate_for_users;
       auto attr = dict.get("annotate_for_users");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `annotate_for_users` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ShardOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.annotate_for_users;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("annotate_for_users",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ShardOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.annotate_for_users.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ShardOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "annotate_for_users")
      return prop.annotate_for_users;
  return std::nullopt;
}

void ShardOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "annotate_for_users") {
       prop.annotate_for_users = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.annotate_for_users)>>(value);
       return;
    }
}

void ShardOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.annotate_for_users) attrs.append("annotate_for_users", prop.annotate_for_users);
}

::llvm::LogicalResult ShardOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getAnnotateForUsersAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps7(attr, "annotate_for_users", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ShardOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.annotate_for_users)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShardOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.annotate_for_users);
}

bool ShardOp::getAnnotateForUsers() {
  auto attr = getAnnotateForUsersAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void ShardOp::setAnnotateForUsers(bool attrValue) {
    auto &odsProp = getProperties().annotate_for_users;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value src, ::mlir::Value sharding, /*optional*/::mlir::UnitAttr annotate_for_users) {
  odsState.addOperands(src);
  odsState.addOperands(sharding);
  if (annotate_for_users) {
    odsState.getOrAddProperties<Properties>().annotate_for_users = annotate_for_users;
  }
  odsState.addTypes(result);
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value src, ::mlir::Value sharding, /*optional*/::mlir::UnitAttr annotate_for_users) {
  odsState.addOperands(src);
  odsState.addOperands(sharding);
  if (annotate_for_users) {
    odsState.getOrAddProperties<Properties>().annotate_for_users = annotate_for_users;
  }

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(ShardOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value src, ::mlir::Value sharding, /*optional*/::mlir::UnitAttr annotate_for_users) {
  odsState.addOperands(src);
  odsState.addOperands(sharding);
  if (annotate_for_users) {
    odsState.getOrAddProperties<Properties>().annotate_for_users = annotate_for_users;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value src, ::mlir::Value sharding, /*optional*/bool annotate_for_users) {
  odsState.addOperands(src);
  odsState.addOperands(sharding);
  if (annotate_for_users) {
    odsState.getOrAddProperties<Properties>().annotate_for_users = ((annotate_for_users) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes(result);
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value src, ::mlir::Value sharding, /*optional*/bool annotate_for_users) {
  odsState.addOperands(src);
  odsState.addOperands(sharding);
  if (annotate_for_users) {
    odsState.getOrAddProperties<Properties>().annotate_for_users = ((annotate_for_users) ? odsBuilder.getUnitAttr() : nullptr);
  }

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(ShardOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value src, ::mlir::Value sharding, /*optional*/bool annotate_for_users) {
  odsState.addOperands(src);
  odsState.addOperands(sharding);
  if (annotate_for_users) {
    odsState.getOrAddProperties<Properties>().annotate_for_users = ((annotate_for_users) ? odsBuilder.getUnitAttr() : nullptr);
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ShardOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 2u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ShardOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ShardOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 2u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ShardOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
  ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
  if (::mlir::succeeded(ShardOp::inferReturnTypes(odsBuilder.getContext(),
          odsState.location, operands,
          odsState.attributes.getDictionary(odsState.getContext()),
          odsState.getRawProperties(),
          odsState.regions, inferredReturnTypes))) {
    assert(inferredReturnTypes.size() == 1u && "mismatched number of return types");
    odsState.addTypes(inferredReturnTypes);
  } else {
    ::llvm::report_fatal_error("Failed to infer result type(s).");
  }
}

::llvm::LogicalResult ShardOp::verifyInvariantsImpl() {
  auto tblgen_annotate_for_users = getProperties().annotate_for_users; (void)tblgen_annotate_for_users;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps7(*this, tblgen_annotate_for_users, "annotate_for_users")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps5(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!((((*this->getODSResults(0).begin()).getType()) == ((*this->getODSOperands(0).begin()).getType()) && ((*this->getODSOperands(0).begin()).getType()) == ((*this->getODSResults(0).begin()).getType()))))
    return emitOpError("failed to verify that all of {result, src} have same type");
  return ::mlir::success();
}

::llvm::LogicalResult ShardOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::llvm::LogicalResult ShardOp::inferReturnTypes(::mlir::MLIRContext *context, ::std::optional<::mlir::Location> location, ::mlir::ValueRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::Type>&inferredReturnTypes) {
  inferredReturnTypes.resize(1);
  ::mlir::Builder odsBuilder(context);
  if (operands.size() <= 0)
    return ::mlir::failure();
  ::mlir::Type odsInferredType0 = operands[0].getType();
  inferredReturnTypes[0] = odsInferredType0;
  return ::mlir::success();
}

::mlir::ParseResult ShardOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand srcRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> srcOperands(&srcRawOperand, 1);  ::llvm::SMLoc srcOperandsLoc;
  (void)srcOperandsLoc;
  ::mlir::OpAsmParser::UnresolvedOperand shardingRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> shardingOperands(&shardingRawOperand, 1);  ::llvm::SMLoc shardingOperandsLoc;
  (void)shardingOperandsLoc;
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  srcOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(srcRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  shardingOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(shardingRawOperand))
    return ::mlir::failure();
  if (::mlir::succeeded(parser.parseOptionalKeyword("annotate_for_users"))) {
    result.getOrAddProperties<ShardOp::Properties>().annotate_for_users = parser.getBuilder().getUnitAttr();  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::RankedTensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  ::mlir::Type odsBuildableType0 = parser.getBuilder().getType<::mlir::mesh::ShardingType>();
  result.addTypes(resultTypes);
  if (parser.resolveOperands(srcOperands, resultTypes[0], srcOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(shardingOperands, odsBuildableType0, shardingOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShardOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getSrc();
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getSharding();
  if ((getAnnotateForUsersAttr() && getAnnotateForUsersAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr))) {
    _odsPrinter << ' ' << "annotate_for_users";
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("annotate_for_users");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getAnnotateForUsersAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("annotate_for_users");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::RankedTensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void ShardOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ShardOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ShardShapeOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ShardShapeOpGenericAdaptorBase::ShardShapeOpGenericAdaptorBase(ShardShapeOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::ArrayRef<int64_t> ShardShapeOpGenericAdaptorBase::getShape() {
  auto attr = getShapeAttr();
  return attr;
}

} // namespace detail
ShardShapeOpAdaptor::ShardShapeOpAdaptor(ShardShapeOp op) : ShardShapeOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ShardShapeOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_shape = getProperties().shape; (void)tblgen_shape;
  if (!tblgen_shape) return emitError(loc, "'mesh.shard_shape' op ""requires attribute 'shape'");

  if (tblgen_shape && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_shape))))
    return emitError(loc, "'mesh.shard_shape' op ""attribute 'shape' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ShardShapeOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult ShardShapeOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.shape;
       auto attr = dict.get("shape");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `shape` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ShardShapeOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.shape;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("shape",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ShardShapeOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.shape.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ShardShapeOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "shape")
      return prop.shape;
  return std::nullopt;
}

void ShardShapeOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "shape") {
       prop.shape = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.shape)>>(value);
       return;
    }
}

void ShardShapeOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.shape) attrs.append("shape", prop.shape);
}

::llvm::LogicalResult ShardShapeOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getShapeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "shape", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ShardShapeOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.shape)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShardShapeOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.shape);
}

::llvm::ArrayRef<int64_t> ShardShapeOp::getShape() {
  auto attr = getShapeAttr();
  return attr;
}

void ShardShapeOp::setShape(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().shape = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void ShardShapeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange result, ::mlir::DenseI64ArrayAttr shape, ::mlir::Value sharding, ::mlir::Value device) {
  odsState.addOperands(sharding);
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().shape = shape;
  odsState.addTypes(result);
}

void ShardShapeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange result, ::llvm::ArrayRef<int64_t> shape, ::mlir::Value sharding, ::mlir::Value device) {
  odsState.addOperands(sharding);
  odsState.addOperands(device);
  odsState.getOrAddProperties<Properties>().shape = odsBuilder.getDenseI64ArrayAttr(shape);
  odsState.addTypes(result);
}

void ShardShapeOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 2u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ShardShapeOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult ShardShapeOp::verifyInvariantsImpl() {
  auto tblgen_shape = getProperties().shape; (void)tblgen_shape;
  if (!tblgen_shape) return emitOpError("requires attribute 'shape'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_shape, "shape")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps5(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps3(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ShardShapeOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ShardShapeOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::DenseI64ArrayAttr shapeAttr;
  ::mlir::OpAsmParser::UnresolvedOperand shardingRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> shardingOperands(&shardingRawOperand, 1);  ::llvm::SMLoc shardingOperandsLoc;
  (void)shardingOperandsLoc;
  ::mlir::OpAsmParser::UnresolvedOperand deviceRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> deviceOperands(&deviceRawOperand, 1);  ::llvm::SMLoc deviceOperandsLoc;
  (void)deviceOperandsLoc;
  ::llvm::SmallVector<::mlir::Type, 1> resultTypes;
  {
    auto odsResult = parseDimensionList(parser, shapeAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<ShardShapeOp::Properties>().shape = shapeAttr;
  }

  shardingOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(shardingRawOperand))
    return ::mlir::failure();

  deviceOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(deviceRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  if (parser.parseTypeList(resultTypes))
    return ::mlir::failure();
  ::mlir::Type odsBuildableType0 = parser.getBuilder().getType<::mlir::mesh::ShardingType>();
  ::mlir::Type odsBuildableType1 = parser.getBuilder().getIndexType();
  result.addTypes(resultTypes);
  if (parser.resolveOperands(shardingOperands, odsBuildableType0, shardingOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(deviceOperands, odsBuildableType1, deviceOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShardShapeOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  printDimensionList(_odsPrinter, *this, getShapeAttr());
  _odsPrinter << ' ';
  _odsPrinter << getSharding();
  _odsPrinter << ' ';
  _odsPrinter << getDevice();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("shape");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter << getResult().getTypes();
}

void ShardShapeOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ShardShapeOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ShardingOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ShardingOpGenericAdaptorBase::ShardingOpGenericAdaptorBase(ShardingOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> ShardingOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  ::llvm::ArrayRef<int32_t> sizeAttr = getProperties().operandSegmentSizes;

  unsigned start = 0;
  for (unsigned i = 0; i < index; ++i)
    start += sizeAttr[i];
  return {start, sizeAttr[index]};
}

::llvm::StringRef ShardingOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::mesh::MeshAxesArrayAttr ShardingOpGenericAdaptorBase::getSplitAxes() {
  auto attr = getSplitAxesAttr();
  return ::llvm::cast<::mlir::mesh::MeshAxesArrayAttr>(attr);
}

::std::optional<::llvm::ArrayRef<int16_t>> ShardingOpGenericAdaptorBase::getPartialAxes() {
  auto attr = getPartialAxesAttr();
  return attr ? ::std::optional<::llvm::ArrayRef<int16_t>>(attr) : (::std::nullopt);
}

::std::optional<::mlir::mesh::ReductionKind> ShardingOpGenericAdaptorBase::getPartialType() {
  auto attr = getPartialTypeAttr();
  return attr ? ::std::optional<::mlir::mesh::ReductionKind>(attr.getValue()) : (::std::nullopt);
}

::mlir::DenseI64ArrayAttr ShardingOpGenericAdaptorBase::getStaticShardedDimsOffsetsAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI64ArrayAttr>(getProperties().static_sharded_dims_offsets);
  return attr;
}

::llvm::ArrayRef<int64_t> ShardingOpGenericAdaptorBase::getStaticShardedDimsOffsets() {
  auto attr = getStaticShardedDimsOffsetsAttr();
  return attr;
}

::mlir::DenseI64ArrayAttr ShardingOpGenericAdaptorBase::getStaticHaloSizesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI64ArrayAttr>(getProperties().static_halo_sizes);
  return attr;
}

::llvm::ArrayRef<int64_t> ShardingOpGenericAdaptorBase::getStaticHaloSizes() {
  auto attr = getStaticHaloSizesAttr();
  return attr;
}

} // namespace detail
ShardingOpAdaptor::ShardingOpAdaptor(ShardingOp op) : ShardingOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ShardingOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.sharding' op ""requires attribute 'mesh'");
  auto tblgen_partial_axes = getProperties().partial_axes; (void)tblgen_partial_axes;
  auto tblgen_partial_type = getProperties().partial_type; (void)tblgen_partial_type;
  auto tblgen_split_axes = getProperties().split_axes; (void)tblgen_split_axes;
  if (!tblgen_split_axes) return emitError(loc, "'mesh.sharding' op ""requires attribute 'split_axes'");
  auto tblgen_static_halo_sizes = getProperties().static_halo_sizes; (void)tblgen_static_halo_sizes;
  auto tblgen_static_sharded_dims_offsets = getProperties().static_sharded_dims_offsets; (void)tblgen_static_sharded_dims_offsets;

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.sharding' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_split_axes && !((::llvm::isa<::mlir::mesh::MeshAxesArrayAttr>(tblgen_split_axes))))
    return emitError(loc, "'mesh.sharding' op ""attribute 'split_axes' failed to satisfy constraint: ");

  if (tblgen_partial_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_partial_axes))))
    return emitError(loc, "'mesh.sharding' op ""attribute 'partial_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_partial_type && !((::llvm::isa<::mlir::mesh::ReductionKindAttr>(tblgen_partial_type))))
    return emitError(loc, "'mesh.sharding' op ""attribute 'partial_type' failed to satisfy constraint: Reduction of an iterator/mesh dimension.");

  if (tblgen_static_sharded_dims_offsets && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_static_sharded_dims_offsets))))
    return emitError(loc, "'mesh.sharding' op ""attribute 'static_sharded_dims_offsets' failed to satisfy constraint: i64 dense array attribute");

  if (tblgen_static_halo_sizes && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_static_halo_sizes))))
    return emitError(loc, "'mesh.sharding' op ""attribute 'static_halo_sizes' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ShardingOp::getODSOperandIndexAndLength(unsigned index) {
  ::llvm::ArrayRef<int32_t> sizeAttr = getProperties().operandSegmentSizes;

  unsigned start = 0;
  for (unsigned i = 0; i < index; ++i)
    start += sizeAttr[i];
  return {start, sizeAttr[index]};
}

::mlir::MutableOperandRange ShardingOp::getDynamicShardedDimsOffsetsMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second, ::mlir::MutableOperandRange::OperandSegment(0u, {getOperandSegmentSizesAttrName(), ::mlir::DenseI32ArrayAttr::get(getContext(), getProperties().operandSegmentSizes)}));
  return mutableRange;
}

::mlir::MutableOperandRange ShardingOp::getDynamicHaloSizesMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second, ::mlir::MutableOperandRange::OperandSegment(1u, {getOperandSegmentSizesAttrName(), ::mlir::DenseI32ArrayAttr::get(getContext(), getProperties().operandSegmentSizes)}));
  return mutableRange;
}

::llvm::LogicalResult ShardingOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.partial_axes;
       auto attr = dict.get("partial_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `partial_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.partial_type;
       auto attr = dict.get("partial_type");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `partial_type` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.split_axes;
       auto attr = dict.get("split_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `split_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.static_halo_sizes;
       auto attr = dict.get("static_halo_sizes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `static_halo_sizes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.static_sharded_dims_offsets;
       auto attr = dict.get("static_sharded_dims_offsets");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `static_sharded_dims_offsets` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
{

      auto setFromAttr = [] (auto &propStorage, ::mlir::Attribute propAttr,
               ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) -> ::mlir::LogicalResult {
        return convertFromAttribute(propStorage, propAttr, emitError);
      };
         auto attr = dict.get("operandSegmentSizes");   if (!attr) attr = dict.get("operand_segment_sizes");;
;
      if (attr && ::mlir::failed(setFromAttr(prop.operandSegmentSizes, attr, emitError)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::mlir::Attribute ShardingOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.partial_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("partial_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.partial_type;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("partial_type",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.split_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("split_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.static_halo_sizes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("static_halo_sizes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.static_sharded_dims_offsets;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("static_sharded_dims_offsets",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.operandSegmentSizes;
      auto attr = [&]() -> ::mlir::Attribute {
        return ::mlir::DenseI32ArrayAttr::get(ctx, propStorage);
      }();
      attrs.push_back(odsBuilder.getNamedAttr("operandSegmentSizes", attr));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ShardingOp::computePropertiesHash(const Properties &prop) {
  auto hash_operandSegmentSizes = [] (const auto &propStorage) -> llvm::hash_code {
    return ::llvm::hash_combine_range(std::begin(propStorage), std::end(propStorage));;
  };
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.partial_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.partial_type.getAsOpaquePointer()), 
    llvm::hash_value(prop.split_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.static_halo_sizes.getAsOpaquePointer()), 
    llvm::hash_value(prop.static_sharded_dims_offsets.getAsOpaquePointer()), 
    hash_operandSegmentSizes(prop.operandSegmentSizes));
}

std::optional<mlir::Attribute> ShardingOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "partial_axes")
      return prop.partial_axes;

    if (name == "partial_type")
      return prop.partial_type;

    if (name == "split_axes")
      return prop.split_axes;

    if (name == "static_halo_sizes")
      return prop.static_halo_sizes;

    if (name == "static_sharded_dims_offsets")
      return prop.static_sharded_dims_offsets;
    if (name == "operand_segment_sizes" || name == "operandSegmentSizes") return [&]() -> ::mlir::Attribute { return ::mlir::DenseI32ArrayAttr::get(ctx, prop.operandSegmentSizes); }();
  return std::nullopt;
}

void ShardingOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "partial_axes") {
       prop.partial_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.partial_axes)>>(value);
       return;
    }

    if (name == "partial_type") {
       prop.partial_type = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.partial_type)>>(value);
       return;
    }

    if (name == "split_axes") {
       prop.split_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.split_axes)>>(value);
       return;
    }

    if (name == "static_halo_sizes") {
       prop.static_halo_sizes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.static_halo_sizes)>>(value);
       return;
    }

    if (name == "static_sharded_dims_offsets") {
       prop.static_sharded_dims_offsets = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.static_sharded_dims_offsets)>>(value);
       return;
    }
        if (name == "operand_segment_sizes" || name == "operandSegmentSizes") {
       auto arrAttr = ::llvm::dyn_cast_or_null<::mlir::DenseI32ArrayAttr>(value);
       if (!arrAttr) return;
       if (arrAttr.size() != sizeof(prop.operandSegmentSizes) / sizeof(int32_t))
         return;
       llvm::copy(arrAttr.asArrayRef(), prop.operandSegmentSizes.begin());
       return;
    }
}

void ShardingOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.partial_axes) attrs.append("partial_axes", prop.partial_axes);

    if (prop.partial_type) attrs.append("partial_type", prop.partial_type);

    if (prop.split_axes) attrs.append("split_axes", prop.split_axes);

    if (prop.static_halo_sizes) attrs.append("static_halo_sizes", prop.static_halo_sizes);

    if (prop.static_sharded_dims_offsets) attrs.append("static_sharded_dims_offsets", prop.static_sharded_dims_offsets);
  attrs.append("operandSegmentSizes", [&]() -> ::mlir::Attribute { return ::mlir::DenseI32ArrayAttr::get(ctx, prop.operandSegmentSizes); }());
}

::llvm::LogicalResult ShardingOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getPartialAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "partial_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getPartialTypeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(attr, "partial_type", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSplitAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps8(attr, "split_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getStaticHaloSizesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "static_halo_sizes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getStaticShardedDimsOffsetsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "static_sharded_dims_offsets", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ShardingOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (reader.getBytecodeVersion() < /*kNativePropertiesODSSegmentSize=*/6) {
    auto &propStorage = prop.operandSegmentSizes;
    ::mlir::DenseI32ArrayAttr attr;
    if (::mlir::failed(reader.readAttribute(attr))) return ::mlir::failure();
    if (attr.size() > static_cast<int64_t>(sizeof(propStorage) / sizeof(int32_t))) {
      reader.emitError("size mismatch for operand/result_segment_size");
      return ::mlir::failure();
    }
    ::llvm::copy(::llvm::ArrayRef<int32_t>(attr), propStorage.begin());
  }

  if (::mlir::failed(reader.readOptionalAttribute(prop.partial_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.partial_type)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.split_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.static_halo_sizes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.static_sharded_dims_offsets)))
    return ::mlir::failure();

  {
    auto &propStorage = prop.operandSegmentSizes;
    auto readProp = [&]() {

  if (reader.getBytecodeVersion() >= /*kNativePropertiesODSSegmentSize=*/6)
    return reader.readSparseArray(::llvm::MutableArrayRef(propStorage));
;
      return ::mlir::success();
    };
    if (::mlir::failed(readProp()))
      return ::mlir::failure();
  }
  return ::mlir::success();
}

void ShardingOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

if (writer.getBytecodeVersion() < /*kNativePropertiesODSSegmentSize=*/6) {
  auto &propStorage = prop.operandSegmentSizes;
  writer.writeAttribute(::mlir::DenseI32ArrayAttr::get(this->getContext(), propStorage));
}

  writer.writeOptionalAttribute(prop.partial_axes);

  writer.writeOptionalAttribute(prop.partial_type);
  writer.writeAttribute(prop.split_axes);

  writer.writeOptionalAttribute(prop.static_halo_sizes);

  writer.writeOptionalAttribute(prop.static_sharded_dims_offsets);

  {
    auto &propStorage = prop.operandSegmentSizes;

  if (writer.getBytecodeVersion() >= /*kNativePropertiesODSSegmentSize=*/6)
    writer.writeSparseArray(::llvm::ArrayRef(propStorage));
;
  }
}

::llvm::StringRef ShardingOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::mesh::MeshAxesArrayAttr ShardingOp::getSplitAxes() {
  auto attr = getSplitAxesAttr();
  return ::llvm::cast<::mlir::mesh::MeshAxesArrayAttr>(attr);
}

::std::optional<::llvm::ArrayRef<int16_t>> ShardingOp::getPartialAxes() {
  auto attr = getPartialAxesAttr();
  return attr ? ::std::optional<::llvm::ArrayRef<int16_t>>(attr) : (::std::nullopt);
}

::std::optional<::mlir::mesh::ReductionKind> ShardingOp::getPartialType() {
  auto attr = getPartialTypeAttr();
  return attr ? ::std::optional<::mlir::mesh::ReductionKind>(attr.getValue()) : (::std::nullopt);
}

::llvm::ArrayRef<int64_t> ShardingOp::getStaticShardedDimsOffsets() {
  auto attr = getStaticShardedDimsOffsetsAttr();
  return attr;
}

::llvm::ArrayRef<int64_t> ShardingOp::getStaticHaloSizes() {
  auto attr = getStaticHaloSizesAttr();
  return attr;
}

void ShardingOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ShardingOp::setPartialAxes(::std::optional<::llvm::ArrayRef<int16_t>> attrValue) {
    auto &odsProp = getProperties().partial_axes;
    if (attrValue)
      odsProp = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(*attrValue);
    else
      odsProp = nullptr;
}

void ShardingOp::setPartialType(::std::optional<::mlir::mesh::ReductionKind> attrValue) {
    auto &odsProp = getProperties().partial_type;
    if (attrValue)
      odsProp = ::mlir::mesh::ReductionKindAttr::get(::mlir::Builder((*this)->getContext()).getContext(), *attrValue);
    else
      odsProp = nullptr;
}

void ShardingOp::setStaticShardedDimsOffsets(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().static_sharded_dims_offsets = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void ShardingOp::setStaticHaloSizes(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().static_halo_sizes = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, /*optional*/::mlir::DenseI16ArrayAttr partial_axes, /*optional*/::mlir::mesh::ReductionKindAttr partial_type, ::mlir::DenseI64ArrayAttr static_sharded_dims_offsets, ::mlir::ValueRange dynamic_sharded_dims_offsets, ::mlir::DenseI64ArrayAttr static_halo_sizes, ::mlir::ValueRange dynamic_halo_sizes) {
  odsState.addOperands(dynamic_sharded_dims_offsets);
  odsState.addOperands(dynamic_halo_sizes);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsets.size()), static_cast<int32_t>(dynamic_halo_sizes.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (partial_axes) {
    odsState.getOrAddProperties<Properties>().partial_axes = partial_axes;
  }
  if (partial_type) {
    odsState.getOrAddProperties<Properties>().partial_type = partial_type;
  }
  if (static_sharded_dims_offsets) {
    odsState.getOrAddProperties<Properties>().static_sharded_dims_offsets = static_sharded_dims_offsets;
  }
  if (static_halo_sizes) {
    odsState.getOrAddProperties<Properties>().static_halo_sizes = static_halo_sizes;
  }
  odsState.addTypes(result);
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::FlatSymbolRefAttr mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, /*optional*/::mlir::DenseI16ArrayAttr partial_axes, /*optional*/::mlir::mesh::ReductionKindAttr partial_type, ::mlir::DenseI64ArrayAttr static_sharded_dims_offsets, ::mlir::ValueRange dynamic_sharded_dims_offsets, ::mlir::DenseI64ArrayAttr static_halo_sizes, ::mlir::ValueRange dynamic_halo_sizes) {
  odsState.addOperands(dynamic_sharded_dims_offsets);
  odsState.addOperands(dynamic_halo_sizes);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsets.size()), static_cast<int32_t>(dynamic_halo_sizes.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (partial_axes) {
    odsState.getOrAddProperties<Properties>().partial_axes = partial_axes;
  }
  if (partial_type) {
    odsState.getOrAddProperties<Properties>().partial_type = partial_type;
  }
  if (static_sharded_dims_offsets) {
    odsState.getOrAddProperties<Properties>().static_sharded_dims_offsets = static_sharded_dims_offsets;
  }
  if (static_halo_sizes) {
    odsState.getOrAddProperties<Properties>().static_halo_sizes = static_halo_sizes;
  }

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(ShardingOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, /*optional*/::mlir::DenseI16ArrayAttr partial_axes, /*optional*/::mlir::mesh::ReductionKindAttr partial_type, ::mlir::DenseI64ArrayAttr static_sharded_dims_offsets, ::mlir::ValueRange dynamic_sharded_dims_offsets, ::mlir::DenseI64ArrayAttr static_halo_sizes, ::mlir::ValueRange dynamic_halo_sizes) {
  odsState.addOperands(dynamic_sharded_dims_offsets);
  odsState.addOperands(dynamic_halo_sizes);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsets.size()), static_cast<int32_t>(dynamic_halo_sizes.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (partial_axes) {
    odsState.getOrAddProperties<Properties>().partial_axes = partial_axes;
  }
  if (partial_type) {
    odsState.getOrAddProperties<Properties>().partial_type = partial_type;
  }
  if (static_sharded_dims_offsets) {
    odsState.getOrAddProperties<Properties>().static_sharded_dims_offsets = static_sharded_dims_offsets;
  }
  if (static_halo_sizes) {
    odsState.getOrAddProperties<Properties>().static_halo_sizes = static_halo_sizes;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, /*optional*/::mlir::DenseI16ArrayAttr partial_axes, /*optional*/::mlir::mesh::ReductionKindAttr partial_type, ::llvm::ArrayRef<int64_t> static_sharded_dims_offsets, ::mlir::ValueRange dynamic_sharded_dims_offsets, ::llvm::ArrayRef<int64_t> static_halo_sizes, ::mlir::ValueRange dynamic_halo_sizes) {
  odsState.addOperands(dynamic_sharded_dims_offsets);
  odsState.addOperands(dynamic_halo_sizes);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsets.size()), static_cast<int32_t>(dynamic_halo_sizes.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (partial_axes) {
    odsState.getOrAddProperties<Properties>().partial_axes = partial_axes;
  }
  if (partial_type) {
    odsState.getOrAddProperties<Properties>().partial_type = partial_type;
  }
  odsState.getOrAddProperties<Properties>().static_sharded_dims_offsets = odsBuilder.getDenseI64ArrayAttr(static_sharded_dims_offsets);
  odsState.getOrAddProperties<Properties>().static_halo_sizes = odsBuilder.getDenseI64ArrayAttr(static_halo_sizes);
  odsState.addTypes(result);
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::llvm::StringRef mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, /*optional*/::mlir::DenseI16ArrayAttr partial_axes, /*optional*/::mlir::mesh::ReductionKindAttr partial_type, ::llvm::ArrayRef<int64_t> static_sharded_dims_offsets, ::mlir::ValueRange dynamic_sharded_dims_offsets, ::llvm::ArrayRef<int64_t> static_halo_sizes, ::mlir::ValueRange dynamic_halo_sizes) {
  odsState.addOperands(dynamic_sharded_dims_offsets);
  odsState.addOperands(dynamic_halo_sizes);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsets.size()), static_cast<int32_t>(dynamic_halo_sizes.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (partial_axes) {
    odsState.getOrAddProperties<Properties>().partial_axes = partial_axes;
  }
  if (partial_type) {
    odsState.getOrAddProperties<Properties>().partial_type = partial_type;
  }
  odsState.getOrAddProperties<Properties>().static_sharded_dims_offsets = odsBuilder.getDenseI64ArrayAttr(static_sharded_dims_offsets);
  odsState.getOrAddProperties<Properties>().static_halo_sizes = odsBuilder.getDenseI64ArrayAttr(static_halo_sizes);

        ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
        if (::mlir::succeeded(ShardingOp::inferReturnTypes(odsBuilder.getContext(),
                      odsState.location, odsState.operands,
                      odsState.attributes.getDictionary(odsState.getContext()),
                      odsState.getRawProperties(),
                      odsState.regions, inferredReturnTypes)))
          odsState.addTypes(inferredReturnTypes);
        else
          ::mlir::detail::reportFatalInferReturnTypesError(odsState);
        
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, /*optional*/::mlir::DenseI16ArrayAttr partial_axes, /*optional*/::mlir::mesh::ReductionKindAttr partial_type, ::llvm::ArrayRef<int64_t> static_sharded_dims_offsets, ::mlir::ValueRange dynamic_sharded_dims_offsets, ::llvm::ArrayRef<int64_t> static_halo_sizes, ::mlir::ValueRange dynamic_halo_sizes) {
  odsState.addOperands(dynamic_sharded_dims_offsets);
  odsState.addOperands(dynamic_halo_sizes);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsets.size()), static_cast<int32_t>(dynamic_halo_sizes.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (partial_axes) {
    odsState.getOrAddProperties<Properties>().partial_axes = partial_axes;
  }
  if (partial_type) {
    odsState.getOrAddProperties<Properties>().partial_type = partial_type;
  }
  odsState.getOrAddProperties<Properties>().static_sharded_dims_offsets = odsBuilder.getDenseI64ArrayAttr(static_sharded_dims_offsets);
  odsState.getOrAddProperties<Properties>().static_halo_sizes = odsBuilder.getDenseI64ArrayAttr(static_halo_sizes);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ShardingOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ShardingOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ShardingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ShardingOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
  ::llvm::SmallVector<::mlir::Type, 2> inferredReturnTypes;
  if (::mlir::succeeded(ShardingOp::inferReturnTypes(odsBuilder.getContext(),
          odsState.location, operands,
          odsState.attributes.getDictionary(odsState.getContext()),
          odsState.getRawProperties(),
          odsState.regions, inferredReturnTypes))) {
    assert(inferredReturnTypes.size() == 1u && "mismatched number of return types");
    odsState.addTypes(inferredReturnTypes);
  } else {
    ::llvm::report_fatal_error("Failed to infer result type(s).");
  }
}

void ShardingOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.static_sharded_dims_offsets)
    properties.static_sharded_dims_offsets = odsBuilder.getDenseI64ArrayAttr({});
  if (!properties.static_halo_sizes)
    properties.static_halo_sizes = odsBuilder.getDenseI64ArrayAttr({});
}

::llvm::LogicalResult ShardingOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_partial_axes = getProperties().partial_axes; (void)tblgen_partial_axes;
  auto tblgen_partial_type = getProperties().partial_type; (void)tblgen_partial_type;
  auto tblgen_split_axes = getProperties().split_axes; (void)tblgen_split_axes;
  if (!tblgen_split_axes) return emitOpError("requires attribute 'split_axes'");
  auto tblgen_static_halo_sizes = getProperties().static_halo_sizes; (void)tblgen_static_halo_sizes;
  auto tblgen_static_sharded_dims_offsets = getProperties().static_sharded_dims_offsets; (void)tblgen_static_sharded_dims_offsets;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps8(*this, tblgen_split_axes, "split_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_partial_axes, "partial_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps4(*this, tblgen_partial_type, "partial_type")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_static_sharded_dims_offsets, "static_sharded_dims_offsets")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_static_halo_sizes, "static_halo_sizes")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps6(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps6(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps5(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ShardingOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::llvm::LogicalResult ShardingOp::inferReturnTypes(::mlir::MLIRContext *context, ::std::optional<::mlir::Location> location, ::mlir::ValueRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::Type>&inferredReturnTypes) {
  inferredReturnTypes.resize(1);
  ::mlir::Builder odsBuilder(context);
  ::mlir::Type odsInferredType0 = odsBuilder.getType<::mlir::mesh::ShardingType>();
  inferredReturnTypes[0] = odsInferredType0;
  return ::mlir::success();
}

::mlir::ParseResult ShardingOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::mesh::MeshAxesArrayAttr split_axesAttr;
  ::mlir::mesh::ReductionKindAttr partial_typeAttr;
  ::mlir::DenseI16ArrayAttr partial_axesAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> dynamic_halo_sizesOperands;
  ::llvm::SMLoc dynamic_halo_sizesOperandsLoc;
  (void)dynamic_halo_sizesOperandsLoc;
  ::mlir::DenseI64ArrayAttr static_halo_sizesAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> dynamic_sharded_dims_offsetsOperands;
  ::llvm::SMLoc dynamic_sharded_dims_offsetsOperandsLoc;
  (void)dynamic_sharded_dims_offsetsOperandsLoc;
  ::mlir::DenseI64ArrayAttr static_sharded_dims_offsetsAttr;
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ShardingOp::Properties>().mesh = meshAttr;
  if (parser.parseKeyword("split_axes"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(split_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (split_axesAttr) result.getOrAddProperties<ShardingOp::Properties>().split_axes = split_axesAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("partial"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(partial_typeAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (partial_typeAttr) result.getOrAddProperties<ShardingOp::Properties>().partial_type = partial_typeAttr;

  if (parser.parseCustomAttributeWithFallback(partial_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (partial_axesAttr) result.getOrAddProperties<ShardingOp::Properties>().partial_axes = partial_axesAttr;
  }
  if (::mlir::succeeded(parser.parseOptionalKeyword("halo_sizes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    dynamic_halo_sizesOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, dynamic_halo_sizesOperands, static_halo_sizesAttr);
    if (odsResult) return ::mlir::failure();
    if (static_halo_sizesAttr)
      result.getOrAddProperties<ShardingOp::Properties>().static_halo_sizes = static_halo_sizesAttr;
  }
  }
  if (::mlir::succeeded(parser.parseOptionalKeyword("sharded_dims_offsets"))) {
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    dynamic_sharded_dims_offsetsOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, dynamic_sharded_dims_offsetsOperands, static_sharded_dims_offsetsAttr);
    if (odsResult) return ::mlir::failure();
    if (static_sharded_dims_offsetsAttr)
      result.getOrAddProperties<ShardingOp::Properties>().static_sharded_dims_offsets = static_sharded_dims_offsetsAttr;
  }
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::mesh::ShardingType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(dynamic_sharded_dims_offsetsOperands.size()), static_cast<int32_t>(dynamic_halo_sizesOperands.size())}), result.getOrAddProperties<ShardingOp::Properties>().operandSegmentSizes.begin());
  ::mlir::Type odsBuildableType0 = parser.getBuilder().getIntegerType(64);
  result.addTypes(resultTypes);
  if (parser.resolveOperands(dynamic_sharded_dims_offsetsOperands, odsBuildableType0, dynamic_sharded_dims_offsetsOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(dynamic_halo_sizesOperands, odsBuildableType0, dynamic_halo_sizesOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShardingOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  _odsPrinter << ' ' << "split_axes";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
_odsPrinter.printStrippedAttrOrType(getSplitAxesAttr());
  if (getPartialAxesAttr()) {
    _odsPrinter << ' ' << "partial";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getPartialTypeAttr());
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getPartialAxesAttr());
  }
  if (((!getDynamicHaloSizes().empty()) || (getStaticHaloSizesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI64ArrayAttr({})))) {
    _odsPrinter << ' ' << "halo_sizes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
    printDynamicIndexList(_odsPrinter, *this, getDynamicHaloSizes(), getStaticHaloSizesAttr());
  }
  if (((!getDynamicShardedDimsOffsets().empty()) || (getStaticShardedDimsOffsetsAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI64ArrayAttr({})))) {
    _odsPrinter << ' ' << "sharded_dims_offsets";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
    printDynamicIndexList(_odsPrinter, *this, getDynamicShardedDimsOffsets(), getStaticShardedDimsOffsetsAttr());
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("operandSegmentSizes");
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("split_axes");
  elidedAttrs.push_back("partial_type");
  elidedAttrs.push_back("partial_axes");
  elidedAttrs.push_back("static_halo_sizes");
  elidedAttrs.push_back("static_sharded_dims_offsets");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getStaticShardedDimsOffsetsAttr();
     if(attr && (attr == odsBuilder.getDenseI64ArrayAttr({})))
       elidedAttrs.push_back("static_sharded_dims_offsets");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getStaticHaloSizesAttr();
     if(attr && (attr == odsBuilder.getDenseI64ArrayAttr({})))
       elidedAttrs.push_back("static_halo_sizes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::mesh::ShardingType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void ShardingOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ShardingOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::ShiftOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ShiftOpGenericAdaptorBase::ShiftOpGenericAdaptorBase(ShiftOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef ShiftOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::DenseI16ArrayAttr ShiftOpGenericAdaptorBase::getMeshAxesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI16ArrayAttr>(getProperties().mesh_axes);
  return attr;
}

::llvm::ArrayRef<int16_t> ShiftOpGenericAdaptorBase::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt ShiftOpGenericAdaptorBase::getShiftAxis() {
  auto attr = getShiftAxisAttr();
  return attr.getValue();
}

uint64_t ShiftOpGenericAdaptorBase::getOffset() {
  auto attr = getOffsetAttr();
  return attr.getValue().getZExtValue();
}

::mlir::UnitAttr ShiftOpGenericAdaptorBase::getRotateAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().rotate);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ShiftOpGenericAdaptorBase::getRotate() {
  auto attr = getRotateAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
ShiftOpAdaptor::ShiftOpAdaptor(ShiftOp op) : ShiftOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ShiftOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.shift' op ""requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_offset = getProperties().offset; (void)tblgen_offset;
  if (!tblgen_offset) return emitError(loc, "'mesh.shift' op ""requires attribute 'offset'");
  auto tblgen_rotate = getProperties().rotate; (void)tblgen_rotate;
  auto tblgen_shift_axis = getProperties().shift_axis; (void)tblgen_shift_axis;
  if (!tblgen_shift_axis) return emitError(loc, "'mesh.shift' op ""requires attribute 'shift_axis'");

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.shift' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_mesh_axes && !((::llvm::isa<::mlir::DenseI16ArrayAttr>(tblgen_mesh_axes))))
    return emitError(loc, "'mesh.shift' op ""attribute 'mesh_axes' failed to satisfy constraint: i16 dense array attribute");

  if (tblgen_shift_axis && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_shift_axis))) && ((::llvm::isa<::mlir::IndexType>(::llvm::cast<::mlir::IntegerAttr>(tblgen_shift_axis).getType())))))
    return emitError(loc, "'mesh.shift' op ""attribute 'shift_axis' failed to satisfy constraint: index attribute");

  if (tblgen_offset && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_offset))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_offset).getType().isSignlessInteger(64)))))
    return emitError(loc, "'mesh.shift' op ""attribute 'offset' failed to satisfy constraint: 64-bit signless integer attribute");

  if (tblgen_rotate && !((::llvm::isa<::mlir::UnitAttr>(tblgen_rotate))))
    return emitError(loc, "'mesh.shift' op ""attribute 'rotate' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ShiftOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.mesh_axes;
       auto attr = dict.get("mesh_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.offset;
       auto attr = dict.get("offset");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `offset` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.rotate;
       auto attr = dict.get("rotate");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `rotate` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.shift_axis;
       auto attr = dict.get("shift_axis");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `shift_axis` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ShiftOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.mesh_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.offset;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("offset",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.rotate;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("rotate",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.shift_axis;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("shift_axis",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ShiftOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.mesh_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.offset.getAsOpaquePointer()), 
    llvm::hash_value(prop.rotate.getAsOpaquePointer()), 
    llvm::hash_value(prop.shift_axis.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ShiftOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "mesh_axes")
      return prop.mesh_axes;

    if (name == "offset")
      return prop.offset;

    if (name == "rotate")
      return prop.rotate;

    if (name == "shift_axis")
      return prop.shift_axis;
  return std::nullopt;
}

void ShiftOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "mesh_axes") {
       prop.mesh_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh_axes)>>(value);
       return;
    }

    if (name == "offset") {
       prop.offset = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.offset)>>(value);
       return;
    }

    if (name == "rotate") {
       prop.rotate = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.rotate)>>(value);
       return;
    }

    if (name == "shift_axis") {
       prop.shift_axis = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.shift_axis)>>(value);
       return;
    }
}

void ShiftOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.mesh_axes) attrs.append("mesh_axes", prop.mesh_axes);

    if (prop.offset) attrs.append("offset", prop.offset);

    if (prop.rotate) attrs.append("rotate", prop.rotate);

    if (prop.shift_axis) attrs.append("shift_axis", prop.shift_axis);
}

::llvm::LogicalResult ShiftOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMeshAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(attr, "mesh_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getOffsetAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps9(attr, "offset", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRotateAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps7(attr, "rotate", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getShiftAxisAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(attr, "shift_axis", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ShiftOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.mesh_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.offset)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.rotate)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.shift_axis)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShiftOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);

  writer.writeOptionalAttribute(prop.mesh_axes);
  writer.writeAttribute(prop.offset);

  writer.writeOptionalAttribute(prop.rotate);
  writer.writeAttribute(prop.shift_axis);
}

::llvm::StringRef ShiftOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::llvm::ArrayRef<int16_t> ShiftOp::getMeshAxes() {
  auto attr = getMeshAxesAttr();
  return attr;
}

::llvm::APInt ShiftOp::getShiftAxis() {
  auto attr = getShiftAxisAttr();
  return attr.getValue();
}

uint64_t ShiftOp::getOffset() {
  auto attr = getOffsetAttr();
  return attr.getValue().getZExtValue();
}

bool ShiftOp::getRotate() {
  auto attr = getRotateAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void ShiftOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void ShiftOp::setMeshAxes(::llvm::ArrayRef<int16_t> attrValue) {
  getProperties().mesh_axes = ::mlir::Builder((*this)->getContext()).getDenseI16ArrayAttr(attrValue);
}

void ShiftOp::setShiftAxis(::llvm::APInt attrValue) {
  getProperties().shift_axis = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIndexType(), attrValue);
}

void ShiftOp::setOffset(uint64_t attrValue) {
  getProperties().offset = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), attrValue);
}

void ShiftOp::setRotate(bool attrValue) {
    auto &odsProp = getProperties().rotate;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ShiftOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr shift_axis, ::mlir::IntegerAttr offset, /*optional*/::mlir::UnitAttr rotate) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().shift_axis = shift_axis;
  odsState.getOrAddProperties<Properties>().offset = offset;
  if (rotate) {
    odsState.getOrAddProperties<Properties>().rotate = rotate;
  }
  odsState.addTypes(result);
}

void ShiftOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::FlatSymbolRefAttr mesh, ::mlir::DenseI16ArrayAttr mesh_axes, ::mlir::Value input, ::mlir::IntegerAttr shift_axis, ::mlir::IntegerAttr offset, /*optional*/::mlir::UnitAttr rotate) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  if (mesh_axes) {
    odsState.getOrAddProperties<Properties>().mesh_axes = mesh_axes;
  }
  odsState.getOrAddProperties<Properties>().shift_axis = shift_axis;
  odsState.getOrAddProperties<Properties>().offset = offset;
  if (rotate) {
    odsState.getOrAddProperties<Properties>().rotate = rotate;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ShiftOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt shift_axis, uint64_t offset, /*optional*/bool rotate) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().shift_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), shift_axis);
  odsState.getOrAddProperties<Properties>().offset = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), offset);
  if (rotate) {
    odsState.getOrAddProperties<Properties>().rotate = ((rotate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes(result);
}

void ShiftOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef mesh, ::llvm::ArrayRef<int16_t> mesh_axes, ::mlir::Value input, ::llvm::APInt shift_axis, uint64_t offset, /*optional*/bool rotate) {
  odsState.addOperands(input);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().mesh_axes = odsBuilder.getDenseI16ArrayAttr(mesh_axes);
  odsState.getOrAddProperties<Properties>().shift_axis = odsBuilder.getIntegerAttr(odsBuilder.getIndexType(), shift_axis);
  odsState.getOrAddProperties<Properties>().offset = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), offset);
  if (rotate) {
    odsState.getOrAddProperties<Properties>().rotate = ((rotate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ShiftOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ShiftOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ShiftOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.mesh_axes)
    properties.mesh_axes = odsBuilder.getDenseI16ArrayAttr({});
}

::llvm::LogicalResult ShiftOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_mesh_axes = getProperties().mesh_axes; (void)tblgen_mesh_axes;
  auto tblgen_offset = getProperties().offset; (void)tblgen_offset;
  if (!tblgen_offset) return emitOpError("requires attribute 'offset'");
  auto tblgen_rotate = getProperties().rotate; (void)tblgen_rotate;
  auto tblgen_shift_axis = getProperties().shift_axis; (void)tblgen_shift_axis;
  if (!tblgen_shift_axis) return emitOpError("requires attribute 'shift_axis'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps2(*this, tblgen_mesh_axes, "mesh_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps3(*this, tblgen_shift_axis, "shift_axis")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps9(*this, tblgen_offset, "offset")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps7(*this, tblgen_rotate, "rotate")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ShiftOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ShiftOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::DenseI16ArrayAttr mesh_axesAttr;
  ::mlir::IntegerAttr shift_axisAttr;
  ::mlir::IntegerAttr offsetAttr;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<ShiftOp::Properties>().mesh = meshAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("mesh_axes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(mesh_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (mesh_axesAttr) result.getOrAddProperties<ShiftOp::Properties>().mesh_axes = mesh_axesAttr;
  }
  if (parser.parseKeyword("shift_axis"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(shift_axisAttr, parser.getBuilder().getIndexType())) {
    return ::mlir::failure();
  }
  if (shift_axisAttr) result.getOrAddProperties<ShiftOp::Properties>().shift_axis = shift_axisAttr;
  if (parser.parseKeyword("offset"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(offsetAttr, parser.getBuilder().getIntegerType(64))) {
    return ::mlir::failure();
  }
  if (offsetAttr) result.getOrAddProperties<ShiftOp::Properties>().offset = offsetAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("rotate"))) {
    result.getOrAddProperties<ShiftOp::Properties>().rotate = parser.getBuilder().getUnitAttr();  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::TensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::RankedTensorType type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ShiftOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  if (getMeshAxesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI16ArrayAttr({})) {
    _odsPrinter << ' ' << "mesh_axes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
  _odsPrinter.printStrippedAttrOrType(getMeshAxesAttr());
  }
  _odsPrinter << ' ' << "shift_axis";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getShiftAxisAttr());
  _odsPrinter << ' ' << "offset";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getOffsetAttr());
  if ((getRotateAttr() && getRotateAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr))) {
    _odsPrinter << ' ' << "rotate";
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("mesh_axes");
  elidedAttrs.push_back("shift_axis");
  elidedAttrs.push_back("offset");
  elidedAttrs.push_back("rotate");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMeshAxesAttr();
     if(attr && (attr == odsBuilder.getDenseI16ArrayAttr({})))
       elidedAttrs.push_back("mesh_axes");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getRotateAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("rotate");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::TensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::RankedTensorType>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void ShiftOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::ShiftOp)

namespace mlir {
namespace mesh {

//===----------------------------------------------------------------------===//
// ::mlir::mesh::UpdateHaloOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
UpdateHaloOpGenericAdaptorBase::UpdateHaloOpGenericAdaptorBase(UpdateHaloOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> UpdateHaloOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef UpdateHaloOpGenericAdaptorBase::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::mesh::MeshAxesArrayAttr UpdateHaloOpGenericAdaptorBase::getSplitAxes() {
  auto attr = getSplitAxesAttr();
  return ::llvm::cast<::mlir::mesh::MeshAxesArrayAttr>(attr);
}

::mlir::DenseI64ArrayAttr UpdateHaloOpGenericAdaptorBase::getStaticHaloSizesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseI64ArrayAttr>(getProperties().static_halo_sizes);
  return attr;
}

::llvm::ArrayRef<int64_t> UpdateHaloOpGenericAdaptorBase::getStaticHaloSizes() {
  auto attr = getStaticHaloSizesAttr();
  return attr;
}

} // namespace detail
UpdateHaloOpAdaptor::UpdateHaloOpAdaptor(UpdateHaloOp op) : UpdateHaloOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult UpdateHaloOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitError(loc, "'mesh.update_halo' op ""requires attribute 'mesh'");
  auto tblgen_split_axes = getProperties().split_axes; (void)tblgen_split_axes;
  if (!tblgen_split_axes) return emitError(loc, "'mesh.update_halo' op ""requires attribute 'split_axes'");
  auto tblgen_static_halo_sizes = getProperties().static_halo_sizes; (void)tblgen_static_halo_sizes;

  if (tblgen_mesh && !((::llvm::isa<::mlir::FlatSymbolRefAttr>(tblgen_mesh))))
    return emitError(loc, "'mesh.update_halo' op ""attribute 'mesh' failed to satisfy constraint: flat symbol reference attribute");

  if (tblgen_split_axes && !((::llvm::isa<::mlir::mesh::MeshAxesArrayAttr>(tblgen_split_axes))))
    return emitError(loc, "'mesh.update_halo' op ""attribute 'split_axes' failed to satisfy constraint: ");

  if (tblgen_static_halo_sizes && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_static_halo_sizes))))
    return emitError(loc, "'mesh.update_halo' op ""attribute 'static_halo_sizes' failed to satisfy constraint: i64 dense array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> UpdateHaloOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange UpdateHaloOp::getHaloSizesMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult UpdateHaloOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.mesh;
       auto attr = dict.get("mesh");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `mesh` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.split_axes;
       auto attr = dict.get("split_axes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `split_axes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.static_halo_sizes;
       auto attr = dict.get("static_halo_sizes");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `static_halo_sizes` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute UpdateHaloOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.mesh;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("mesh",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.split_axes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("split_axes",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.static_halo_sizes;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("static_halo_sizes",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code UpdateHaloOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.mesh.getAsOpaquePointer()), 
    llvm::hash_value(prop.split_axes.getAsOpaquePointer()), 
    llvm::hash_value(prop.static_halo_sizes.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> UpdateHaloOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "mesh")
      return prop.mesh;

    if (name == "split_axes")
      return prop.split_axes;

    if (name == "static_halo_sizes")
      return prop.static_halo_sizes;
  return std::nullopt;
}

void UpdateHaloOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "mesh") {
       prop.mesh = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.mesh)>>(value);
       return;
    }

    if (name == "split_axes") {
       prop.split_axes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.split_axes)>>(value);
       return;
    }

    if (name == "static_halo_sizes") {
       prop.static_halo_sizes = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.static_halo_sizes)>>(value);
       return;
    }
}

void UpdateHaloOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.mesh) attrs.append("mesh", prop.mesh);

    if (prop.split_axes) attrs.append("split_axes", prop.split_axes);

    if (prop.static_halo_sizes) attrs.append("static_halo_sizes", prop.static_halo_sizes);
}

::llvm::LogicalResult UpdateHaloOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMeshAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(attr, "mesh", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSplitAxesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps8(attr, "split_axes", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getStaticHaloSizesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(attr, "static_halo_sizes", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult UpdateHaloOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.mesh)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.split_axes)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.static_halo_sizes)))
    return ::mlir::failure();
  return ::mlir::success();
}

void UpdateHaloOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.mesh);
  writer.writeAttribute(prop.split_axes);

  writer.writeOptionalAttribute(prop.static_halo_sizes);
}

::llvm::StringRef UpdateHaloOp::getMesh() {
  auto attr = getMeshAttr();
  return attr.getValue();
}

::mlir::mesh::MeshAxesArrayAttr UpdateHaloOp::getSplitAxes() {
  auto attr = getSplitAxesAttr();
  return ::llvm::cast<::mlir::mesh::MeshAxesArrayAttr>(attr);
}

::llvm::ArrayRef<int64_t> UpdateHaloOp::getStaticHaloSizes() {
  auto attr = getStaticHaloSizesAttr();
  return attr;
}

void UpdateHaloOp::setMesh(::llvm::StringRef attrValue) {
  getProperties().mesh = ::mlir::SymbolRefAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void UpdateHaloOp::setStaticHaloSizes(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().static_halo_sizes = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void UpdateHaloOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value destination, ::mlir::FlatSymbolRefAttr mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, ::mlir::ValueRange halo_sizes, ::mlir::DenseI64ArrayAttr static_halo_sizes) {
  odsState.addOperands(destination);
  odsState.addOperands(halo_sizes);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (static_halo_sizes) {
    odsState.getOrAddProperties<Properties>().static_halo_sizes = static_halo_sizes;
  }
  odsState.addTypes(result);
}

void UpdateHaloOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value destination, ::mlir::FlatSymbolRefAttr mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, ::mlir::ValueRange halo_sizes, ::mlir::DenseI64ArrayAttr static_halo_sizes) {
  odsState.addOperands(destination);
  odsState.addOperands(halo_sizes);
  odsState.getOrAddProperties<Properties>().mesh = mesh;
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  if (static_halo_sizes) {
    odsState.getOrAddProperties<Properties>().static_halo_sizes = static_halo_sizes;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void UpdateHaloOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value destination, ::llvm::StringRef mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, ::mlir::ValueRange halo_sizes, ::llvm::ArrayRef<int64_t> static_halo_sizes) {
  odsState.addOperands(destination);
  odsState.addOperands(halo_sizes);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  odsState.getOrAddProperties<Properties>().static_halo_sizes = odsBuilder.getDenseI64ArrayAttr(static_halo_sizes);
  odsState.addTypes(result);
}

void UpdateHaloOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value destination, ::llvm::StringRef mesh, ::mlir::mesh::MeshAxesArrayAttr split_axes, ::mlir::ValueRange halo_sizes, ::llvm::ArrayRef<int64_t> static_halo_sizes) {
  odsState.addOperands(destination);
  odsState.addOperands(halo_sizes);
  odsState.getOrAddProperties<Properties>().mesh = ::mlir::SymbolRefAttr::get(odsBuilder.getContext(), mesh);
  odsState.getOrAddProperties<Properties>().split_axes = split_axes;
  odsState.getOrAddProperties<Properties>().static_halo_sizes = odsBuilder.getDenseI64ArrayAttr(static_halo_sizes);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void UpdateHaloOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<UpdateHaloOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void UpdateHaloOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.static_halo_sizes)
    properties.static_halo_sizes = odsBuilder.getDenseI64ArrayAttr({});
}

::llvm::LogicalResult UpdateHaloOp::verifyInvariantsImpl() {
  auto tblgen_mesh = getProperties().mesh; (void)tblgen_mesh;
  if (!tblgen_mesh) return emitOpError("requires attribute 'mesh'");
  auto tblgen_split_axes = getProperties().split_axes; (void)tblgen_split_axes;
  if (!tblgen_split_axes) return emitOpError("requires attribute 'split_axes'");
  auto tblgen_static_halo_sizes = getProperties().static_halo_sizes; (void)tblgen_static_halo_sizes;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps1(*this, tblgen_mesh, "mesh")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps8(*this, tblgen_split_axes, "split_axes")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_MeshOps5(*this, tblgen_static_halo_sizes, "static_halo_sizes")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps7(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps6(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_MeshOps7(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!((std::equal_to<>()((*this->getODSResults(0).begin()).getType(), (*this->getODSOperands(0).begin()).getType()))))
    return emitOpError("failed to verify that result has same type as destination");
  return ::mlir::success();
}

::llvm::LogicalResult UpdateHaloOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult UpdateHaloOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand destinationRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> destinationOperands(&destinationRawOperand, 1);  ::llvm::SMLoc destinationOperandsLoc;
  (void)destinationOperandsLoc;
  ::mlir::FlatSymbolRefAttr meshAttr;
  ::mlir::mesh::MeshAxesArrayAttr split_axesAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> halo_sizesOperands;
  ::llvm::SMLoc halo_sizesOperandsLoc;
  (void)halo_sizesOperandsLoc;
  ::mlir::DenseI64ArrayAttr static_halo_sizesAttr;
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);

  destinationOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(destinationRawOperand))
    return ::mlir::failure();
  if (parser.parseKeyword("on"))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(meshAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (meshAttr) result.getOrAddProperties<UpdateHaloOp::Properties>().mesh = meshAttr;
  if (parser.parseKeyword("split_axes"))
    return ::mlir::failure();
  if (parser.parseEqual())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(split_axesAttr, ::mlir::Type{})) {
    return ::mlir::failure();
  }
  if (split_axesAttr) result.getOrAddProperties<UpdateHaloOp::Properties>().split_axes = split_axesAttr;
  if (::mlir::succeeded(parser.parseOptionalKeyword("halo_sizes"))) {
  if (parser.parseEqual())
    return ::mlir::failure();
  {
    halo_sizesOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseDynamicIndexList(parser, halo_sizesOperands, static_halo_sizesAttr);
    if (odsResult) return ::mlir::failure();
    if (static_halo_sizesAttr)
      result.getOrAddProperties<UpdateHaloOp::Properties>().static_halo_sizes = static_halo_sizesAttr;
  }
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::Type type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  for (::mlir::Type type : resultTypes) {
    (void)type;
    if (!(((((::llvm::isa<::mlir::MemRefType>(type))) && ([](::mlir::Type elementType) { return (true); }(::llvm::cast<::mlir::ShapedType>(type).getElementType()))) && (((::llvm::cast<::mlir::ShapedType>(type).hasRank())) && ((::llvm::cast<::mlir::ShapedType>(type).getRank() >= 1)))) || ((((::llvm::isa<::mlir::TensorType>(type))) && (((::llvm::cast<::mlir::ShapedType>(type).hasRank())) && ((::llvm::cast<::mlir::ShapedType>(type).getRank() >= 1)))) && ([](::mlir::Type elementType) { return (true); }(::llvm::cast<::mlir::ShapedType>(type).getElementType()))))) {
      return parser.emitError(parser.getNameLoc()) << "'result' must be non-0-ranked.memref of any type values or non-0-ranked.tensor of any type values, but got " << type;
    }
  }
  ::mlir::Type odsBuildableType0 = parser.getBuilder().getIntegerType(64);
  result.addTypes(resultTypes);
  if (parser.resolveOperands(destinationOperands, resultTypes[0], destinationOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(halo_sizesOperands, odsBuildableType0, halo_sizesOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void UpdateHaloOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getDestination();
  _odsPrinter << ' ' << "on";
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMeshAttr());
  _odsPrinter << ' ' << "split_axes";
  _odsPrinter << ' ' << "=";
  _odsPrinter << ' ';
_odsPrinter.printStrippedAttrOrType(getSplitAxesAttr());
  if (((!getHaloSizes().empty()) || (getStaticHaloSizesAttr() != ::mlir::OpBuilder((*this)->getContext()).getDenseI64ArrayAttr({})))) {
    _odsPrinter << ' ' << "halo_sizes";
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
    printDynamicIndexList(_odsPrinter, *this, getHaloSizes(), getStaticHaloSizesAttr());
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("mesh");
  elidedAttrs.push_back("split_axes");
  elidedAttrs.push_back("static_halo_sizes");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getStaticHaloSizesAttr();
     if(attr && (attr == odsBuilder.getDenseI64ArrayAttr({})))
       elidedAttrs.push_back("static_halo_sizes");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::Type>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

void UpdateHaloOp::getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects) {
}

} // namespace mesh
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::mesh::UpdateHaloOp)


#endif  // GET_OP_CLASSES

