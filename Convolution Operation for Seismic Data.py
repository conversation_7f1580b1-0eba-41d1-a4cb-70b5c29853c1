import tensorflow as tf
from tensorflow.keras.layers import Conv1D, Conv2D, MaxPooling1D, MaxPooling2D

# 1D Convolution for seismic traces
def create_1d_cnn_for_seismic():
    """1D CNN for seismic trace analysis"""
    model = Sequential([
        Conv1D(32, kernel_size=5, activation='relu', input_shape=(200, 1)),
        MaxPooling1D(pool_size=2),
        Conv1D(64, kernel_size=3, activation='relu'),
        MaxPooling1D(pool_size=2),
        Conv1D(128, kernel_size=3, activation='relu'),
        tf.keras.layers.GlobalAveragePooling1D(),
        <PERSON>se(50, activation='relu'),
        Dense(3, activation='softmax')  # 3 classes: oil, gas, water
    ])
    return model

# 2D Convolution for seismic sections
def create_2d_cnn_for_seismic():
    """2D CNN for seismic section analysis"""
    model = Sequential([
        Conv2D(32, (3, 3), activation='relu', input_shape=(100, 100, 1)),
        MaxPooling2D((2, 2)),
        Conv2D(64, (3, 3), activation='relu'),
        MaxPooling2D((2, 2)),
        Conv2D(128, (3, 3), activation='relu'),
        tf.keras.layers.GlobalAveragePooling2D(),
        Dense(128, activation='relu'),
        Dense(4, activation='softmax')  # 4 geological features
    ])
    return model

# Demonstrate convolution operation
def demonstrate_convolution():
    # Create a simple seismic trace with features
    trace = np.zeros(100)
    trace[20:25] = 1.0    # Strong reflection
    trace[50:53] = -0.8   # Negative reflection
    trace[80:82] = 0.6    # Weak reflection
    
    # Add noise
    trace += np.random.normal(0, 0.1, 100)
    
    # Define different filters
    filters = {
        'Edge detector': np.array([-1, 0, 1]),
        'Smoothing': np.array([0.2, 0.6, 0.2]),
        'Peak detector': np.array([-0.5, 1, -0.5])
    }
    
    plt.figure(figsize=(15, 4))
    
    # Original trace
    plt.subplot(1, 4, 1)
    plt.plot(trace)
    plt.title('Original Seismic Trace')
    plt.ylabel('Amplitude')
    
    # Apply filters
    for i, (name, filt) in enumerate(filters.items()):
        filtered = np.convolve(trace, filt, mode='same')
        plt.subplot(1, 4, i+2)
        plt.plot(filtered)
        plt.title(f'{name} Filter')
        if i == 0:
            plt.ylabel('Filtered Amplitude')
    
    plt.tight_layout()
    plt.show()

demonstrate_convolution()