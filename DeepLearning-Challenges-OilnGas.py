import numpy as np
import matplotlib.pyplot as plt

def ricker_wavelet(points, a):
    """Generate a Ricker wavelet (Mexican hat wavelet)"""
    t = np.linspace(-points//2, points//2, points)
    A = 2 / (np.sqrt(3*a) * (np.pi**0.25))
    wsq = a**2
    vec = A * (1 - (t**2)/wsq) * np.exp(-(t**2)/(2*wsq))
    return vec

def generate_seismic_section(nx=200, nz=100):
    """Generate synthetic seismic section"""
    # Create layered earth model
    velocity = np.ones((nz, nx)) * 2000  # m/s

    # Add geological layers
    velocity[20:40, :] += 500   # Faster layer (limestone)
    velocity[60:80, :] += 1000  # Much faster layer (salt)

    # Add some lateral variation (fault)
    velocity[:, 100:] += 200

    # Convert to reflection coefficients
    rc = np.diff(velocity, axis=0) / (velocity[:-1] + velocity[1:])

    # Create time axis
    dt = 0.001  # 1ms sampling
    t = np.arange(0, 0.2, dt)  # 200ms total time

    # Ricker wavelet (seismic source)
    wavelet = ricker_wavelet(len(t), 4.0)

    # Convolve each trace with wavelet
    seismic = np.zeros((len(t), nx))
    for i in range(nx):
        rc_trace = np.zeros(len(t))
        rc_trace[:rc.shape[0]] = rc[:, i]
        seismic[:, i] = np.convolve(rc_trace, wavelet, mode='same')

    return seismic, t

# Generate seismic data for the challenges
seismic_data, time_axis = generate_seismic_section()

# Challenge 1: Limited labeled data
class DataAugmentation:
    """Data augmentation for seismic traces"""
    
    @staticmethod
    def add_noise(trace, noise_level=0.1):
        noise = np.random.normal(0, noise_level, len(trace))
        return trace + noise
    
    @staticmethod
    def time_shift(trace, max_shift=5):
        shift = np.random.randint(-max_shift, max_shift)
        return np.roll(trace, shift)
    
    @staticmethod
    def amplitude_scaling(trace, scale_range=(0.8, 1.2)):
        scale = np.random.uniform(*scale_range)
        return trace * scale

# Example of data augmentation
original_trace = seismic_data[:, 50]
augmented_traces = []

for i in range(5):
    aug_trace = DataAugmentation.add_noise(original_trace)
    aug_trace = DataAugmentation.time_shift(aug_trace)
    aug_trace = DataAugmentation.amplitude_scaling(aug_trace)
    augmented_traces.append(aug_trace)

plt.figure(figsize=(12, 4))
plt.plot(time_axis, original_trace, 'k-', linewidth=2, label='Original')
for i, trace in enumerate(augmented_traces):
    plt.plot(time_axis, trace, alpha=0.7, label=f'Augmented {i+1}')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
plt.title('Data Augmentation for Seismic Traces')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()