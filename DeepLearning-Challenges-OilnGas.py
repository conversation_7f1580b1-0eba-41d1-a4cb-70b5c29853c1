# Challenge 1: Limited labeled data
class DataAugmentation:
    """Data augmentation for seismic traces"""
    
    @staticmethod
    def add_noise(trace, noise_level=0.1):
        noise = np.random.normal(0, noise_level, len(trace))
        return trace + noise
    
    @staticmethod
    def time_shift(trace, max_shift=5):
        shift = np.random.randint(-max_shift, max_shift)
        return np.roll(trace, shift)
    
    @staticmethod
    def amplitude_scaling(trace, scale_range=(0.8, 1.2)):
        scale = np.random.uniform(*scale_range)
        return trace * scale

# Example of data augmentation
original_trace = seismic_data[:, 50]
augmented_traces = []

for i in range(5):
    aug_trace = DataAugmentation.add_noise(original_trace)
    aug_trace = DataAugmentation.time_shift(aug_trace)
    aug_trace = DataAugmentation.amplitude_scaling(aug_trace)
    augmented_traces.append(aug_trace)

plt.figure(figsize=(12, 4))
plt.plot(time_axis, original_trace, 'k-', linewidth=2, label='Original')
for i, trace in enumerate(augmented_traces):
    plt.plot(time_axis, trace, alpha=0.7, label=f'Augmented {i+1}')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
plt.title('Data Augmentation for Seismic Traces')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()