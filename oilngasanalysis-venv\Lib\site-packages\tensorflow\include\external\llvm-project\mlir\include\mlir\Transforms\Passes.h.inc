/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CSE
#define GEN_PASS_DECL_CANONICALIZER
#define GEN_PASS_DECL_COMPOSITEFIXEDPOINTPASS
#define GEN_PASS_DECL_CONTROLFLOWSINK
#define GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#define GEN_PASS_DECL_INLINER
#define GEN_PASS_DECL_LOCATIONSNAPSHOT
#define GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#define GEN_PASS_DECL_LOOPINVARIANTSUBSETHOISTING
#define GEN_PASS_DECL_MEM2REG
#define GEN_PASS_DECL_PRINTIRPASS
#define GEN_PASS_DECL_PRINTOPSTATS
#define GEN_PASS_DECL_REMOVEDEADVALUES
#define GEN_PASS_DECL_SCCP
#define GEN_PASS_DECL_SROA
#define GEN_PA<PERSON>_DECL_STRIPDEBUGINFO
#define GEN_PASS_DECL_SYMBOLDCE
#define GEN_PASS_DECL_SYMBOLPRIVATIZE
#define GEN_PASS_DECL_TOPOLOGICALSORT
#define GEN_PASS_DECL_VIEWOPGRAPH
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// CSE
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CSE
#undef GEN_PASS_DECL_CSE
#endif // GEN_PASS_DECL_CSE
#ifdef GEN_PASS_DEF_CSE
namespace impl {

template <typename DerivedT>
class CSEBase : public ::mlir::OperationPass<> {
public:
  using Base = CSEBase;

  CSEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CSEBase(const CSEBase &other) : ::mlir::OperationPass<>(other) {}
  CSEBase& operator=(const CSEBase &) = delete;
  CSEBase(CSEBase &&) = delete;
  CSEBase& operator=(CSEBase &&) = delete;
  ~CSEBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cse");
  }
  ::llvm::StringRef getArgument() const override { return "cse"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate common sub-expressions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSE");
  }
  ::llvm::StringRef getName() const override { return "CSE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CSEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numCSE{this, "num-cse'd", "Number of operations CSE'd"};
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of operations DCE'd"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CSE
#endif // GEN_PASS_DEF_CSE

//===----------------------------------------------------------------------===//
// Canonicalizer
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CANONICALIZER
struct CanonicalizerOptions {
  bool topDownProcessingEnabled = true;
  mlir::GreedySimplifyRegionLevel enableRegionSimplification = mlir::GreedySimplifyRegionLevel::Normal;
  int64_t maxIterations = 10;
  int64_t maxNumRewrites = -1;
  bool testConvergence = false;
  ::llvm::SmallVector<std::string> disabledPatterns;
  ::llvm::SmallVector<std::string> enabledPatterns;
};
#undef GEN_PASS_DECL_CANONICALIZER
#endif // GEN_PASS_DECL_CANONICALIZER
#ifdef GEN_PASS_DEF_CANONICALIZER
namespace impl {

template <typename DerivedT>
class CanonicalizerBase : public ::mlir::OperationPass<> {
public:
  using Base = CanonicalizerBase;

  CanonicalizerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CanonicalizerBase(const CanonicalizerBase &other) : ::mlir::OperationPass<>(other) {}
  CanonicalizerBase& operator=(const CanonicalizerBase &) = delete;
  CanonicalizerBase(CanonicalizerBase &&) = delete;
  CanonicalizerBase& operator=(CanonicalizerBase &&) = delete;
  ~CanonicalizerBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("canonicalize");
  }
  ::llvm::StringRef getArgument() const override { return "canonicalize"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Canonicalizer");
  }
  ::llvm::StringRef getName() const override { return "Canonicalizer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<ub::UBDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CanonicalizerBase<DerivedT>)

  CanonicalizerBase(CanonicalizerOptions options) : CanonicalizerBase() {
    topDownProcessingEnabled = std::move(options.topDownProcessingEnabled);
    enableRegionSimplification = std::move(options.enableRegionSimplification);
    maxIterations = std::move(options.maxIterations);
    maxNumRewrites = std::move(options.maxNumRewrites);
    testConvergence = std::move(options.testConvergence);
    disabledPatterns = std::move(options.disabledPatterns);
    enabledPatterns = std::move(options.enabledPatterns);
  }
protected:
  ::mlir::Pass::Option<bool> topDownProcessingEnabled{*this, "top-down", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<mlir::GreedySimplifyRegionLevel> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Perform control flow optimizations to the region tree"), ::llvm::cl::init(mlir::GreedySimplifyRegionLevel::Normal), ::llvm::cl::values(
               clEnumValN(mlir::GreedySimplifyRegionLevel::Disabled, "disabled",
                "Don't run any control-flow simplification."),
               clEnumValN(mlir::GreedySimplifyRegionLevel::Normal, "normal",
                "Perform simple control-flow simplifications (e.g. dead args elimination)."),
               clEnumValN(mlir::GreedySimplifyRegionLevel::Aggressive, "aggressive",
                "Perform aggressive control-flow simplification (e.g. block merging).")
              )};
  ::mlir::Pass::Option<int64_t> maxIterations{*this, "max-iterations", ::llvm::cl::desc("Max. iterations between applying patterns / simplifying regions"), ::llvm::cl::init(10)};
  ::mlir::Pass::Option<int64_t> maxNumRewrites{*this, "max-num-rewrites", ::llvm::cl::desc("Max. number of pattern rewrites within an iteration"), ::llvm::cl::init(-1)};
  ::mlir::Pass::Option<bool> testConvergence{*this, "test-convergence", ::llvm::cl::desc("Test only: Fail pass on non-convergence to detect cyclic pattern"), ::llvm::cl::init(false)};
  ::mlir::Pass::ListOption<std::string> disabledPatterns{*this, "disable-patterns", ::llvm::cl::desc("Labels of patterns that should be filtered out during application")};
  ::mlir::Pass::ListOption<std::string> enabledPatterns{*this, "enable-patterns", ::llvm::cl::desc("Labels of patterns that should be used during application, all other patterns are filtered out")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CANONICALIZER
#endif // GEN_PASS_DEF_CANONICALIZER

//===----------------------------------------------------------------------===//
// CompositeFixedPointPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_COMPOSITEFIXEDPOINTPASS
struct CompositeFixedPointPassOptions {
  std::string name = "CompositeFixedPointPass";
  std::string pipelineStr;
  int maxIter = 10;
};
std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass();
std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass(CompositeFixedPointPassOptions options);
#undef GEN_PASS_DECL_COMPOSITEFIXEDPOINTPASS
#endif // GEN_PASS_DECL_COMPOSITEFIXEDPOINTPASS
#ifdef GEN_PASS_DEF_COMPOSITEFIXEDPOINTPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass(CompositeFixedPointPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class CompositeFixedPointPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CompositeFixedPointPassBase;

  CompositeFixedPointPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CompositeFixedPointPassBase(const CompositeFixedPointPassBase &other) : ::mlir::OperationPass<>(other) {}
  CompositeFixedPointPassBase& operator=(const CompositeFixedPointPassBase &) = delete;
  CompositeFixedPointPassBase(CompositeFixedPointPassBase &&) = delete;
  CompositeFixedPointPassBase& operator=(CompositeFixedPointPassBase &&) = delete;
  ~CompositeFixedPointPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("composite-fixed-point-pass");
  }
  ::llvm::StringRef getArgument() const override { return "composite-fixed-point-pass"; }

  ::llvm::StringRef getDescription() const override { return "Composite fixed point pass"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CompositeFixedPointPass");
  }
  ::llvm::StringRef getName() const override { return "CompositeFixedPointPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CompositeFixedPointPassBase<DerivedT>)

  CompositeFixedPointPassBase(CompositeFixedPointPassOptions options) : CompositeFixedPointPassBase() {
    name = std::move(options.name);
    pipelineStr = std::move(options.pipelineStr);
    maxIter = std::move(options.maxIter);
  }
protected:
  ::mlir::Pass::Option<std::string> name{*this, "name", ::llvm::cl::desc("Composite pass display name"), ::llvm::cl::init("CompositeFixedPointPass")};
  ::mlir::Pass::Option<std::string> pipelineStr{*this, "pipeline", ::llvm::cl::desc("Composite pass inner pipeline")};
  ::mlir::Pass::Option<int> maxIter{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations if inner pipeline"), ::llvm::cl::init(10)};
private:

  friend std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass(CompositeFixedPointPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass() {
  return impl::createCompositeFixedPointPass();
}

std::unique_ptr<::mlir::Pass> createCompositeFixedPointPass(CompositeFixedPointPassOptions options) {
  return impl::createCompositeFixedPointPass(std::move(options));
}
#undef GEN_PASS_DEF_COMPOSITEFIXEDPOINTPASS
#endif // GEN_PASS_DEF_COMPOSITEFIXEDPOINTPASS

//===----------------------------------------------------------------------===//
// ControlFlowSink
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONTROLFLOWSINK
#undef GEN_PASS_DECL_CONTROLFLOWSINK
#endif // GEN_PASS_DECL_CONTROLFLOWSINK
#ifdef GEN_PASS_DEF_CONTROLFLOWSINK
namespace impl {

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<>(other) {}
  ControlFlowSinkBase& operator=(const ControlFlowSinkBase &) = delete;
  ControlFlowSinkBase(ControlFlowSinkBase &&) = delete;
  ControlFlowSinkBase& operator=(ControlFlowSinkBase &&) = delete;
  ~ControlFlowSinkBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("control-flow-sink");
  }
  ::llvm::StringRef getArgument() const override { return "control-flow-sink"; }

  ::llvm::StringRef getDescription() const override { return "Sink operations into conditional blocks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ControlFlowSinkBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numSunk{this, "num-sunk", "Number of operations sunk"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONTROLFLOWSINK
#endif // GEN_PASS_DEF_CONTROLFLOWSINK

//===----------------------------------------------------------------------===//
// GenerateRuntimeVerification
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#undef GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#endif // GEN_PASS_DECL_GENERATERUNTIMEVERIFICATION
#ifdef GEN_PASS_DEF_GENERATERUNTIMEVERIFICATION
namespace impl {

template <typename DerivedT>
class GenerateRuntimeVerificationBase : public ::mlir::OperationPass<> {
public:
  using Base = GenerateRuntimeVerificationBase;

  GenerateRuntimeVerificationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  GenerateRuntimeVerificationBase(const GenerateRuntimeVerificationBase &other) : ::mlir::OperationPass<>(other) {}
  GenerateRuntimeVerificationBase& operator=(const GenerateRuntimeVerificationBase &) = delete;
  GenerateRuntimeVerificationBase(GenerateRuntimeVerificationBase &&) = delete;
  GenerateRuntimeVerificationBase& operator=(GenerateRuntimeVerificationBase &&) = delete;
  ~GenerateRuntimeVerificationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("generate-runtime-verification");
  }
  ::llvm::StringRef getArgument() const override { return "generate-runtime-verification"; }

  ::llvm::StringRef getDescription() const override { return "Generate additional runtime op verification checks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GenerateRuntimeVerification");
  }
  ::llvm::StringRef getName() const override { return "GenerateRuntimeVerification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GenerateRuntimeVerificationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GENERATERUNTIMEVERIFICATION
#endif // GEN_PASS_DEF_GENERATERUNTIMEVERIFICATION

//===----------------------------------------------------------------------===//
// Inliner
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INLINER
struct InlinerOptions {
  std::string defaultPipelineStr = "canonicalize";
  ::llvm::SmallVector<OpPassManager> opPipelineList;
  unsigned maxInliningIterations = 4;
  unsigned inliningThreshold = -1U;
};
#undef GEN_PASS_DECL_INLINER
#endif // GEN_PASS_DECL_INLINER
#ifdef GEN_PASS_DEF_INLINER
namespace impl {

template <typename DerivedT>
class InlinerBase : public ::mlir::OperationPass<> {
public:
  using Base = InlinerBase;

  InlinerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InlinerBase(const InlinerBase &other) : ::mlir::OperationPass<>(other) {}
  InlinerBase& operator=(const InlinerBase &) = delete;
  InlinerBase(InlinerBase &&) = delete;
  InlinerBase& operator=(InlinerBase &&) = delete;
  ~InlinerBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("inline");
  }
  ::llvm::StringRef getArgument() const override { return "inline"; }

  ::llvm::StringRef getDescription() const override { return "Inline function calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Inliner");
  }
  ::llvm::StringRef getName() const override { return "Inliner"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InlinerBase<DerivedT>)

  InlinerBase(InlinerOptions options) : InlinerBase() {
    defaultPipelineStr = std::move(options.defaultPipelineStr);
    opPipelineList = std::move(options.opPipelineList);
    maxInliningIterations = std::move(options.maxInliningIterations);
    inliningThreshold = std::move(options.inliningThreshold);
  }
protected:
  ::mlir::Pass::Option<std::string> defaultPipelineStr{*this, "default-pipeline", ::llvm::cl::desc("The optimizer pipeline used for callables that do not have a dedicated optimizer pipeline in opPipelineList"), ::llvm::cl::init("canonicalize")};
  ::mlir::Pass::ListOption<OpPassManager> opPipelineList{*this, "op-pipelines", ::llvm::cl::desc("Callable operation specific optimizer pipelines (in the form of `dialect.op(pipeline)`)")};
  ::mlir::Pass::Option<unsigned> maxInliningIterations{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations when inlining within an SCC"), ::llvm::cl::init(4)};
  ::mlir::Pass::Option<unsigned> inliningThreshold{*this, "inlining-threshold", ::llvm::cl::desc("If the ratio between the number of the operations in the callee and the number of the operations in the caller exceeds this value (in percentage), then the callee is not inlined even if it is legal to inline it"), ::llvm::cl::init(-1U)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_INLINER
#endif // GEN_PASS_DEF_INLINER

//===----------------------------------------------------------------------===//
// LocationSnapshot
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOCATIONSNAPSHOT
struct LocationSnapshotOptions {
  std::string fileName;
  std::string tag;
  bool enableDebugInfo = false;
  bool printGenericOpForm = false;
  bool useLocalScope = false;
  bool printPrettyDebugInfo = false;
};
std::unique_ptr<::mlir::Pass> createLocationSnapshot();
std::unique_ptr<::mlir::Pass> createLocationSnapshot(LocationSnapshotOptions options);
#undef GEN_PASS_DECL_LOCATIONSNAPSHOT
#endif // GEN_PASS_DECL_LOCATIONSNAPSHOT
#ifdef GEN_PASS_DEF_LOCATIONSNAPSHOT

namespace impl {
  std::unique_ptr<::mlir::Pass> createLocationSnapshot();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createLocationSnapshot(LocationSnapshotOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class LocationSnapshotBase : public ::mlir::OperationPass<> {
public:
  using Base = LocationSnapshotBase;

  LocationSnapshotBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LocationSnapshotBase(const LocationSnapshotBase &other) : ::mlir::OperationPass<>(other) {}
  LocationSnapshotBase& operator=(const LocationSnapshotBase &) = delete;
  LocationSnapshotBase(LocationSnapshotBase &&) = delete;
  LocationSnapshotBase& operator=(LocationSnapshotBase &&) = delete;
  ~LocationSnapshotBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("snapshot-op-locations");
  }
  ::llvm::StringRef getArgument() const override { return "snapshot-op-locations"; }

  ::llvm::StringRef getDescription() const override { return "Generate new locations from the current IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LocationSnapshot");
  }
  ::llvm::StringRef getName() const override { return "LocationSnapshot"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LocationSnapshotBase<DerivedT>)

  LocationSnapshotBase(LocationSnapshotOptions options) : LocationSnapshotBase() {
    fileName = std::move(options.fileName);
    tag = std::move(options.tag);
    enableDebugInfo = std::move(options.enableDebugInfo);
    printGenericOpForm = std::move(options.printGenericOpForm);
    useLocalScope = std::move(options.useLocalScope);
    printPrettyDebugInfo = std::move(options.printPrettyDebugInfo);
  }
protected:
  ::mlir::Pass::Option<std::string> fileName{*this, "filename", ::llvm::cl::desc("The filename to print the generated IR")};
  ::mlir::Pass::Option<std::string> tag{*this, "tag", ::llvm::cl::desc("A tag to use when fusing the new locations with the original. If unset, the locations are replaced.")};
  ::mlir::Pass::Option<bool> enableDebugInfo{*this, "print-debuginfo", ::llvm::cl::desc("Print debug info in MLIR output"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printGenericOpForm{*this, "print-op-generic", ::llvm::cl::desc("Print the generic op form"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useLocalScope{*this, "print-local-scope", ::llvm::cl::desc("Print with local scope and inline information (eliding aliases for attributes, types, and locations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printPrettyDebugInfo{*this, "pretty-debuginfo", ::llvm::cl::desc("Print pretty debug info in MLIR output"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createLocationSnapshot() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createLocationSnapshot(LocationSnapshotOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLocationSnapshot() {
  return impl::createLocationSnapshot();
}

std::unique_ptr<::mlir::Pass> createLocationSnapshot(LocationSnapshotOptions options) {
  return impl::createLocationSnapshot(std::move(options));
}
#undef GEN_PASS_DEF_LOCATIONSNAPSHOT
#endif // GEN_PASS_DEF_LOCATIONSNAPSHOT

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#undef GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#endif // GEN_PASS_DECL_LOOPINVARIANTCODEMOTION
#ifdef GEN_PASS_DEF_LOOPINVARIANTCODEMOTION
namespace impl {

template <typename DerivedT>
class LoopInvariantCodeMotionBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantCodeMotionBase;

  LoopInvariantCodeMotionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantCodeMotionBase(const LoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<>(other) {}
  LoopInvariantCodeMotionBase& operator=(const LoopInvariantCodeMotionBase &) = delete;
  LoopInvariantCodeMotionBase(LoopInvariantCodeMotionBase &&) = delete;
  LoopInvariantCodeMotionBase& operator=(LoopInvariantCodeMotionBase &&) = delete;
  ~LoopInvariantCodeMotionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopInvariantCodeMotionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOOPINVARIANTCODEMOTION
#endif // GEN_PASS_DEF_LOOPINVARIANTCODEMOTION

//===----------------------------------------------------------------------===//
// LoopInvariantSubsetHoisting
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOOPINVARIANTSUBSETHOISTING
#undef GEN_PASS_DECL_LOOPINVARIANTSUBSETHOISTING
#endif // GEN_PASS_DECL_LOOPINVARIANTSUBSETHOISTING
#ifdef GEN_PASS_DEF_LOOPINVARIANTSUBSETHOISTING
namespace impl {

template <typename DerivedT>
class LoopInvariantSubsetHoistingBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantSubsetHoistingBase;

  LoopInvariantSubsetHoistingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantSubsetHoistingBase(const LoopInvariantSubsetHoistingBase &other) : ::mlir::OperationPass<>(other) {}
  LoopInvariantSubsetHoistingBase& operator=(const LoopInvariantSubsetHoistingBase &) = delete;
  LoopInvariantSubsetHoistingBase(LoopInvariantSubsetHoistingBase &&) = delete;
  LoopInvariantSubsetHoistingBase& operator=(LoopInvariantSubsetHoistingBase &&) = delete;
  ~LoopInvariantSubsetHoistingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-subset-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-subset-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant subset ops outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantSubsetHoisting");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantSubsetHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopInvariantSubsetHoistingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOOPINVARIANTSUBSETHOISTING
#endif // GEN_PASS_DEF_LOOPINVARIANTSUBSETHOISTING

//===----------------------------------------------------------------------===//
// Mem2Reg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MEM2REG
struct Mem2RegOptions {
  bool enableRegionSimplification = true;
};
std::unique_ptr<::mlir::Pass> createMem2Reg();
std::unique_ptr<::mlir::Pass> createMem2Reg(Mem2RegOptions options);
#undef GEN_PASS_DECL_MEM2REG
#endif // GEN_PASS_DECL_MEM2REG
#ifdef GEN_PASS_DEF_MEM2REG

namespace impl {
  std::unique_ptr<::mlir::Pass> createMem2Reg();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createMem2Reg(Mem2RegOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class Mem2RegBase : public ::mlir::OperationPass<> {
public:
  using Base = Mem2RegBase;

  Mem2RegBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  Mem2RegBase(const Mem2RegBase &other) : ::mlir::OperationPass<>(other) {}
  Mem2RegBase& operator=(const Mem2RegBase &) = delete;
  Mem2RegBase(Mem2RegBase &&) = delete;
  Mem2RegBase& operator=(Mem2RegBase &&) = delete;
  ~Mem2RegBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mem2reg");
  }
  ::llvm::StringRef getArgument() const override { return "mem2reg"; }

  ::llvm::StringRef getDescription() const override { return "Promotes memory slots into values."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Mem2Reg");
  }
  ::llvm::StringRef getName() const override { return "Mem2Reg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(Mem2RegBase<DerivedT>)

  Mem2RegBase(Mem2RegOptions options) : Mem2RegBase() {
    enableRegionSimplification = std::move(options.enableRegionSimplification);
  }
protected:
  ::mlir::Pass::Option<bool> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Perform control flow optimizations to the region tree"), ::llvm::cl::init(true)};
  ::mlir::Pass::Statistic promotedAmount{this, "promoted slots", "Total amount of memory slot promoted"};
  ::mlir::Pass::Statistic newBlockArgumentAmount{this, "new block args", "Total amount of new block argument inserted in blocks"};
private:

  friend std::unique_ptr<::mlir::Pass> createMem2Reg() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createMem2Reg(Mem2RegOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createMem2Reg() {
  return impl::createMem2Reg();
}

std::unique_ptr<::mlir::Pass> createMem2Reg(Mem2RegOptions options) {
  return impl::createMem2Reg(std::move(options));
}
#undef GEN_PASS_DEF_MEM2REG
#endif // GEN_PASS_DEF_MEM2REG

//===----------------------------------------------------------------------===//
// PrintIRPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PRINTIRPASS
struct PrintIRPassOptions {
  std::string label;
};
#undef GEN_PASS_DECL_PRINTIRPASS
#endif // GEN_PASS_DECL_PRINTIRPASS
#ifdef GEN_PASS_DEF_PRINTIRPASS
namespace impl {

template <typename DerivedT>
class PrintIRPassBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintIRPassBase;

  PrintIRPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintIRPassBase(const PrintIRPassBase &other) : ::mlir::OperationPass<>(other) {}
  PrintIRPassBase& operator=(const PrintIRPassBase &) = delete;
  PrintIRPassBase(PrintIRPassBase &&) = delete;
  PrintIRPassBase& operator=(PrintIRPassBase &&) = delete;
  ~PrintIRPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-ir");
  }
  ::llvm::StringRef getArgument() const override { return "print-ir"; }

  ::llvm::StringRef getDescription() const override { return "Print IR on the debug stream"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintIRPass");
  }
  ::llvm::StringRef getName() const override { return "PrintIRPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintIRPassBase<DerivedT>)

  PrintIRPassBase(PrintIRPassOptions options) : PrintIRPassBase() {
    label = std::move(options.label);
  }
protected:
  ::mlir::Pass::Option<std::string> label{*this, "label", ::llvm::cl::desc("Label")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PRINTIRPASS
#endif // GEN_PASS_DEF_PRINTIRPASS

//===----------------------------------------------------------------------===//
// PrintOpStats
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PRINTOPSTATS
struct PrintOpStatsOptions {
  bool printAsJSON = false;
};
#undef GEN_PASS_DECL_PRINTOPSTATS
#endif // GEN_PASS_DECL_PRINTOPSTATS
#ifdef GEN_PASS_DEF_PRINTOPSTATS
namespace impl {

template <typename DerivedT>
class PrintOpStatsBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintOpStatsBase;

  PrintOpStatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintOpStatsBase(const PrintOpStatsBase &other) : ::mlir::OperationPass<>(other) {}
  PrintOpStatsBase& operator=(const PrintOpStatsBase &) = delete;
  PrintOpStatsBase(PrintOpStatsBase &&) = delete;
  PrintOpStatsBase& operator=(PrintOpStatsBase &&) = delete;
  ~PrintOpStatsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-op-stats");
  }
  ::llvm::StringRef getArgument() const override { return "print-op-stats"; }

  ::llvm::StringRef getDescription() const override { return "Print statistics of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintOpStats");
  }
  ::llvm::StringRef getName() const override { return "PrintOpStats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintOpStatsBase<DerivedT>)

  PrintOpStatsBase(PrintOpStatsOptions options) : PrintOpStatsBase() {
    printAsJSON = std::move(options.printAsJSON);
  }
protected:
  ::mlir::Pass::Option<bool> printAsJSON{*this, "json", ::llvm::cl::desc("print the stats as JSON"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PRINTOPSTATS
#endif // GEN_PASS_DEF_PRINTOPSTATS

//===----------------------------------------------------------------------===//
// RemoveDeadValues
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_REMOVEDEADVALUES
#undef GEN_PASS_DECL_REMOVEDEADVALUES
#endif // GEN_PASS_DECL_REMOVEDEADVALUES
#ifdef GEN_PASS_DEF_REMOVEDEADVALUES
namespace impl {

template <typename DerivedT>
class RemoveDeadValuesBase : public ::mlir::OperationPass<> {
public:
  using Base = RemoveDeadValuesBase;

  RemoveDeadValuesBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveDeadValuesBase(const RemoveDeadValuesBase &other) : ::mlir::OperationPass<>(other) {}
  RemoveDeadValuesBase& operator=(const RemoveDeadValuesBase &) = delete;
  RemoveDeadValuesBase(RemoveDeadValuesBase &&) = delete;
  RemoveDeadValuesBase& operator=(RemoveDeadValuesBase &&) = delete;
  ~RemoveDeadValuesBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("remove-dead-values");
  }
  ::llvm::StringRef getArgument() const override { return "remove-dead-values"; }

  ::llvm::StringRef getDescription() const override { return "Remove dead values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveDeadValues");
  }
  ::llvm::StringRef getName() const override { return "RemoveDeadValues"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveDeadValuesBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_REMOVEDEADVALUES
#endif // GEN_PASS_DEF_REMOVEDEADVALUES

//===----------------------------------------------------------------------===//
// SCCP
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCCP
#undef GEN_PASS_DECL_SCCP
#endif // GEN_PASS_DECL_SCCP
#ifdef GEN_PASS_DEF_SCCP
namespace impl {

template <typename DerivedT>
class SCCPBase : public ::mlir::OperationPass<> {
public:
  using Base = SCCPBase;

  SCCPBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCCPBase(const SCCPBase &other) : ::mlir::OperationPass<>(other) {}
  SCCPBase& operator=(const SCCPBase &) = delete;
  SCCPBase(SCCPBase &&) = delete;
  SCCPBase& operator=(SCCPBase &&) = delete;
  ~SCCPBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sccp");
  }
  ::llvm::StringRef getArgument() const override { return "sccp"; }

  ::llvm::StringRef getDescription() const override { return "Sparse Conditional Constant Propagation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCCP");
  }
  ::llvm::StringRef getName() const override { return "SCCP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCCPBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCCP
#endif // GEN_PASS_DEF_SCCP

//===----------------------------------------------------------------------===//
// SROA
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SROA
std::unique_ptr<::mlir::Pass> createSROA();
#undef GEN_PASS_DECL_SROA
#endif // GEN_PASS_DECL_SROA
#ifdef GEN_PASS_DEF_SROA

namespace impl {
  std::unique_ptr<::mlir::Pass> createSROA();
} // namespace impl
namespace impl {

template <typename DerivedT>
class SROABase : public ::mlir::OperationPass<> {
public:
  using Base = SROABase;

  SROABase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SROABase(const SROABase &other) : ::mlir::OperationPass<>(other) {}
  SROABase& operator=(const SROABase &) = delete;
  SROABase(SROABase &&) = delete;
  SROABase& operator=(SROABase &&) = delete;
  ~SROABase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sroa");
  }
  ::llvm::StringRef getArgument() const override { return "sroa"; }

  ::llvm::StringRef getDescription() const override { return "Scalar Replacement of Aggregates"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SROA");
  }
  ::llvm::StringRef getName() const override { return "SROA"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SROABase<DerivedT>)

protected:
  ::mlir::Pass::Statistic destructuredAmount{this, "destructured slots", "Total amount of memory slots destructured"};
  ::mlir::Pass::Statistic slotsWithMemoryBenefit{this, "slots with memory benefit", "Total amount of memory slots in which the destructured size was smaller than the total size after eliminating unused fields"};
  ::mlir::Pass::Statistic maxSubelementAmount{this, "max subelement number", "Maximal number of sub-elements a successfully destructured slot initially had"};
private:

  friend std::unique_ptr<::mlir::Pass> createSROA() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createSROA() {
  return impl::createSROA();
}
#undef GEN_PASS_DEF_SROA
#endif // GEN_PASS_DEF_SROA

//===----------------------------------------------------------------------===//
// StripDebugInfo
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STRIPDEBUGINFO
#undef GEN_PASS_DECL_STRIPDEBUGINFO
#endif // GEN_PASS_DECL_STRIPDEBUGINFO
#ifdef GEN_PASS_DEF_STRIPDEBUGINFO
namespace impl {

template <typename DerivedT>
class StripDebugInfoBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDebugInfoBase;

  StripDebugInfoBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDebugInfoBase(const StripDebugInfoBase &other) : ::mlir::OperationPass<>(other) {}
  StripDebugInfoBase& operator=(const StripDebugInfoBase &) = delete;
  StripDebugInfoBase(StripDebugInfoBase &&) = delete;
  StripDebugInfoBase& operator=(StripDebugInfoBase &&) = delete;
  ~StripDebugInfoBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("strip-debuginfo");
  }
  ::llvm::StringRef getArgument() const override { return "strip-debuginfo"; }

  ::llvm::StringRef getDescription() const override { return "Strip debug info from all operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDebugInfo");
  }
  ::llvm::StringRef getName() const override { return "StripDebugInfo"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripDebugInfoBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STRIPDEBUGINFO
#endif // GEN_PASS_DEF_STRIPDEBUGINFO

//===----------------------------------------------------------------------===//
// SymbolDCE
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SYMBOLDCE
#undef GEN_PASS_DECL_SYMBOLDCE
#endif // GEN_PASS_DECL_SYMBOLDCE
#ifdef GEN_PASS_DEF_SYMBOLDCE
namespace impl {

template <typename DerivedT>
class SymbolDCEBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolDCEBase;

  SymbolDCEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolDCEBase(const SymbolDCEBase &other) : ::mlir::OperationPass<>(other) {}
  SymbolDCEBase& operator=(const SymbolDCEBase &) = delete;
  SymbolDCEBase(SymbolDCEBase &&) = delete;
  SymbolDCEBase& operator=(SymbolDCEBase &&) = delete;
  ~SymbolDCEBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-dce");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-dce"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate dead symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolDCE");
  }
  ::llvm::StringRef getName() const override { return "SymbolDCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolDCEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of symbols DCE'd"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SYMBOLDCE
#endif // GEN_PASS_DEF_SYMBOLDCE

//===----------------------------------------------------------------------===//
// SymbolPrivatize
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SYMBOLPRIVATIZE
struct SymbolPrivatizeOptions {
  ::llvm::SmallVector<std::string> exclude;
};
#undef GEN_PASS_DECL_SYMBOLPRIVATIZE
#endif // GEN_PASS_DECL_SYMBOLPRIVATIZE
#ifdef GEN_PASS_DEF_SYMBOLPRIVATIZE
namespace impl {

template <typename DerivedT>
class SymbolPrivatizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolPrivatizeBase;

  SymbolPrivatizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolPrivatizeBase(const SymbolPrivatizeBase &other) : ::mlir::OperationPass<>(other) {}
  SymbolPrivatizeBase& operator=(const SymbolPrivatizeBase &) = delete;
  SymbolPrivatizeBase(SymbolPrivatizeBase &&) = delete;
  SymbolPrivatizeBase& operator=(SymbolPrivatizeBase &&) = delete;
  ~SymbolPrivatizeBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-privatize");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-privatize"; }

  ::llvm::StringRef getDescription() const override { return "Mark symbols private"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolPrivatize");
  }
  ::llvm::StringRef getName() const override { return "SymbolPrivatize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolPrivatizeBase<DerivedT>)

  SymbolPrivatizeBase(SymbolPrivatizeOptions options) : SymbolPrivatizeBase() {
    exclude = std::move(options.exclude);
  }
protected:
  ::mlir::Pass::ListOption<std::string> exclude{*this, "exclude", ::llvm::cl::desc("Comma separated list of symbols that should not be marked private")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SYMBOLPRIVATIZE
#endif // GEN_PASS_DEF_SYMBOLPRIVATIZE

//===----------------------------------------------------------------------===//
// TopologicalSort
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOPOLOGICALSORT
#undef GEN_PASS_DECL_TOPOLOGICALSORT
#endif // GEN_PASS_DECL_TOPOLOGICALSORT
#ifdef GEN_PASS_DEF_TOPOLOGICALSORT
namespace impl {

template <typename DerivedT>
class TopologicalSortBase : public ::mlir::OperationPass<> {
public:
  using Base = TopologicalSortBase;

  TopologicalSortBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TopologicalSortBase(const TopologicalSortBase &other) : ::mlir::OperationPass<>(other) {}
  TopologicalSortBase& operator=(const TopologicalSortBase &) = delete;
  TopologicalSortBase(TopologicalSortBase &&) = delete;
  TopologicalSortBase& operator=(TopologicalSortBase &&) = delete;
  ~TopologicalSortBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("topological-sort");
  }
  ::llvm::StringRef getArgument() const override { return "topological-sort"; }

  ::llvm::StringRef getDescription() const override { return "Sort regions without SSA dominance in topological order"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TopologicalSort");
  }
  ::llvm::StringRef getName() const override { return "TopologicalSort"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TopologicalSortBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOPOLOGICALSORT
#endif // GEN_PASS_DEF_TOPOLOGICALSORT

//===----------------------------------------------------------------------===//
// ViewOpGraph
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VIEWOPGRAPH
struct ViewOpGraphOptions {
  unsigned maxLabelLen = 20;
  bool printAttrs = true;
  bool printControlFlowEdges = false;
  bool printDataFlowEdges = true;
  bool printResultTypes = true;
};
#undef GEN_PASS_DECL_VIEWOPGRAPH
#endif // GEN_PASS_DECL_VIEWOPGRAPH
#ifdef GEN_PASS_DEF_VIEWOPGRAPH
namespace impl {

template <typename DerivedT>
class ViewOpGraphBase : public ::mlir::OperationPass<> {
public:
  using Base = ViewOpGraphBase;

  ViewOpGraphBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ViewOpGraphBase(const ViewOpGraphBase &other) : ::mlir::OperationPass<>(other) {}
  ViewOpGraphBase& operator=(const ViewOpGraphBase &) = delete;
  ViewOpGraphBase(ViewOpGraphBase &&) = delete;
  ViewOpGraphBase& operator=(ViewOpGraphBase &&) = delete;
  ~ViewOpGraphBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("view-op-graph");
  }
  ::llvm::StringRef getArgument() const override { return "view-op-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print Graphviz visualization of an operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ViewOpGraph");
  }
  ::llvm::StringRef getName() const override { return "ViewOpGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ViewOpGraphBase<DerivedT>)

  ViewOpGraphBase(ViewOpGraphOptions options) : ViewOpGraphBase() {
    maxLabelLen = std::move(options.maxLabelLen);
    printAttrs = std::move(options.printAttrs);
    printControlFlowEdges = std::move(options.printControlFlowEdges);
    printDataFlowEdges = std::move(options.printDataFlowEdges);
    printResultTypes = std::move(options.printResultTypes);
  }
protected:
  ::mlir::Pass::Option<unsigned> maxLabelLen{*this, "max-label-len", ::llvm::cl::desc("Limit attribute/type length to number of chars"), ::llvm::cl::init(20)};
  ::mlir::Pass::Option<bool> printAttrs{*this, "print-attrs", ::llvm::cl::desc("Print attributes of operations"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printControlFlowEdges{*this, "print-control-flow-edges", ::llvm::cl::desc("Print control flow edges"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printDataFlowEdges{*this, "print-data-flow-edges", ::llvm::cl::desc("Print data flow edges"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printResultTypes{*this, "print-result-types", ::llvm::cl::desc("Print result types of operations"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VIEWOPGRAPH
#endif // GEN_PASS_DEF_VIEWOPGRAPH
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// CSE Registration
//===----------------------------------------------------------------------===//

inline void registerCSE() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCSEPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCSEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCSEPass();
  });
}

//===----------------------------------------------------------------------===//
// Canonicalizer Registration
//===----------------------------------------------------------------------===//

inline void registerCanonicalizer() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCanonicalizerPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCanonicalizerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCanonicalizerPass();
  });
}

//===----------------------------------------------------------------------===//
// CompositeFixedPointPass Registration
//===----------------------------------------------------------------------===//

inline void registerCompositeFixedPointPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCompositeFixedPointPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCompositeFixedPointPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCompositeFixedPointPass();
  });
}

//===----------------------------------------------------------------------===//
// ControlFlowSink Registration
//===----------------------------------------------------------------------===//

inline void registerControlFlowSink() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::createControlFlowSinkPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerControlFlowSinkPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::createControlFlowSinkPass();
  });
}

//===----------------------------------------------------------------------===//
// GenerateRuntimeVerification Registration
//===----------------------------------------------------------------------===//

inline void registerGenerateRuntimeVerification() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGenerateRuntimeVerificationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGenerateRuntimeVerificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGenerateRuntimeVerificationPass();
  });
}

//===----------------------------------------------------------------------===//
// Inliner Registration
//===----------------------------------------------------------------------===//

inline void registerInliner() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createInlinerPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInlinerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createInlinerPass();
  });
}

//===----------------------------------------------------------------------===//
// LocationSnapshot Registration
//===----------------------------------------------------------------------===//

inline void registerLocationSnapshot() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLocationSnapshot();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLocationSnapshotPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLocationSnapshot();
  });
}

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion Registration
//===----------------------------------------------------------------------===//

inline void registerLoopInvariantCodeMotion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantCodeMotionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLoopInvariantCodeMotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantCodeMotionPass();
  });
}

//===----------------------------------------------------------------------===//
// LoopInvariantSubsetHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerLoopInvariantSubsetHoisting() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantSubsetHoistingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLoopInvariantSubsetHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantSubsetHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// Mem2Reg Registration
//===----------------------------------------------------------------------===//

inline void registerMem2Reg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMem2Reg();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMem2RegPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMem2Reg();
  });
}

//===----------------------------------------------------------------------===//
// PrintIRPass Registration
//===----------------------------------------------------------------------===//

inline void registerPrintIRPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintIRPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPrintIRPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintIRPass();
  });
}

//===----------------------------------------------------------------------===//
// PrintOpStats Registration
//===----------------------------------------------------------------------===//

inline void registerPrintOpStats() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpStatsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPrintOpStatsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpStatsPass();
  });
}

//===----------------------------------------------------------------------===//
// RemoveDeadValues Registration
//===----------------------------------------------------------------------===//

inline void registerRemoveDeadValues() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createRemoveDeadValuesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerRemoveDeadValuesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createRemoveDeadValuesPass();
  });
}

//===----------------------------------------------------------------------===//
// SCCP Registration
//===----------------------------------------------------------------------===//

inline void registerSCCP() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCCPPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCCPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCCPPass();
  });
}

//===----------------------------------------------------------------------===//
// SROA Registration
//===----------------------------------------------------------------------===//

inline void registerSROA() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSROA();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSROAPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSROA();
  });
}

//===----------------------------------------------------------------------===//
// StripDebugInfo Registration
//===----------------------------------------------------------------------===//

inline void registerStripDebugInfo() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStripDebugInfoPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStripDebugInfoPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStripDebugInfoPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolDCE Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolDCE() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolDCEPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSymbolDCEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolDCEPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolPrivatize Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolPrivatize() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolPrivatizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSymbolPrivatizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolPrivatizePass();
  });
}

//===----------------------------------------------------------------------===//
// TopologicalSort Registration
//===----------------------------------------------------------------------===//

inline void registerTopologicalSort() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTopologicalSortPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTopologicalSortPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTopologicalSortPass();
  });
}

//===----------------------------------------------------------------------===//
// ViewOpGraph Registration
//===----------------------------------------------------------------------===//

inline void registerViewOpGraph() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpGraphPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerViewOpGraphPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// Transforms Registration
//===----------------------------------------------------------------------===//

inline void registerTransformsPasses() {
  registerCSE();
  registerCanonicalizer();
  registerCompositeFixedPointPass();
  registerControlFlowSink();
  registerGenerateRuntimeVerification();
  registerInliner();
  registerLocationSnapshot();
  registerLoopInvariantCodeMotion();
  registerLoopInvariantSubsetHoisting();
  registerMem2Reg();
  registerPrintIRPass();
  registerPrintOpStats();
  registerRemoveDeadValues();
  registerSCCP();
  registerSROA();
  registerStripDebugInfo();
  registerSymbolDCE();
  registerSymbolPrivatize();
  registerTopologicalSort();
  registerViewOpGraph();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class CSEBase : public ::mlir::OperationPass<> {
public:
  using Base = CSEBase;

  CSEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CSEBase(const CSEBase &other) : ::mlir::OperationPass<>(other) {}
  CSEBase& operator=(const CSEBase &) = delete;
  CSEBase(CSEBase &&) = delete;
  CSEBase& operator=(CSEBase &&) = delete;
  ~CSEBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cse");
  }
  ::llvm::StringRef getArgument() const override { return "cse"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate common sub-expressions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSE");
  }
  ::llvm::StringRef getName() const override { return "CSE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CSEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numCSE{this, "num-cse'd", "Number of operations CSE'd"};
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of operations DCE'd"};
};

template <typename DerivedT>
class CanonicalizerBase : public ::mlir::OperationPass<> {
public:
  using Base = CanonicalizerBase;

  CanonicalizerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CanonicalizerBase(const CanonicalizerBase &other) : ::mlir::OperationPass<>(other) {}
  CanonicalizerBase& operator=(const CanonicalizerBase &) = delete;
  CanonicalizerBase(CanonicalizerBase &&) = delete;
  CanonicalizerBase& operator=(CanonicalizerBase &&) = delete;
  ~CanonicalizerBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("canonicalize");
  }
  ::llvm::StringRef getArgument() const override { return "canonicalize"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Canonicalizer");
  }
  ::llvm::StringRef getName() const override { return "Canonicalizer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<ub::UBDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CanonicalizerBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> topDownProcessingEnabled{*this, "top-down", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<mlir::GreedySimplifyRegionLevel> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Perform control flow optimizations to the region tree"), ::llvm::cl::init(mlir::GreedySimplifyRegionLevel::Normal), ::llvm::cl::values(
               clEnumValN(mlir::GreedySimplifyRegionLevel::Disabled, "disabled",
                "Don't run any control-flow simplification."),
               clEnumValN(mlir::GreedySimplifyRegionLevel::Normal, "normal",
                "Perform simple control-flow simplifications (e.g. dead args elimination)."),
               clEnumValN(mlir::GreedySimplifyRegionLevel::Aggressive, "aggressive",
                "Perform aggressive control-flow simplification (e.g. block merging).")
              )};
  ::mlir::Pass::Option<int64_t> maxIterations{*this, "max-iterations", ::llvm::cl::desc("Max. iterations between applying patterns / simplifying regions"), ::llvm::cl::init(10)};
  ::mlir::Pass::Option<int64_t> maxNumRewrites{*this, "max-num-rewrites", ::llvm::cl::desc("Max. number of pattern rewrites within an iteration"), ::llvm::cl::init(-1)};
  ::mlir::Pass::Option<bool> testConvergence{*this, "test-convergence", ::llvm::cl::desc("Test only: Fail pass on non-convergence to detect cyclic pattern"), ::llvm::cl::init(false)};
  ::mlir::Pass::ListOption<std::string> disabledPatterns{*this, "disable-patterns", ::llvm::cl::desc("Labels of patterns that should be filtered out during application")};
  ::mlir::Pass::ListOption<std::string> enabledPatterns{*this, "enable-patterns", ::llvm::cl::desc("Labels of patterns that should be used during application, all other patterns are filtered out")};
};

template <typename DerivedT>
class CompositeFixedPointPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CompositeFixedPointPassBase;

  CompositeFixedPointPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CompositeFixedPointPassBase(const CompositeFixedPointPassBase &other) : ::mlir::OperationPass<>(other) {}
  CompositeFixedPointPassBase& operator=(const CompositeFixedPointPassBase &) = delete;
  CompositeFixedPointPassBase(CompositeFixedPointPassBase &&) = delete;
  CompositeFixedPointPassBase& operator=(CompositeFixedPointPassBase &&) = delete;
  ~CompositeFixedPointPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("composite-fixed-point-pass");
  }
  ::llvm::StringRef getArgument() const override { return "composite-fixed-point-pass"; }

  ::llvm::StringRef getDescription() const override { return "Composite fixed point pass"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CompositeFixedPointPass");
  }
  ::llvm::StringRef getName() const override { return "CompositeFixedPointPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CompositeFixedPointPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> name{*this, "name", ::llvm::cl::desc("Composite pass display name"), ::llvm::cl::init("CompositeFixedPointPass")};
  ::mlir::Pass::Option<std::string> pipelineStr{*this, "pipeline", ::llvm::cl::desc("Composite pass inner pipeline")};
  ::mlir::Pass::Option<int> maxIter{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations if inner pipeline"), ::llvm::cl::init(10)};
};

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<>(other) {}
  ControlFlowSinkBase& operator=(const ControlFlowSinkBase &) = delete;
  ControlFlowSinkBase(ControlFlowSinkBase &&) = delete;
  ControlFlowSinkBase& operator=(ControlFlowSinkBase &&) = delete;
  ~ControlFlowSinkBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("control-flow-sink");
  }
  ::llvm::StringRef getArgument() const override { return "control-flow-sink"; }

  ::llvm::StringRef getDescription() const override { return "Sink operations into conditional blocks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ControlFlowSinkBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numSunk{this, "num-sunk", "Number of operations sunk"};
};

template <typename DerivedT>
class GenerateRuntimeVerificationBase : public ::mlir::OperationPass<> {
public:
  using Base = GenerateRuntimeVerificationBase;

  GenerateRuntimeVerificationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  GenerateRuntimeVerificationBase(const GenerateRuntimeVerificationBase &other) : ::mlir::OperationPass<>(other) {}
  GenerateRuntimeVerificationBase& operator=(const GenerateRuntimeVerificationBase &) = delete;
  GenerateRuntimeVerificationBase(GenerateRuntimeVerificationBase &&) = delete;
  GenerateRuntimeVerificationBase& operator=(GenerateRuntimeVerificationBase &&) = delete;
  ~GenerateRuntimeVerificationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("generate-runtime-verification");
  }
  ::llvm::StringRef getArgument() const override { return "generate-runtime-verification"; }

  ::llvm::StringRef getDescription() const override { return "Generate additional runtime op verification checks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GenerateRuntimeVerification");
  }
  ::llvm::StringRef getName() const override { return "GenerateRuntimeVerification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GenerateRuntimeVerificationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class InlinerBase : public ::mlir::OperationPass<> {
public:
  using Base = InlinerBase;

  InlinerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InlinerBase(const InlinerBase &other) : ::mlir::OperationPass<>(other) {}
  InlinerBase& operator=(const InlinerBase &) = delete;
  InlinerBase(InlinerBase &&) = delete;
  InlinerBase& operator=(InlinerBase &&) = delete;
  ~InlinerBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("inline");
  }
  ::llvm::StringRef getArgument() const override { return "inline"; }

  ::llvm::StringRef getDescription() const override { return "Inline function calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Inliner");
  }
  ::llvm::StringRef getName() const override { return "Inliner"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InlinerBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> defaultPipelineStr{*this, "default-pipeline", ::llvm::cl::desc("The optimizer pipeline used for callables that do not have a dedicated optimizer pipeline in opPipelineList"), ::llvm::cl::init("canonicalize")};
  ::mlir::Pass::ListOption<OpPassManager> opPipelineList{*this, "op-pipelines", ::llvm::cl::desc("Callable operation specific optimizer pipelines (in the form of `dialect.op(pipeline)`)")};
  ::mlir::Pass::Option<unsigned> maxInliningIterations{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations when inlining within an SCC"), ::llvm::cl::init(4)};
  ::mlir::Pass::Option<unsigned> inliningThreshold{*this, "inlining-threshold", ::llvm::cl::desc("If the ratio between the number of the operations in the callee and the number of the operations in the caller exceeds this value (in percentage), then the callee is not inlined even if it is legal to inline it"), ::llvm::cl::init(-1U)};
};

template <typename DerivedT>
class LocationSnapshotBase : public ::mlir::OperationPass<> {
public:
  using Base = LocationSnapshotBase;

  LocationSnapshotBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LocationSnapshotBase(const LocationSnapshotBase &other) : ::mlir::OperationPass<>(other) {}
  LocationSnapshotBase& operator=(const LocationSnapshotBase &) = delete;
  LocationSnapshotBase(LocationSnapshotBase &&) = delete;
  LocationSnapshotBase& operator=(LocationSnapshotBase &&) = delete;
  ~LocationSnapshotBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("snapshot-op-locations");
  }
  ::llvm::StringRef getArgument() const override { return "snapshot-op-locations"; }

  ::llvm::StringRef getDescription() const override { return "Generate new locations from the current IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LocationSnapshot");
  }
  ::llvm::StringRef getName() const override { return "LocationSnapshot"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LocationSnapshotBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> fileName{*this, "filename", ::llvm::cl::desc("The filename to print the generated IR")};
  ::mlir::Pass::Option<std::string> tag{*this, "tag", ::llvm::cl::desc("A tag to use when fusing the new locations with the original. If unset, the locations are replaced.")};
  ::mlir::Pass::Option<bool> enableDebugInfo{*this, "print-debuginfo", ::llvm::cl::desc("Print debug info in MLIR output"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printGenericOpForm{*this, "print-op-generic", ::llvm::cl::desc("Print the generic op form"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useLocalScope{*this, "print-local-scope", ::llvm::cl::desc("Print with local scope and inline information (eliding aliases for attributes, types, and locations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printPrettyDebugInfo{*this, "pretty-debuginfo", ::llvm::cl::desc("Print pretty debug info in MLIR output"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LoopInvariantCodeMotionBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantCodeMotionBase;

  LoopInvariantCodeMotionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantCodeMotionBase(const LoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<>(other) {}
  LoopInvariantCodeMotionBase& operator=(const LoopInvariantCodeMotionBase &) = delete;
  LoopInvariantCodeMotionBase(LoopInvariantCodeMotionBase &&) = delete;
  LoopInvariantCodeMotionBase& operator=(LoopInvariantCodeMotionBase &&) = delete;
  ~LoopInvariantCodeMotionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopInvariantCodeMotionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LoopInvariantSubsetHoistingBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantSubsetHoistingBase;

  LoopInvariantSubsetHoistingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantSubsetHoistingBase(const LoopInvariantSubsetHoistingBase &other) : ::mlir::OperationPass<>(other) {}
  LoopInvariantSubsetHoistingBase& operator=(const LoopInvariantSubsetHoistingBase &) = delete;
  LoopInvariantSubsetHoistingBase(LoopInvariantSubsetHoistingBase &&) = delete;
  LoopInvariantSubsetHoistingBase& operator=(LoopInvariantSubsetHoistingBase &&) = delete;
  ~LoopInvariantSubsetHoistingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-subset-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-subset-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant subset ops outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantSubsetHoisting");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantSubsetHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LoopInvariantSubsetHoistingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class Mem2RegBase : public ::mlir::OperationPass<> {
public:
  using Base = Mem2RegBase;

  Mem2RegBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  Mem2RegBase(const Mem2RegBase &other) : ::mlir::OperationPass<>(other) {}
  Mem2RegBase& operator=(const Mem2RegBase &) = delete;
  Mem2RegBase(Mem2RegBase &&) = delete;
  Mem2RegBase& operator=(Mem2RegBase &&) = delete;
  ~Mem2RegBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mem2reg");
  }
  ::llvm::StringRef getArgument() const override { return "mem2reg"; }

  ::llvm::StringRef getDescription() const override { return "Promotes memory slots into values."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Mem2Reg");
  }
  ::llvm::StringRef getName() const override { return "Mem2Reg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(Mem2RegBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Perform control flow optimizations to the region tree"), ::llvm::cl::init(true)};
  ::mlir::Pass::Statistic promotedAmount{this, "promoted slots", "Total amount of memory slot promoted"};
  ::mlir::Pass::Statistic newBlockArgumentAmount{this, "new block args", "Total amount of new block argument inserted in blocks"};
};

template <typename DerivedT>
class PrintIRPassBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintIRPassBase;

  PrintIRPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintIRPassBase(const PrintIRPassBase &other) : ::mlir::OperationPass<>(other) {}
  PrintIRPassBase& operator=(const PrintIRPassBase &) = delete;
  PrintIRPassBase(PrintIRPassBase &&) = delete;
  PrintIRPassBase& operator=(PrintIRPassBase &&) = delete;
  ~PrintIRPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-ir");
  }
  ::llvm::StringRef getArgument() const override { return "print-ir"; }

  ::llvm::StringRef getDescription() const override { return "Print IR on the debug stream"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintIRPass");
  }
  ::llvm::StringRef getName() const override { return "PrintIRPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintIRPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> label{*this, "label", ::llvm::cl::desc("Label")};
};

template <typename DerivedT>
class PrintOpStatsBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintOpStatsBase;

  PrintOpStatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintOpStatsBase(const PrintOpStatsBase &other) : ::mlir::OperationPass<>(other) {}
  PrintOpStatsBase& operator=(const PrintOpStatsBase &) = delete;
  PrintOpStatsBase(PrintOpStatsBase &&) = delete;
  PrintOpStatsBase& operator=(PrintOpStatsBase &&) = delete;
  ~PrintOpStatsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-op-stats");
  }
  ::llvm::StringRef getArgument() const override { return "print-op-stats"; }

  ::llvm::StringRef getDescription() const override { return "Print statistics of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintOpStats");
  }
  ::llvm::StringRef getName() const override { return "PrintOpStats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrintOpStatsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> printAsJSON{*this, "json", ::llvm::cl::desc("print the stats as JSON"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class RemoveDeadValuesBase : public ::mlir::OperationPass<> {
public:
  using Base = RemoveDeadValuesBase;

  RemoveDeadValuesBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveDeadValuesBase(const RemoveDeadValuesBase &other) : ::mlir::OperationPass<>(other) {}
  RemoveDeadValuesBase& operator=(const RemoveDeadValuesBase &) = delete;
  RemoveDeadValuesBase(RemoveDeadValuesBase &&) = delete;
  RemoveDeadValuesBase& operator=(RemoveDeadValuesBase &&) = delete;
  ~RemoveDeadValuesBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("remove-dead-values");
  }
  ::llvm::StringRef getArgument() const override { return "remove-dead-values"; }

  ::llvm::StringRef getDescription() const override { return "Remove dead values"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveDeadValues");
  }
  ::llvm::StringRef getName() const override { return "RemoveDeadValues"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveDeadValuesBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCCPBase : public ::mlir::OperationPass<> {
public:
  using Base = SCCPBase;

  SCCPBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCCPBase(const SCCPBase &other) : ::mlir::OperationPass<>(other) {}
  SCCPBase& operator=(const SCCPBase &) = delete;
  SCCPBase(SCCPBase &&) = delete;
  SCCPBase& operator=(SCCPBase &&) = delete;
  ~SCCPBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sccp");
  }
  ::llvm::StringRef getArgument() const override { return "sccp"; }

  ::llvm::StringRef getDescription() const override { return "Sparse Conditional Constant Propagation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCCP");
  }
  ::llvm::StringRef getName() const override { return "SCCP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCCPBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SROABase : public ::mlir::OperationPass<> {
public:
  using Base = SROABase;

  SROABase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SROABase(const SROABase &other) : ::mlir::OperationPass<>(other) {}
  SROABase& operator=(const SROABase &) = delete;
  SROABase(SROABase &&) = delete;
  SROABase& operator=(SROABase &&) = delete;
  ~SROABase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sroa");
  }
  ::llvm::StringRef getArgument() const override { return "sroa"; }

  ::llvm::StringRef getDescription() const override { return "Scalar Replacement of Aggregates"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SROA");
  }
  ::llvm::StringRef getName() const override { return "SROA"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SROABase<DerivedT>)

protected:
  ::mlir::Pass::Statistic destructuredAmount{this, "destructured slots", "Total amount of memory slots destructured"};
  ::mlir::Pass::Statistic slotsWithMemoryBenefit{this, "slots with memory benefit", "Total amount of memory slots in which the destructured size was smaller than the total size after eliminating unused fields"};
  ::mlir::Pass::Statistic maxSubelementAmount{this, "max subelement number", "Maximal number of sub-elements a successfully destructured slot initially had"};
};

template <typename DerivedT>
class StripDebugInfoBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDebugInfoBase;

  StripDebugInfoBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDebugInfoBase(const StripDebugInfoBase &other) : ::mlir::OperationPass<>(other) {}
  StripDebugInfoBase& operator=(const StripDebugInfoBase &) = delete;
  StripDebugInfoBase(StripDebugInfoBase &&) = delete;
  StripDebugInfoBase& operator=(StripDebugInfoBase &&) = delete;
  ~StripDebugInfoBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("strip-debuginfo");
  }
  ::llvm::StringRef getArgument() const override { return "strip-debuginfo"; }

  ::llvm::StringRef getDescription() const override { return "Strip debug info from all operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDebugInfo");
  }
  ::llvm::StringRef getName() const override { return "StripDebugInfo"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripDebugInfoBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SymbolDCEBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolDCEBase;

  SymbolDCEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolDCEBase(const SymbolDCEBase &other) : ::mlir::OperationPass<>(other) {}
  SymbolDCEBase& operator=(const SymbolDCEBase &) = delete;
  SymbolDCEBase(SymbolDCEBase &&) = delete;
  SymbolDCEBase& operator=(SymbolDCEBase &&) = delete;
  ~SymbolDCEBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-dce");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-dce"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate dead symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolDCE");
  }
  ::llvm::StringRef getName() const override { return "SymbolDCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolDCEBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of symbols DCE'd"};
};

template <typename DerivedT>
class SymbolPrivatizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolPrivatizeBase;

  SymbolPrivatizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolPrivatizeBase(const SymbolPrivatizeBase &other) : ::mlir::OperationPass<>(other) {}
  SymbolPrivatizeBase& operator=(const SymbolPrivatizeBase &) = delete;
  SymbolPrivatizeBase(SymbolPrivatizeBase &&) = delete;
  SymbolPrivatizeBase& operator=(SymbolPrivatizeBase &&) = delete;
  ~SymbolPrivatizeBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-privatize");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-privatize"; }

  ::llvm::StringRef getDescription() const override { return "Mark symbols private"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolPrivatize");
  }
  ::llvm::StringRef getName() const override { return "SymbolPrivatize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolPrivatizeBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> exclude{*this, "exclude", ::llvm::cl::desc("Comma separated list of symbols that should not be marked private")};
};

template <typename DerivedT>
class TopologicalSortBase : public ::mlir::OperationPass<> {
public:
  using Base = TopologicalSortBase;

  TopologicalSortBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TopologicalSortBase(const TopologicalSortBase &other) : ::mlir::OperationPass<>(other) {}
  TopologicalSortBase& operator=(const TopologicalSortBase &) = delete;
  TopologicalSortBase(TopologicalSortBase &&) = delete;
  TopologicalSortBase& operator=(TopologicalSortBase &&) = delete;
  ~TopologicalSortBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("topological-sort");
  }
  ::llvm::StringRef getArgument() const override { return "topological-sort"; }

  ::llvm::StringRef getDescription() const override { return "Sort regions without SSA dominance in topological order"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TopologicalSort");
  }
  ::llvm::StringRef getName() const override { return "TopologicalSort"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TopologicalSortBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ViewOpGraphBase : public ::mlir::OperationPass<> {
public:
  using Base = ViewOpGraphBase;

  ViewOpGraphBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ViewOpGraphBase(const ViewOpGraphBase &other) : ::mlir::OperationPass<>(other) {}
  ViewOpGraphBase& operator=(const ViewOpGraphBase &) = delete;
  ViewOpGraphBase(ViewOpGraphBase &&) = delete;
  ViewOpGraphBase& operator=(ViewOpGraphBase &&) = delete;
  ~ViewOpGraphBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("view-op-graph");
  }
  ::llvm::StringRef getArgument() const override { return "view-op-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print Graphviz visualization of an operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ViewOpGraph");
  }
  ::llvm::StringRef getName() const override { return "ViewOpGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ViewOpGraphBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> maxLabelLen{*this, "max-label-len", ::llvm::cl::desc("Limit attribute/type length to number of chars"), ::llvm::cl::init(20)};
  ::mlir::Pass::Option<bool> printAttrs{*this, "print-attrs", ::llvm::cl::desc("Print attributes of operations"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printControlFlowEdges{*this, "print-control-flow-edges", ::llvm::cl::desc("Print control flow edges"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printDataFlowEdges{*this, "print-data-flow-edges", ::llvm::cl::desc("Print data flow edges"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printResultTypes{*this, "print-result-types", ::llvm::cl::desc("Print result types of operations"), ::llvm::cl::init(true)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
