/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Op Definitions                                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|* From: TransformOps.td                                                      *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifdef GET_OP_LIST
#undef GET_OP_LIST

::mlir::transform::AlternativesOp,
::mlir::transform::AnnotateOp,
::mlir::transform::ApplyCanonicalizationPatternsOp,
::mlir::transform::ApplyCommonSubexpressionEliminationOp,
::mlir::transform::ApplyConversionPatternsOp,
::mlir::transform::ApplyDeadCodeEliminationOp,
::mlir::transform::ApplyLoopInvariantCodeMotionOp,
::mlir::transform::ApplyPatternsOp,
::mlir::transform::ApplyRegisteredPassOp,
::mlir::transform::ApplyToLLVMConversionPatternsOp,
::mlir::transform::CastOp,
::mlir::transform::CollectMatchingOp,
::mlir::transform::ForeachMatchOp,
::mlir::transform::ForeachOp,
::mlir::transform::GetConsumersOfResult,
::mlir::transform::GetDefiningOp,
::mlir::transform::GetOperandOp,
::mlir::transform::GetParentOp,
::mlir::transform::GetProducerOfOperand,
::mlir::transform::GetResultOp,
::mlir::transform::GetTypeOp,
::mlir::transform::IncludeOp,
::mlir::transform::MatchOperationEmptyOp,
::mlir::transform::MatchOperationNameOp,
::mlir::transform::MatchParamCmpIOp,
::mlir::transform::MergeHandlesOp,
::mlir::transform::NamedSequenceOp,
::mlir::transform::NumAssociationsOp,
::mlir::transform::ParamConstantOp,
::mlir::transform::PrintOp,
::mlir::transform::ReplicateOp,
::mlir::transform::SelectOp,
::mlir::transform::SequenceOp,
::mlir::transform::SplitHandleOp,
::mlir::transform::VerifyOp,
::mlir::transform::YieldOp
#endif  // GET_OP_LIST

#ifdef GET_OP_CLASSES
#undef GET_OP_CLASSES


//===----------------------------------------------------------------------===//
// Local Utility Method Definitions
//===----------------------------------------------------------------------===//

namespace mlir {
namespace transform {

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_TransformOps1(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::transform::TransformHandleTypeInterface>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be TransformHandleTypeInterface instance, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_TransformOps2(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::transform::TransformHandleTypeInterface>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be variadic of TransformHandleTypeInterface instance, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_TransformOps3(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::transform::TransformParamTypeInterface>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be TransformParamTypeInterface instance, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_TransformOps4(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!(((::llvm::isa<::mlir::transform::TransformParamTypeInterface>(type))) || ((::llvm::isa<::mlir::transform::TransformHandleTypeInterface>(type))) || ((::llvm::isa<::mlir::transform::TransformValueHandleTypeInterface>(type))))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be variadic of any transform handle or parameter, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_TransformOps5(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!((::llvm::isa<::mlir::transform::TransformValueHandleTypeInterface>(type)))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be TransformValueHandleTypeInterface instance, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_type_constraint_TransformOps6(
    ::mlir::Operation *op, ::mlir::Type type, ::llvm::StringRef valueKind,
    unsigned valueIndex) {
  if (!(((::llvm::isa<::mlir::transform::TransformParamTypeInterface>(type))) || ((::llvm::isa<::mlir::transform::TransformHandleTypeInterface>(type))) || ((::llvm::isa<::mlir::transform::TransformValueHandleTypeInterface>(type))))) {
    return op->emitOpError(valueKind) << " #" << valueIndex
        << " must be any transform handle or parameter, but got " << type;
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps1(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::StringAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: string attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps1(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps1(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps2(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::ArrayAttr>(attr))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(attr), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::StringAttr>(attr))); }))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: string array attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps2(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps2(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps3(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::UnitAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: unit attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps3(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps3(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps4(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::IntegerAttr>(attr))) && ((::llvm::cast<::mlir::IntegerAttr>(attr).getType().isSignlessInteger(64)))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: 64-bit signless integer attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps4(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps4(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps5(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::SymbolRefAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: symbol reference attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps5(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps5(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps6(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::ArrayAttr>(attr))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(attr), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::SymbolRefAttr>(attr))); }))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: symbol ref array attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps6(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps6(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps7(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: i64 dense array attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps7(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps7(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps8(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((((::llvm::isa<::mlir::IntegerAttr>(attr))) && ((::llvm::cast<::mlir::IntegerAttr>(attr).getType().isSignlessInteger(64)))) && ((::llvm::cast<::mlir::IntegerAttr>(attr).getValue().isStrictlyPositive()))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: 64-bit signless integer attribute whose value is positive";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps8(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps8(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps9(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::transform::FailurePropagationModeAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: Silenceable error propagation policy";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps9(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps9(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps10(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::ArrayAttr>(attr))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(attr), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::DictionaryAttr>(attr))); }))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: Array of dictionary attributes";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps10(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps10(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps11(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::transform::MatchCmpIPredicateAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: allowed 32-bit signless integer cases: 0, 1, 2, 3, 4, 5";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps11(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps11(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps12(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !(((::llvm::isa<::mlir::TypeAttr>(attr))) && ((::llvm::isa<::mlir::FunctionType>(::llvm::cast<::mlir::TypeAttr>(attr).getValue()))) && ((true))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: function type attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps12(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps12(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps13(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((true)))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: any attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps13(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps13(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps14(
    ::mlir::Attribute attr, ::llvm::StringRef attrName, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  if (attr && !((::llvm::isa<::mlir::BoolAttr>(attr))))
    return emitError() << "attribute '" << attrName
        << "' failed to satisfy constraint: bool attribute";
  return ::mlir::success();
}
static ::llvm::LogicalResult __mlir_ods_local_attr_constraint_TransformOps14(
    ::mlir::Operation *op, ::mlir::Attribute attr, ::llvm::StringRef attrName) {
  return __mlir_ods_local_attr_constraint_TransformOps14(attr, attrName, [op]() {
    return op->emitOpError();
  });
}

static ::llvm::LogicalResult __mlir_ods_local_region_constraint_TransformOps1(
    ::mlir::Operation *op, ::mlir::Region &region, ::llvm::StringRef regionName,
    unsigned regionIndex) {
  if (!((::llvm::hasNItems(region, 1)))) {
    return op->emitOpError("region #") << regionIndex
        << (regionName.empty() ? " " : " ('" + regionName + "') ")
        << "failed to verify constraint: region with 1 blocks";
  }
  return ::mlir::success();
}

static ::llvm::LogicalResult __mlir_ods_local_region_constraint_TransformOps2(
    ::mlir::Operation *op, ::mlir::Region &region, ::llvm::StringRef regionName,
    unsigned regionIndex) {
  if (!((::llvm::hasNItemsOrLess(region, 1)))) {
    return op->emitOpError("region #") << regionIndex
        << (regionName.empty() ? " " : " ('" + regionName + "') ")
        << "failed to verify constraint: region with at most 1 blocks";
  }
  return ::mlir::success();
}
} // namespace transform
} // namespace mlir
namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::AlternativesOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
std::pair<unsigned, unsigned> AlternativesOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

} // namespace detail
AlternativesOpAdaptor::AlternativesOpAdaptor(AlternativesOp op) : AlternativesOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult AlternativesOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

std::pair<unsigned, unsigned> AlternativesOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange AlternativesOp::getScopeMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

std::pair<unsigned, unsigned> AlternativesOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

void AlternativesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, /*optional*/::mlir::Value scope, unsigned alternativesCount) {
  if (scope)
    odsState.addOperands(scope);
  for (unsigned i = 0; i < alternativesCount; ++i)
    (void)odsState.addRegion();
  odsState.addTypes(results);
}

void AlternativesOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes, unsigned numRegions) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  for (unsigned i = 0; i != numRegions; ++i)
    (void)odsState.addRegion();
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult AlternativesOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    if (valueGroup0.size() > 1) {
      return emitOpError("operand group starting at #") << index
          << " requires 0 or 1 element, but found " << valueGroup0.size();
    }

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;

    for (auto &region : getAlternatives())
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps1(*this, region, "alternatives", index++)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::llvm::LogicalResult AlternativesOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult AlternativesOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> scopeOperands;
  ::llvm::SMLoc scopeOperandsLoc;
  (void)scopeOperandsLoc;
  ::llvm::SmallVector<::mlir::Type, 1> scopeTypes;
  ::llvm::SmallVector<::mlir::Type, 1> resultsTypes;
  ::llvm::SmallVector<std::unique_ptr<::mlir::Region>, 2> fullRegions;

  {
    scopeOperandsLoc = parser.getCurrentLocation();
    ::mlir::OpAsmParser::UnresolvedOperand operand;
    ::mlir::OptionalParseResult parseResult =
                                    parser.parseOptionalOperand(operand);
    if (parseResult.has_value()) {
      if (failed(*parseResult))
        return ::mlir::failure();
      scopeOperands.push_back(operand);
    }
  }
  if (!scopeOperands.empty()) {
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::Type optionalType;
    ::mlir::OptionalParseResult parseResult =
                                    parser.parseOptionalType(optionalType);
    if (parseResult.has_value()) {
      if (failed(*parseResult))
        return ::mlir::failure();
      scopeTypes.push_back(optionalType);
    }
  }
  }
  if (::mlir::succeeded(parser.parseOptionalArrow())) {

  if (parser.parseTypeList(resultsTypes))
    return ::mlir::failure();
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDictWithKeyword(result.attributes))
      return ::mlir::failure();
  }

  {
    std::unique_ptr<::mlir::Region> region;
    auto firstRegionResult = parser.parseOptionalRegion(region);
    if (firstRegionResult.has_value()) {
      if (failed(*firstRegionResult))
        return ::mlir::failure();
      fullRegions.emplace_back(std::move(region));

      // Parse any trailing regions.
      while (succeeded(parser.parseOptionalComma())) {
        region = std::make_unique<::mlir::Region>();
        if (parser.parseRegion(*region))
          return ::mlir::failure();
        fullRegions.emplace_back(std::move(region));
      }
    }
  }

  for (auto &region : fullRegions)
    ensureTerminator(*region, parser.getBuilder(), result.location);
  result.addRegions(fullRegions);
  result.addTypes(resultsTypes);
  if (parser.resolveOperands(scopeOperands, scopeTypes, scopeOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void AlternativesOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  if (getScope()) {
    _odsPrinter << ' ';
    if (::mlir::Value value = getScope())
      _odsPrinter << value;
    _odsPrinter << ' ' << ":";
    _odsPrinter << ' ';
    _odsPrinter << (getScope() ? ::llvm::ArrayRef<::mlir::Type>(getScope().getType()) : ::llvm::ArrayRef<::mlir::Type>());
  }
  if (!getResults().empty()) {
    _odsPrinter << ' ' << "->";
    _odsPrinter << ' ';
    _odsPrinter << getResults().getTypes();
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDictWithKeyword((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ';
    llvm::interleaveComma(getOperation()->getRegions(), _odsPrinter, [&](::mlir::Region &region) {

  {
    bool printTerminator = true;
    if (auto *term = region.empty() ? nullptr : region.begin()->getTerminator()) {
      printTerminator = !term->getAttrDictionary().empty() ||
                        term->getNumOperands() != 0 ||
                        term->getNumResults() != 0;
    }
    _odsPrinter.printRegion(region, /*printEntryBlockArgs=*/true,
      /*printBlockTerminators=*/printTerminator);
  }
    });
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::AlternativesOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::AnnotateOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
AnnotateOpGenericAdaptorBase::AnnotateOpGenericAdaptorBase(AnnotateOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> AnnotateOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::StringRef AnnotateOpGenericAdaptorBase::getName() {
  auto attr = getNameAttr();
  return attr.getValue();
}

} // namespace detail
AnnotateOpAdaptor::AnnotateOpAdaptor(AnnotateOp op) : AnnotateOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult AnnotateOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_name = getProperties().name; (void)tblgen_name;
  if (!tblgen_name) return emitError(loc, "'transform.annotate' op ""requires attribute 'name'");

  if (tblgen_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_name))))
    return emitError(loc, "'transform.annotate' op ""attribute 'name' failed to satisfy constraint: string attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> AnnotateOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange AnnotateOp::getParamMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult AnnotateOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.name;
       auto attr = dict.get("name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute AnnotateOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("name",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code AnnotateOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.name.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> AnnotateOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "name")
      return prop.name;
  return std::nullopt;
}

void AnnotateOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "name") {
       prop.name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.name)>>(value);
       return;
    }
}

void AnnotateOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.name) attrs.append("name", prop.name);
}

::llvm::LogicalResult AnnotateOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "name", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult AnnotateOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.name)))
    return ::mlir::failure();
  return ::mlir::success();
}

void AnnotateOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.name);
}

::llvm::StringRef AnnotateOp::getName() {
  auto attr = getNameAttr();
  return attr.getValue();
}

void AnnotateOp::setName(::llvm::StringRef attrValue) {
  getProperties().name = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void AnnotateOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value target, ::mlir::StringAttr name, /*optional*/::mlir::Value param) {
  odsState.addOperands(target);
  if (param)
    odsState.addOperands(param);
  odsState.getOrAddProperties<Properties>().name = name;
}

void AnnotateOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::StringAttr name, /*optional*/::mlir::Value param) {
  odsState.addOperands(target);
  if (param)
    odsState.addOperands(param);
  odsState.getOrAddProperties<Properties>().name = name;
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AnnotateOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value target, ::llvm::StringRef name, /*optional*/::mlir::Value param) {
  odsState.addOperands(target);
  if (param)
    odsState.addOperands(param);
  odsState.getOrAddProperties<Properties>().name = odsBuilder.getStringAttr(name);
}

void AnnotateOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::llvm::StringRef name, /*optional*/::mlir::Value param) {
  odsState.addOperands(target);
  if (param)
    odsState.addOperands(param);
  odsState.getOrAddProperties<Properties>().name = odsBuilder.getStringAttr(name);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void AnnotateOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<AnnotateOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult AnnotateOp::verifyInvariantsImpl() {
  auto tblgen_name = getProperties().name; (void)tblgen_name;
  if (!tblgen_name) return emitOpError("requires attribute 'name'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_name, "name")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    if (valueGroup1.size() > 1) {
      return emitOpError("operand group starting at #") << index
          << " requires 0 or 1 element, but found " << valueGroup1.size();
    }

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult AnnotateOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult AnnotateOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::StringAttr nameAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> paramOperands;
  ::llvm::SMLoc paramOperandsLoc;
  (void)paramOperandsLoc;
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);
  ::llvm::SmallVector<::mlir::Type, 1> paramTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(nameAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (nameAttr) result.getOrAddProperties<AnnotateOp::Properties>().name = nameAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (::mlir::succeeded(parser.parseOptionalEqual())) {

  {
    paramOperandsLoc = parser.getCurrentLocation();
    ::mlir::OpAsmParser::UnresolvedOperand operand;
    ::mlir::OptionalParseResult parseResult =
                                    parser.parseOptionalOperand(operand);
    if (parseResult.has_value()) {
      if (failed(*parseResult))
        return ::mlir::failure();
      paramOperands.push_back(operand);
    }
  }
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  if (::mlir::succeeded(parser.parseOptionalComma())) {

  {
    ::mlir::Type optionalType;
    ::mlir::OptionalParseResult parseResult =
                                    parser.parseOptionalType(optionalType);
    if (parseResult.has_value()) {
      if (failed(*parseResult))
        return ::mlir::failure();
      paramTypes.push_back(optionalType);
    }
  }
  }
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(paramOperands, paramTypes, paramOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void AnnotateOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getNameAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("name");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  if (getParam()) {
    _odsPrinter << ' ' << "=";
    _odsPrinter << ' ';
    if (::mlir::Value value = getParam())
      _odsPrinter << value;
  }
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  if (getParam()) {
    _odsPrinter << ",";
    _odsPrinter << ' ';
    _odsPrinter << (getParam() ? ::llvm::ArrayRef<::mlir::Type>(getParam().getType()) : ::llvm::ArrayRef<::mlir::Type>());
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::AnnotateOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyCanonicalizationPatternsOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
ApplyCanonicalizationPatternsOpAdaptor::ApplyCanonicalizationPatternsOpAdaptor(ApplyCanonicalizationPatternsOp op) : ApplyCanonicalizationPatternsOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyCanonicalizationPatternsOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void ApplyCanonicalizationPatternsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState) {
}

void ApplyCanonicalizationPatternsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes) {
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyCanonicalizationPatternsOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult ApplyCanonicalizationPatternsOp::verifyInvariantsImpl() {
  return ::mlir::success();
}

::llvm::LogicalResult ApplyCanonicalizationPatternsOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ApplyCanonicalizationPatternsOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  return ::mlir::success();
}

void ApplyCanonicalizationPatternsOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyCanonicalizationPatternsOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyCommonSubexpressionEliminationOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
ApplyCommonSubexpressionEliminationOpAdaptor::ApplyCommonSubexpressionEliminationOpAdaptor(ApplyCommonSubexpressionEliminationOp op) : ApplyCommonSubexpressionEliminationOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyCommonSubexpressionEliminationOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void ApplyCommonSubexpressionEliminationOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value target) {
  odsState.addOperands(target);
}

void ApplyCommonSubexpressionEliminationOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target) {
  odsState.addOperands(target);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyCommonSubexpressionEliminationOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult ApplyCommonSubexpressionEliminationOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ApplyCommonSubexpressionEliminationOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ApplyCommonSubexpressionEliminationOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyCommonSubexpressionEliminationOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyCommonSubexpressionEliminationOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyConversionPatternsOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ApplyConversionPatternsOpGenericAdaptorBase::ApplyConversionPatternsOpGenericAdaptorBase(ApplyConversionPatternsOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOpGenericAdaptorBase::getLegalOps() {
  auto attr = getLegalOpsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOpGenericAdaptorBase::getIllegalOps() {
  auto attr = getIllegalOpsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOpGenericAdaptorBase::getLegalDialects() {
  auto attr = getLegalDialectsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOpGenericAdaptorBase::getIllegalDialects() {
  auto attr = getIllegalDialectsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::mlir::UnitAttr ApplyConversionPatternsOpGenericAdaptorBase::getPartialConversionAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().partial_conversion);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ApplyConversionPatternsOpGenericAdaptorBase::getPartialConversion() {
  auto attr = getPartialConversionAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::UnitAttr ApplyConversionPatternsOpGenericAdaptorBase::getPreserveHandlesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().preserve_handles);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ApplyConversionPatternsOpGenericAdaptorBase::getPreserveHandles() {
  auto attr = getPreserveHandlesAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
ApplyConversionPatternsOpAdaptor::ApplyConversionPatternsOpAdaptor(ApplyConversionPatternsOp op) : ApplyConversionPatternsOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyConversionPatternsOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_illegal_dialects = getProperties().illegal_dialects; (void)tblgen_illegal_dialects;
  auto tblgen_illegal_ops = getProperties().illegal_ops; (void)tblgen_illegal_ops;
  auto tblgen_legal_dialects = getProperties().legal_dialects; (void)tblgen_legal_dialects;
  auto tblgen_legal_ops = getProperties().legal_ops; (void)tblgen_legal_ops;
  auto tblgen_partial_conversion = getProperties().partial_conversion; (void)tblgen_partial_conversion;
  auto tblgen_preserve_handles = getProperties().preserve_handles; (void)tblgen_preserve_handles;

  if (tblgen_legal_ops && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_legal_ops))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_legal_ops), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::StringAttr>(attr))); }))))
    return emitError(loc, "'transform.apply_conversion_patterns' op ""attribute 'legal_ops' failed to satisfy constraint: string array attribute");

  if (tblgen_illegal_ops && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_illegal_ops))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_illegal_ops), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::StringAttr>(attr))); }))))
    return emitError(loc, "'transform.apply_conversion_patterns' op ""attribute 'illegal_ops' failed to satisfy constraint: string array attribute");

  if (tblgen_legal_dialects && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_legal_dialects))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_legal_dialects), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::StringAttr>(attr))); }))))
    return emitError(loc, "'transform.apply_conversion_patterns' op ""attribute 'legal_dialects' failed to satisfy constraint: string array attribute");

  if (tblgen_illegal_dialects && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_illegal_dialects))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_illegal_dialects), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::StringAttr>(attr))); }))))
    return emitError(loc, "'transform.apply_conversion_patterns' op ""attribute 'illegal_dialects' failed to satisfy constraint: string array attribute");

  if (tblgen_partial_conversion && !((::llvm::isa<::mlir::UnitAttr>(tblgen_partial_conversion))))
    return emitError(loc, "'transform.apply_conversion_patterns' op ""attribute 'partial_conversion' failed to satisfy constraint: unit attribute");

  if (tblgen_preserve_handles && !((::llvm::isa<::mlir::UnitAttr>(tblgen_preserve_handles))))
    return emitError(loc, "'transform.apply_conversion_patterns' op ""attribute 'preserve_handles' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ApplyConversionPatternsOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.illegal_dialects;
       auto attr = dict.get("illegal_dialects");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `illegal_dialects` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.illegal_ops;
       auto attr = dict.get("illegal_ops");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `illegal_ops` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.legal_dialects;
       auto attr = dict.get("legal_dialects");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `legal_dialects` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.legal_ops;
       auto attr = dict.get("legal_ops");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `legal_ops` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.partial_conversion;
       auto attr = dict.get("partial_conversion");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `partial_conversion` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.preserve_handles;
       auto attr = dict.get("preserve_handles");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `preserve_handles` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ApplyConversionPatternsOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.illegal_dialects;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("illegal_dialects",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.illegal_ops;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("illegal_ops",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.legal_dialects;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("legal_dialects",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.legal_ops;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("legal_ops",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.partial_conversion;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("partial_conversion",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.preserve_handles;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("preserve_handles",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ApplyConversionPatternsOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.illegal_dialects.getAsOpaquePointer()), 
    llvm::hash_value(prop.illegal_ops.getAsOpaquePointer()), 
    llvm::hash_value(prop.legal_dialects.getAsOpaquePointer()), 
    llvm::hash_value(prop.legal_ops.getAsOpaquePointer()), 
    llvm::hash_value(prop.partial_conversion.getAsOpaquePointer()), 
    llvm::hash_value(prop.preserve_handles.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ApplyConversionPatternsOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "illegal_dialects")
      return prop.illegal_dialects;

    if (name == "illegal_ops")
      return prop.illegal_ops;

    if (name == "legal_dialects")
      return prop.legal_dialects;

    if (name == "legal_ops")
      return prop.legal_ops;

    if (name == "partial_conversion")
      return prop.partial_conversion;

    if (name == "preserve_handles")
      return prop.preserve_handles;
  return std::nullopt;
}

void ApplyConversionPatternsOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "illegal_dialects") {
       prop.illegal_dialects = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.illegal_dialects)>>(value);
       return;
    }

    if (name == "illegal_ops") {
       prop.illegal_ops = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.illegal_ops)>>(value);
       return;
    }

    if (name == "legal_dialects") {
       prop.legal_dialects = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.legal_dialects)>>(value);
       return;
    }

    if (name == "legal_ops") {
       prop.legal_ops = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.legal_ops)>>(value);
       return;
    }

    if (name == "partial_conversion") {
       prop.partial_conversion = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.partial_conversion)>>(value);
       return;
    }

    if (name == "preserve_handles") {
       prop.preserve_handles = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.preserve_handles)>>(value);
       return;
    }
}

void ApplyConversionPatternsOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.illegal_dialects) attrs.append("illegal_dialects", prop.illegal_dialects);

    if (prop.illegal_ops) attrs.append("illegal_ops", prop.illegal_ops);

    if (prop.legal_dialects) attrs.append("legal_dialects", prop.legal_dialects);

    if (prop.legal_ops) attrs.append("legal_ops", prop.legal_ops);

    if (prop.partial_conversion) attrs.append("partial_conversion", prop.partial_conversion);

    if (prop.preserve_handles) attrs.append("preserve_handles", prop.preserve_handles);
}

::llvm::LogicalResult ApplyConversionPatternsOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getIllegalDialectsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(attr, "illegal_dialects", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getIllegalOpsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(attr, "illegal_ops", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getLegalDialectsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(attr, "legal_dialects", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getLegalOpsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(attr, "legal_ops", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getPartialConversionAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "partial_conversion", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getPreserveHandlesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "preserve_handles", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ApplyConversionPatternsOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.illegal_dialects)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.illegal_ops)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.legal_dialects)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.legal_ops)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.partial_conversion)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.preserve_handles)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyConversionPatternsOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.illegal_dialects);

  writer.writeOptionalAttribute(prop.illegal_ops);

  writer.writeOptionalAttribute(prop.legal_dialects);

  writer.writeOptionalAttribute(prop.legal_ops);

  writer.writeOptionalAttribute(prop.partial_conversion);

  writer.writeOptionalAttribute(prop.preserve_handles);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOp::getLegalOps() {
  auto attr = getLegalOpsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOp::getIllegalOps() {
  auto attr = getIllegalOpsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOp::getLegalDialects() {
  auto attr = getLegalDialectsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > ApplyConversionPatternsOp::getIllegalDialects() {
  auto attr = getIllegalDialectsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

bool ApplyConversionPatternsOp::getPartialConversion() {
  auto attr = getPartialConversionAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

bool ApplyConversionPatternsOp::getPreserveHandles() {
  auto attr = getPreserveHandlesAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void ApplyConversionPatternsOp::setPartialConversion(bool attrValue) {
    auto &odsProp = getProperties().partial_conversion;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ApplyConversionPatternsOp::setPreserveHandles(bool attrValue) {
    auto &odsProp = getProperties().preserve_handles;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

::llvm::LogicalResult ApplyConversionPatternsOp::verifyInvariantsImpl() {
  auto tblgen_illegal_dialects = getProperties().illegal_dialects; (void)tblgen_illegal_dialects;
  auto tblgen_illegal_ops = getProperties().illegal_ops; (void)tblgen_illegal_ops;
  auto tblgen_legal_dialects = getProperties().legal_dialects; (void)tblgen_legal_dialects;
  auto tblgen_legal_ops = getProperties().legal_ops; (void)tblgen_legal_ops;
  auto tblgen_partial_conversion = getProperties().partial_conversion; (void)tblgen_partial_conversion;
  auto tblgen_preserve_handles = getProperties().preserve_handles; (void)tblgen_preserve_handles;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(*this, tblgen_legal_ops, "legal_ops")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(*this, tblgen_illegal_ops, "illegal_ops")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(*this, tblgen_legal_dialects, "legal_dialects")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(*this, tblgen_illegal_dialects, "illegal_dialects")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_partial_conversion, "partial_conversion")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_preserve_handles, "preserve_handles")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;

    for (auto &region : ::llvm::MutableArrayRef((*this)->getRegion(0)))
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps2(*this, region, "patterns", index++)))
        return ::mlir::failure();

    for (auto &region : getDefaultTypeConverterRegion())
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps2(*this, region, "default_type_converter_region", index++)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::llvm::LogicalResult ApplyConversionPatternsOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult ApplyConversionPatternsOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  std::unique_ptr<::mlir::Region> patternsRegion = std::make_unique<::mlir::Region>();
  ::llvm::SmallVector<std::unique_ptr<::mlir::Region>, 2> default_type_converter_regionRegions;
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();

  if (parser.parseRegion(*patternsRegion))
    return ::mlir::failure();

  if (patternsRegion->empty()) patternsRegion->emplaceBlock();
  if (::mlir::succeeded(parser.parseOptionalKeyword("with"))) {
  if (parser.parseKeyword("type_converter"))
    return ::mlir::failure();

  {
    std::unique_ptr<::mlir::Region> region;
    auto firstRegionResult = parser.parseOptionalRegion(region);
    if (firstRegionResult.has_value()) {
      if (failed(*firstRegionResult))
        return ::mlir::failure();
      default_type_converter_regionRegions.emplace_back(std::move(region));

      // Parse any trailing regions.
      while (succeeded(parser.parseOptionalComma())) {
        region = std::make_unique<::mlir::Region>();
        if (parser.parseRegion(*region))
          return ::mlir::failure();
        default_type_converter_regionRegions.emplace_back(std::move(region));
      }
    }
  }

  for (auto &region : default_type_converter_regionRegions)
    if (region->empty()) region->emplaceBlock();
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  result.addRegion(std::move(patternsRegion));
  result.addRegions(default_type_converter_regionRegions);
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyConversionPatternsOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << ' ';
  _odsPrinter.printRegion(getPatterns());
  if (!getDefaultTypeConverterRegion().empty()) {
    _odsPrinter << ' ' << "with";
    _odsPrinter << ' ' << "type_converter";
    _odsPrinter << ' ';
      llvm::interleaveComma(getDefaultTypeConverterRegion(), _odsPrinter, [&](::mlir::Region &region) {
          _odsPrinter.printRegion(region);
      });
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getPartialConversionAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("partial_conversion");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getPreserveHandlesAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("preserve_handles");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyConversionPatternsOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyDeadCodeEliminationOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
ApplyDeadCodeEliminationOpAdaptor::ApplyDeadCodeEliminationOpAdaptor(ApplyDeadCodeEliminationOp op) : ApplyDeadCodeEliminationOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyDeadCodeEliminationOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void ApplyDeadCodeEliminationOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value target) {
  odsState.addOperands(target);
}

void ApplyDeadCodeEliminationOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target) {
  odsState.addOperands(target);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyDeadCodeEliminationOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult ApplyDeadCodeEliminationOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ApplyDeadCodeEliminationOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ApplyDeadCodeEliminationOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyDeadCodeEliminationOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyDeadCodeEliminationOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyLoopInvariantCodeMotionOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
ApplyLoopInvariantCodeMotionOpAdaptor::ApplyLoopInvariantCodeMotionOpAdaptor(ApplyLoopInvariantCodeMotionOp op) : ApplyLoopInvariantCodeMotionOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyLoopInvariantCodeMotionOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void ApplyLoopInvariantCodeMotionOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value target) {
  odsState.addOperands(target);
}

void ApplyLoopInvariantCodeMotionOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target) {
  odsState.addOperands(target);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyLoopInvariantCodeMotionOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult ApplyLoopInvariantCodeMotionOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ApplyLoopInvariantCodeMotionOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ApplyLoopInvariantCodeMotionOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyLoopInvariantCodeMotionOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyLoopInvariantCodeMotionOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyPatternsOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ApplyPatternsOpGenericAdaptorBase::ApplyPatternsOpGenericAdaptorBase(ApplyPatternsOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::UnitAttr ApplyPatternsOpGenericAdaptorBase::getApplyCseAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().apply_cse);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ApplyPatternsOpGenericAdaptorBase::getApplyCse() {
  auto attr = getApplyCseAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::IntegerAttr ApplyPatternsOpGenericAdaptorBase::getMaxIterationsAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::IntegerAttr>(getProperties().max_iterations);
  return attr;
}

uint64_t ApplyPatternsOpGenericAdaptorBase::getMaxIterations() {
  auto attr = getMaxIterationsAttr();
  return attr.getValue().getZExtValue();
}

::mlir::IntegerAttr ApplyPatternsOpGenericAdaptorBase::getMaxNumRewritesAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::IntegerAttr>(getProperties().max_num_rewrites);
  return attr;
}

uint64_t ApplyPatternsOpGenericAdaptorBase::getMaxNumRewrites() {
  auto attr = getMaxNumRewritesAttr();
  return attr.getValue().getZExtValue();
}

} // namespace detail
ApplyPatternsOpAdaptor::ApplyPatternsOpAdaptor(ApplyPatternsOp op) : ApplyPatternsOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyPatternsOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_apply_cse = getProperties().apply_cse; (void)tblgen_apply_cse;
  auto tblgen_max_iterations = getProperties().max_iterations; (void)tblgen_max_iterations;
  auto tblgen_max_num_rewrites = getProperties().max_num_rewrites; (void)tblgen_max_num_rewrites;

  if (tblgen_apply_cse && !((::llvm::isa<::mlir::UnitAttr>(tblgen_apply_cse))))
    return emitError(loc, "'transform.apply_patterns' op ""attribute 'apply_cse' failed to satisfy constraint: unit attribute");

  if (tblgen_max_iterations && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_max_iterations))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_max_iterations).getType().isSignlessInteger(64)))))
    return emitError(loc, "'transform.apply_patterns' op ""attribute 'max_iterations' failed to satisfy constraint: 64-bit signless integer attribute");

  if (tblgen_max_num_rewrites && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_max_num_rewrites))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_max_num_rewrites).getType().isSignlessInteger(64)))))
    return emitError(loc, "'transform.apply_patterns' op ""attribute 'max_num_rewrites' failed to satisfy constraint: 64-bit signless integer attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ApplyPatternsOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.apply_cse;
       auto attr = dict.get("apply_cse");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `apply_cse` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.max_iterations;
       auto attr = dict.get("max_iterations");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `max_iterations` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.max_num_rewrites;
       auto attr = dict.get("max_num_rewrites");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `max_num_rewrites` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ApplyPatternsOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.apply_cse;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("apply_cse",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.max_iterations;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("max_iterations",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.max_num_rewrites;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("max_num_rewrites",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ApplyPatternsOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.apply_cse.getAsOpaquePointer()), 
    llvm::hash_value(prop.max_iterations.getAsOpaquePointer()), 
    llvm::hash_value(prop.max_num_rewrites.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ApplyPatternsOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "apply_cse")
      return prop.apply_cse;

    if (name == "max_iterations")
      return prop.max_iterations;

    if (name == "max_num_rewrites")
      return prop.max_num_rewrites;
  return std::nullopt;
}

void ApplyPatternsOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "apply_cse") {
       prop.apply_cse = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.apply_cse)>>(value);
       return;
    }

    if (name == "max_iterations") {
       prop.max_iterations = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.max_iterations)>>(value);
       return;
    }

    if (name == "max_num_rewrites") {
       prop.max_num_rewrites = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.max_num_rewrites)>>(value);
       return;
    }
}

void ApplyPatternsOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.apply_cse) attrs.append("apply_cse", prop.apply_cse);

    if (prop.max_iterations) attrs.append("max_iterations", prop.max_iterations);

    if (prop.max_num_rewrites) attrs.append("max_num_rewrites", prop.max_num_rewrites);
}

::llvm::LogicalResult ApplyPatternsOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getApplyCseAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "apply_cse", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMaxIterationsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(attr, "max_iterations", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMaxNumRewritesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(attr, "max_num_rewrites", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ApplyPatternsOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.apply_cse)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.max_iterations)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.max_num_rewrites)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyPatternsOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.apply_cse);

  writer.writeOptionalAttribute(prop.max_iterations);

  writer.writeOptionalAttribute(prop.max_num_rewrites);
}

bool ApplyPatternsOp::getApplyCse() {
  auto attr = getApplyCseAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

uint64_t ApplyPatternsOp::getMaxIterations() {
  auto attr = getMaxIterationsAttr();
  return attr.getValue().getZExtValue();
}

uint64_t ApplyPatternsOp::getMaxNumRewrites() {
  auto attr = getMaxNumRewritesAttr();
  return attr.getValue().getZExtValue();
}

void ApplyPatternsOp::setApplyCse(bool attrValue) {
    auto &odsProp = getProperties().apply_cse;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ApplyPatternsOp::setMaxIterations(uint64_t attrValue) {
  getProperties().max_iterations = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), attrValue);
}

void ApplyPatternsOp::setMaxNumRewrites(uint64_t attrValue) {
  getProperties().max_num_rewrites = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), attrValue);
}

void ApplyPatternsOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.max_iterations)
    properties.max_iterations = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), static_cast<uint64_t>(-1));
  if (!properties.max_num_rewrites)
    properties.max_num_rewrites = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), static_cast<uint64_t>(-1));
}

::llvm::LogicalResult ApplyPatternsOp::verifyInvariantsImpl() {
  auto tblgen_apply_cse = getProperties().apply_cse; (void)tblgen_apply_cse;
  auto tblgen_max_iterations = getProperties().max_iterations; (void)tblgen_max_iterations;
  auto tblgen_max_num_rewrites = getProperties().max_num_rewrites; (void)tblgen_max_num_rewrites;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_apply_cse, "apply_cse")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(*this, tblgen_max_iterations, "max_iterations")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(*this, tblgen_max_num_rewrites, "max_num_rewrites")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;

    for (auto &region : ::llvm::MutableArrayRef((*this)->getRegion(0)))
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps2(*this, region, "patterns", index++)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::llvm::LogicalResult ApplyPatternsOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult ApplyPatternsOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  std::unique_ptr<::mlir::Region> patternsRegion = std::make_unique<::mlir::Region>();
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();

  if (parser.parseRegion(*patternsRegion))
    return ::mlir::failure();

  if (patternsRegion->empty()) patternsRegion->emplaceBlock();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  result.addRegion(std::move(patternsRegion));
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyPatternsOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << ' ';
  _odsPrinter.printRegion(getPatterns());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getApplyCseAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("apply_cse");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMaxIterationsAttr();
     if(attr && (attr == odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), static_cast<uint64_t>(-1))))
       elidedAttrs.push_back("max_iterations");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getMaxNumRewritesAttr();
     if(attr && (attr == odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), static_cast<uint64_t>(-1))))
       elidedAttrs.push_back("max_num_rewrites");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyPatternsOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyRegisteredPassOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ApplyRegisteredPassOpGenericAdaptorBase::ApplyRegisteredPassOpGenericAdaptorBase(ApplyRegisteredPassOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef ApplyRegisteredPassOpGenericAdaptorBase::getPassName() {
  auto attr = getPassNameAttr();
  return attr.getValue();
}

::mlir::StringAttr ApplyRegisteredPassOpGenericAdaptorBase::getOptionsAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().options);
  return attr;
}

::llvm::StringRef ApplyRegisteredPassOpGenericAdaptorBase::getOptions() {
  auto attr = getOptionsAttr();
  return attr.getValue();
}

} // namespace detail
ApplyRegisteredPassOpAdaptor::ApplyRegisteredPassOpAdaptor(ApplyRegisteredPassOp op) : ApplyRegisteredPassOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyRegisteredPassOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_options = getProperties().options; (void)tblgen_options;
  auto tblgen_pass_name = getProperties().pass_name; (void)tblgen_pass_name;
  if (!tblgen_pass_name) return emitError(loc, "'transform.apply_registered_pass' op ""requires attribute 'pass_name'");

  if (tblgen_pass_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_pass_name))))
    return emitError(loc, "'transform.apply_registered_pass' op ""attribute 'pass_name' failed to satisfy constraint: string attribute");

  if (tblgen_options && !((::llvm::isa<::mlir::StringAttr>(tblgen_options))))
    return emitError(loc, "'transform.apply_registered_pass' op ""attribute 'options' failed to satisfy constraint: string attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ApplyRegisteredPassOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.options;
       auto attr = dict.get("options");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `options` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.pass_name;
       auto attr = dict.get("pass_name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `pass_name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ApplyRegisteredPassOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.options;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("options",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.pass_name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("pass_name",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ApplyRegisteredPassOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.options.getAsOpaquePointer()), 
    llvm::hash_value(prop.pass_name.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ApplyRegisteredPassOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "options")
      return prop.options;

    if (name == "pass_name")
      return prop.pass_name;
  return std::nullopt;
}

void ApplyRegisteredPassOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "options") {
       prop.options = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.options)>>(value);
       return;
    }

    if (name == "pass_name") {
       prop.pass_name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.pass_name)>>(value);
       return;
    }
}

void ApplyRegisteredPassOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.options) attrs.append("options", prop.options);

    if (prop.pass_name) attrs.append("pass_name", prop.pass_name);
}

::llvm::LogicalResult ApplyRegisteredPassOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getOptionsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "options", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getPassNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "pass_name", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ApplyRegisteredPassOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.options)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.pass_name)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyRegisteredPassOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.options);
  writer.writeAttribute(prop.pass_name);
}

::llvm::StringRef ApplyRegisteredPassOp::getPassName() {
  auto attr = getPassNameAttr();
  return attr.getValue();
}

::llvm::StringRef ApplyRegisteredPassOp::getOptions() {
  auto attr = getOptionsAttr();
  return attr.getValue();
}

void ApplyRegisteredPassOp::setPassName(::llvm::StringRef attrValue) {
  getProperties().pass_name = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void ApplyRegisteredPassOp::setOptions(::llvm::StringRef attrValue) {
  getProperties().options = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void ApplyRegisteredPassOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::mlir::StringAttr pass_name, ::mlir::StringAttr options) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().pass_name = pass_name;
  if (options) {
    odsState.getOrAddProperties<Properties>().options = options;
  }
  odsState.addTypes(result);
}

void ApplyRegisteredPassOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::StringAttr pass_name, ::mlir::StringAttr options) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().pass_name = pass_name;
  if (options) {
    odsState.getOrAddProperties<Properties>().options = options;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyRegisteredPassOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::llvm::StringRef pass_name, ::llvm::StringRef options) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().pass_name = odsBuilder.getStringAttr(pass_name);
  odsState.getOrAddProperties<Properties>().options = odsBuilder.getStringAttr(options);
  odsState.addTypes(result);
}

void ApplyRegisteredPassOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::llvm::StringRef pass_name, ::llvm::StringRef options) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().pass_name = odsBuilder.getStringAttr(pass_name);
  odsState.getOrAddProperties<Properties>().options = odsBuilder.getStringAttr(options);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyRegisteredPassOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ApplyRegisteredPassOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void ApplyRegisteredPassOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.options)
    properties.options = odsBuilder.getStringAttr("");
}

::llvm::LogicalResult ApplyRegisteredPassOp::verifyInvariantsImpl() {
  auto tblgen_options = getProperties().options; (void)tblgen_options;
  auto tblgen_pass_name = getProperties().pass_name; (void)tblgen_pass_name;
  if (!tblgen_pass_name) return emitOpError("requires attribute 'pass_name'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_pass_name, "pass_name")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_options, "options")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ApplyRegisteredPassOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ApplyRegisteredPassOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::StringAttr pass_nameAttr;
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  if (parser.parseCustomAttributeWithFallback(pass_nameAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (pass_nameAttr) result.getOrAddProperties<ApplyRegisteredPassOp::Properties>().pass_name = pass_nameAttr;
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyRegisteredPassOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getPassNameAttr());
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("pass_name");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getOptionsAttr();
     if(attr && (attr == odsBuilder.getStringAttr("")))
       elidedAttrs.push_back("options");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyRegisteredPassOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ApplyToLLVMConversionPatternsOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ApplyToLLVMConversionPatternsOpGenericAdaptorBase::ApplyToLLVMConversionPatternsOpGenericAdaptorBase(ApplyToLLVMConversionPatternsOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef ApplyToLLVMConversionPatternsOpGenericAdaptorBase::getDialectName() {
  auto attr = getDialectNameAttr();
  return attr.getValue();
}

} // namespace detail
ApplyToLLVMConversionPatternsOpAdaptor::ApplyToLLVMConversionPatternsOpAdaptor(ApplyToLLVMConversionPatternsOp op) : ApplyToLLVMConversionPatternsOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ApplyToLLVMConversionPatternsOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_dialect_name = getProperties().dialect_name; (void)tblgen_dialect_name;
  if (!tblgen_dialect_name) return emitError(loc, "'transform.apply_conversion_patterns.dialect_to_llvm' op ""requires attribute 'dialect_name'");

  if (tblgen_dialect_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_dialect_name))))
    return emitError(loc, "'transform.apply_conversion_patterns.dialect_to_llvm' op ""attribute 'dialect_name' failed to satisfy constraint: string attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ApplyToLLVMConversionPatternsOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.dialect_name;
       auto attr = dict.get("dialect_name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `dialect_name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ApplyToLLVMConversionPatternsOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.dialect_name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("dialect_name",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ApplyToLLVMConversionPatternsOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.dialect_name.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ApplyToLLVMConversionPatternsOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "dialect_name")
      return prop.dialect_name;
  return std::nullopt;
}

void ApplyToLLVMConversionPatternsOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "dialect_name") {
       prop.dialect_name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.dialect_name)>>(value);
       return;
    }
}

void ApplyToLLVMConversionPatternsOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.dialect_name) attrs.append("dialect_name", prop.dialect_name);
}

::llvm::LogicalResult ApplyToLLVMConversionPatternsOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getDialectNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "dialect_name", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ApplyToLLVMConversionPatternsOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.dialect_name)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ApplyToLLVMConversionPatternsOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.dialect_name);
}

::llvm::StringRef ApplyToLLVMConversionPatternsOp::getDialectName() {
  auto attr = getDialectNameAttr();
  return attr.getValue();
}

void ApplyToLLVMConversionPatternsOp::setDialectName(::llvm::StringRef attrValue) {
  getProperties().dialect_name = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void ApplyToLLVMConversionPatternsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::StringAttr dialect_name) {
  odsState.getOrAddProperties<Properties>().dialect_name = dialect_name;
}

void ApplyToLLVMConversionPatternsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::StringAttr dialect_name) {
  odsState.getOrAddProperties<Properties>().dialect_name = dialect_name;
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyToLLVMConversionPatternsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::llvm::StringRef dialect_name) {
  odsState.getOrAddProperties<Properties>().dialect_name = odsBuilder.getStringAttr(dialect_name);
}

void ApplyToLLVMConversionPatternsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef dialect_name) {
  odsState.getOrAddProperties<Properties>().dialect_name = odsBuilder.getStringAttr(dialect_name);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ApplyToLLVMConversionPatternsOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ApplyToLLVMConversionPatternsOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult ApplyToLLVMConversionPatternsOp::verifyInvariantsImpl() {
  auto tblgen_dialect_name = getProperties().dialect_name; (void)tblgen_dialect_name;
  if (!tblgen_dialect_name) return emitOpError("requires attribute 'dialect_name'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_dialect_name, "dialect_name")))
    return ::mlir::failure();
  return ::mlir::success();
}

::llvm::LogicalResult ApplyToLLVMConversionPatternsOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult ApplyToLLVMConversionPatternsOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::StringAttr dialect_nameAttr;

  if (parser.parseCustomAttributeWithFallback(dialect_nameAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (dialect_nameAttr) result.getOrAddProperties<ApplyToLLVMConversionPatternsOp::Properties>().dialect_name = dialect_nameAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  return ::mlir::success();
}

void ApplyToLLVMConversionPatternsOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getDialectNameAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("dialect_name");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ApplyToLLVMConversionPatternsOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::CastOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
CastOpAdaptor::CastOpAdaptor(CastOp op) : CastOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult CastOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void CastOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type output, ::mlir::Value input) {
  odsState.addOperands(input);
  odsState.addTypes(output);
}

void CastOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value input) {
  odsState.addOperands(input);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void CastOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult CastOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult CastOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult CastOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand inputRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> inputOperands(&inputRawOperand, 1);  ::llvm::SMLoc inputOperandsLoc;
  (void)inputOperandsLoc;
  ::mlir::Type inputRawType{};
  ::llvm::ArrayRef<::mlir::Type> inputTypes(&inputRawType, 1);
  ::mlir::Type outputRawType{};
  ::llvm::ArrayRef<::mlir::Type> outputTypes(&outputRawType, 1);

  inputOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(inputRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    inputRawType = type;
  }
  if (parser.parseKeyword("to"))
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    outputRawType = type;
  }
  result.addTypes(outputTypes);
  if (parser.resolveOperands(inputOperands, inputTypes, inputOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void CastOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getInput();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getInput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ' ' << "to";
  _odsPrinter << ' ';
  {
    auto type = getOutput().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::CastOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::CollectMatchingOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
CollectMatchingOpGenericAdaptorBase::CollectMatchingOpGenericAdaptorBase(CollectMatchingOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::SymbolRefAttr CollectMatchingOpGenericAdaptorBase::getMatcher() {
  auto attr = getMatcherAttr();
  return attr;
}

} // namespace detail
CollectMatchingOpAdaptor::CollectMatchingOpAdaptor(CollectMatchingOp op) : CollectMatchingOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult CollectMatchingOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_matcher = getProperties().matcher; (void)tblgen_matcher;
  if (!tblgen_matcher) return emitError(loc, "'transform.collect_matching' op ""requires attribute 'matcher'");

  if (tblgen_matcher && !((::llvm::isa<::mlir::SymbolRefAttr>(tblgen_matcher))))
    return emitError(loc, "'transform.collect_matching' op ""attribute 'matcher' failed to satisfy constraint: symbol reference attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> CollectMatchingOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult CollectMatchingOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.matcher;
       auto attr = dict.get("matcher");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `matcher` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute CollectMatchingOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.matcher;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("matcher",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code CollectMatchingOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.matcher.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> CollectMatchingOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "matcher")
      return prop.matcher;
  return std::nullopt;
}

void CollectMatchingOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "matcher") {
       prop.matcher = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.matcher)>>(value);
       return;
    }
}

void CollectMatchingOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.matcher) attrs.append("matcher", prop.matcher);
}

::llvm::LogicalResult CollectMatchingOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getMatcherAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps5(attr, "matcher", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult CollectMatchingOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.matcher)))
    return ::mlir::failure();
  return ::mlir::success();
}

void CollectMatchingOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.matcher);
}

::mlir::SymbolRefAttr CollectMatchingOp::getMatcher() {
  auto attr = getMatcherAttr();
  return attr;
}

void CollectMatchingOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::Value root, ::mlir::SymbolRefAttr matcher) {
  odsState.addOperands(root);
  odsState.getOrAddProperties<Properties>().matcher = matcher;
  odsState.addTypes(results);
}

void CollectMatchingOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<CollectMatchingOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult CollectMatchingOp::verifyInvariantsImpl() {
  auto tblgen_matcher = getProperties().matcher; (void)tblgen_matcher;
  if (!tblgen_matcher) return emitOpError("requires attribute 'matcher'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps5(*this, tblgen_matcher, "matcher")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult CollectMatchingOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult CollectMatchingOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::SymbolRefAttr matcherAttr;
  ::mlir::OpAsmParser::UnresolvedOperand rootRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> rootOperands(&rootRawOperand, 1);  ::llvm::SMLoc rootOperandsLoc;
  (void)rootOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> rootTypes;
  ::llvm::ArrayRef<::mlir::Type> resultsTypes;

  if (parser.parseCustomAttributeWithFallback(matcherAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (matcherAttr) result.getOrAddProperties<CollectMatchingOp::Properties>().matcher = matcherAttr;
  if (parser.parseKeyword("in"))
    return ::mlir::failure();

  rootOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(rootRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType root__results_functionType;
  if (parser.parseType(root__results_functionType))
    return ::mlir::failure();
  rootTypes = root__results_functionType.getInputs();
  resultsTypes = root__results_functionType.getResults();
  result.addTypes(resultsTypes);
  if (parser.resolveOperands(rootOperands, rootTypes, rootOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void CollectMatchingOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getMatcherAttr());
  _odsPrinter << ' ' << "in";
  _odsPrinter << ' ';
  _odsPrinter << getRoot();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("matcher");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(::llvm::ArrayRef<::mlir::Type>(getRoot().getType()), getResults().getTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::CollectMatchingOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ForeachMatchOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ForeachMatchOpGenericAdaptorBase::ForeachMatchOpGenericAdaptorBase(ForeachMatchOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> ForeachMatchOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::UnitAttr ForeachMatchOpGenericAdaptorBase::getRestrictRootAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().restrict_root);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ForeachMatchOpGenericAdaptorBase::getRestrictRoot() {
  auto attr = getRestrictRootAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::UnitAttr ForeachMatchOpGenericAdaptorBase::getFlattenResultsAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().flatten_results);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ForeachMatchOpGenericAdaptorBase::getFlattenResults() {
  auto attr = getFlattenResultsAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::ArrayAttr ForeachMatchOpGenericAdaptorBase::getMatchers() {
  auto attr = getMatchersAttr();
  return attr;
}

::mlir::ArrayAttr ForeachMatchOpGenericAdaptorBase::getActions() {
  auto attr = getActionsAttr();
  return attr;
}

} // namespace detail
ForeachMatchOpAdaptor::ForeachMatchOpAdaptor(ForeachMatchOp op) : ForeachMatchOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ForeachMatchOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_actions = getProperties().actions; (void)tblgen_actions;
  if (!tblgen_actions) return emitError(loc, "'transform.foreach_match' op ""requires attribute 'actions'");
  auto tblgen_flatten_results = getProperties().flatten_results; (void)tblgen_flatten_results;
  auto tblgen_matchers = getProperties().matchers; (void)tblgen_matchers;
  if (!tblgen_matchers) return emitError(loc, "'transform.foreach_match' op ""requires attribute 'matchers'");
  auto tblgen_restrict_root = getProperties().restrict_root; (void)tblgen_restrict_root;

  if (tblgen_restrict_root && !((::llvm::isa<::mlir::UnitAttr>(tblgen_restrict_root))))
    return emitError(loc, "'transform.foreach_match' op ""attribute 'restrict_root' failed to satisfy constraint: unit attribute");

  if (tblgen_flatten_results && !((::llvm::isa<::mlir::UnitAttr>(tblgen_flatten_results))))
    return emitError(loc, "'transform.foreach_match' op ""attribute 'flatten_results' failed to satisfy constraint: unit attribute");

  if (tblgen_matchers && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_matchers))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_matchers), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::SymbolRefAttr>(attr))); }))))
    return emitError(loc, "'transform.foreach_match' op ""attribute 'matchers' failed to satisfy constraint: symbol ref array attribute");

  if (tblgen_actions && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_actions))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_actions), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::SymbolRefAttr>(attr))); }))))
    return emitError(loc, "'transform.foreach_match' op ""attribute 'actions' failed to satisfy constraint: symbol ref array attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ForeachMatchOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange ForeachMatchOp::getForwardedInputsMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

std::pair<unsigned, unsigned> ForeachMatchOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult ForeachMatchOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.actions;
       auto attr = dict.get("actions");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `actions` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.flatten_results;
       auto attr = dict.get("flatten_results");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `flatten_results` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.matchers;
       auto attr = dict.get("matchers");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `matchers` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.restrict_root;
       auto attr = dict.get("restrict_root");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `restrict_root` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ForeachMatchOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.actions;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("actions",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.flatten_results;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("flatten_results",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.matchers;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("matchers",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.restrict_root;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("restrict_root",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ForeachMatchOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.actions.getAsOpaquePointer()), 
    llvm::hash_value(prop.flatten_results.getAsOpaquePointer()), 
    llvm::hash_value(prop.matchers.getAsOpaquePointer()), 
    llvm::hash_value(prop.restrict_root.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ForeachMatchOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "actions")
      return prop.actions;

    if (name == "flatten_results")
      return prop.flatten_results;

    if (name == "matchers")
      return prop.matchers;

    if (name == "restrict_root")
      return prop.restrict_root;
  return std::nullopt;
}

void ForeachMatchOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "actions") {
       prop.actions = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.actions)>>(value);
       return;
    }

    if (name == "flatten_results") {
       prop.flatten_results = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.flatten_results)>>(value);
       return;
    }

    if (name == "matchers") {
       prop.matchers = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.matchers)>>(value);
       return;
    }

    if (name == "restrict_root") {
       prop.restrict_root = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.restrict_root)>>(value);
       return;
    }
}

void ForeachMatchOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.actions) attrs.append("actions", prop.actions);

    if (prop.flatten_results) attrs.append("flatten_results", prop.flatten_results);

    if (prop.matchers) attrs.append("matchers", prop.matchers);

    if (prop.restrict_root) attrs.append("restrict_root", prop.restrict_root);
}

::llvm::LogicalResult ForeachMatchOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getActionsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps6(attr, "actions", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getFlattenResultsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "flatten_results", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getMatchersAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps6(attr, "matchers", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRestrictRootAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "restrict_root", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ForeachMatchOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.actions)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.flatten_results)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.matchers)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.restrict_root)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ForeachMatchOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.actions);

  writer.writeOptionalAttribute(prop.flatten_results);
  writer.writeAttribute(prop.matchers);

  writer.writeOptionalAttribute(prop.restrict_root);
}

bool ForeachMatchOp::getRestrictRoot() {
  auto attr = getRestrictRootAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

bool ForeachMatchOp::getFlattenResults() {
  auto attr = getFlattenResultsAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::ArrayAttr ForeachMatchOp::getMatchers() {
  auto attr = getMatchersAttr();
  return attr;
}

::mlir::ArrayAttr ForeachMatchOp::getActions() {
  auto attr = getActionsAttr();
  return attr;
}

void ForeachMatchOp::setRestrictRoot(bool attrValue) {
    auto &odsProp = getProperties().restrict_root;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ForeachMatchOp::setFlattenResults(bool attrValue) {
    auto &odsProp = getProperties().flatten_results;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ForeachMatchOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type updated, ::mlir::TypeRange forwarded_outputs, ::mlir::Value root, ::mlir::ValueRange forwarded_inputs, /*optional*/::mlir::UnitAttr restrict_root, /*optional*/::mlir::UnitAttr flatten_results, ::mlir::ArrayAttr matchers, ::mlir::ArrayAttr actions) {
  odsState.addOperands(root);
  odsState.addOperands(forwarded_inputs);
  if (restrict_root) {
    odsState.getOrAddProperties<Properties>().restrict_root = restrict_root;
  }
  if (flatten_results) {
    odsState.getOrAddProperties<Properties>().flatten_results = flatten_results;
  }
  odsState.getOrAddProperties<Properties>().matchers = matchers;
  odsState.getOrAddProperties<Properties>().actions = actions;
  odsState.addTypes(updated);
  odsState.addTypes(forwarded_outputs);
}

void ForeachMatchOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value root, ::mlir::ValueRange forwarded_inputs, /*optional*/::mlir::UnitAttr restrict_root, /*optional*/::mlir::UnitAttr flatten_results, ::mlir::ArrayAttr matchers, ::mlir::ArrayAttr actions) {
  odsState.addOperands(root);
  odsState.addOperands(forwarded_inputs);
  if (restrict_root) {
    odsState.getOrAddProperties<Properties>().restrict_root = restrict_root;
  }
  if (flatten_results) {
    odsState.getOrAddProperties<Properties>().flatten_results = flatten_results;
  }
  odsState.getOrAddProperties<Properties>().matchers = matchers;
  odsState.getOrAddProperties<Properties>().actions = actions;
  assert(resultTypes.size() >= 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ForeachMatchOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type updated, ::mlir::TypeRange forwarded_outputs, ::mlir::Value root, ::mlir::ValueRange forwarded_inputs, /*optional*/bool restrict_root, /*optional*/bool flatten_results, ::mlir::ArrayAttr matchers, ::mlir::ArrayAttr actions) {
  odsState.addOperands(root);
  odsState.addOperands(forwarded_inputs);
  if (restrict_root) {
    odsState.getOrAddProperties<Properties>().restrict_root = ((restrict_root) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (flatten_results) {
    odsState.getOrAddProperties<Properties>().flatten_results = ((flatten_results) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.getOrAddProperties<Properties>().matchers = matchers;
  odsState.getOrAddProperties<Properties>().actions = actions;
  odsState.addTypes(updated);
  odsState.addTypes(forwarded_outputs);
}

void ForeachMatchOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value root, ::mlir::ValueRange forwarded_inputs, /*optional*/bool restrict_root, /*optional*/bool flatten_results, ::mlir::ArrayAttr matchers, ::mlir::ArrayAttr actions) {
  odsState.addOperands(root);
  odsState.addOperands(forwarded_inputs);
  if (restrict_root) {
    odsState.getOrAddProperties<Properties>().restrict_root = ((restrict_root) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (flatten_results) {
    odsState.getOrAddProperties<Properties>().flatten_results = ((flatten_results) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.getOrAddProperties<Properties>().matchers = matchers;
  odsState.getOrAddProperties<Properties>().actions = actions;
  assert(resultTypes.size() >= 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ForeachMatchOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() >= 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ForeachMatchOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult ForeachMatchOp::verifyInvariantsImpl() {
  auto tblgen_actions = getProperties().actions; (void)tblgen_actions;
  if (!tblgen_actions) return emitOpError("requires attribute 'actions'");
  auto tblgen_flatten_results = getProperties().flatten_results; (void)tblgen_flatten_results;
  auto tblgen_matchers = getProperties().matchers; (void)tblgen_matchers;
  if (!tblgen_matchers) return emitOpError("requires attribute 'matchers'");
  auto tblgen_restrict_root = getProperties().restrict_root; (void)tblgen_restrict_root;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_restrict_root, "restrict_root")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_flatten_results, "flatten_results")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps6(*this, tblgen_matchers, "matchers")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps6(*this, tblgen_actions, "actions")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSResults(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ForeachMatchOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult ForeachMatchOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand rootRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> rootOperands(&rootRawOperand, 1);  ::llvm::SMLoc rootOperandsLoc;
  (void)rootOperandsLoc;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> forwarded_inputsOperands;
  ::llvm::SMLoc forwarded_inputsOperandsLoc;
  (void)forwarded_inputsOperandsLoc;
  ::mlir::ArrayAttr matchersAttr;
  ::mlir::ArrayAttr actionsAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;
  bool restrict_rootClause = false;
  bool flatten_resultsClause = false;
  while(true) {
if (succeeded(parser.parseOptionalKeyword("restrict_root"))) {

  if (restrict_rootClause) {
    return parser.emitError(parser.getNameLoc())
          << "`restrict_root` clause can appear at most once in the expansion of the "
             "oilist directive";
  }
  restrict_rootClause = true;
    result.getOrAddProperties<ForeachMatchOp::Properties>().restrict_root = parser.getBuilder().getUnitAttr();    } else if (succeeded(parser.parseOptionalKeyword("flatten_results"))) {

  if (flatten_resultsClause) {
    return parser.emitError(parser.getNameLoc())
          << "`flatten_results` clause can appear at most once in the expansion of the "
             "oilist directive";
  }
  flatten_resultsClause = true;
    result.getOrAddProperties<ForeachMatchOp::Properties>().flatten_results = parser.getBuilder().getUnitAttr();    } else  {
    break;
  }
}
  if (parser.parseKeyword("in"))
    return ::mlir::failure();

  rootOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(rootRawOperand))
    return ::mlir::failure();
  if (::mlir::succeeded(parser.parseOptionalComma())) {

  forwarded_inputsOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(forwarded_inputsOperands))
    return ::mlir::failure();
  }
  {
    auto odsResult = parseForeachMatchSymbols(parser, matchersAttr, actionsAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<ForeachMatchOp::Properties>().matchers = matchersAttr;
    result.getOrAddProperties<ForeachMatchOp::Properties>().actions = actionsAttr;
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(::llvm::concat<const ::mlir::OpAsmParser::UnresolvedOperand>(rootOperands, forwarded_inputsOperands), allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ForeachMatchOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  if (false || ((getRestrictRootAttr() && getRestrictRootAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr)))) {
  _odsPrinter << ' ' << "restrict_root";
  }
  if (false || ((getFlattenResultsAttr() && getFlattenResultsAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr)))) {
  _odsPrinter << ' ' << "flatten_results";
  }
  _odsPrinter << ' ' << "in";
  _odsPrinter << ' ';
  _odsPrinter << getRoot();
  if (!getForwardedInputs().empty()) {
    _odsPrinter << ",";
    _odsPrinter << ' ';
    _odsPrinter << getForwardedInputs();
  }
  _odsPrinter << ' ';
  printForeachMatchSymbols(_odsPrinter, *this, getMatchersAttr(), getActionsAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("restrict_root");
  elidedAttrs.push_back("flatten_results");
  elidedAttrs.push_back("matchers");
  elidedAttrs.push_back("actions");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getRestrictRootAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("restrict_root");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getFlattenResultsAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("flatten_results");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ForeachMatchOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ForeachOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ForeachOpGenericAdaptorBase::ForeachOpGenericAdaptorBase(ForeachOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> ForeachOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::UnitAttr ForeachOpGenericAdaptorBase::getWithZipShortestAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().with_zip_shortest);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool ForeachOpGenericAdaptorBase::getWithZipShortest() {
  auto attr = getWithZipShortestAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
ForeachOpAdaptor::ForeachOpAdaptor(ForeachOp op) : ForeachOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ForeachOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_with_zip_shortest = getProperties().with_zip_shortest; (void)tblgen_with_zip_shortest;

  if (tblgen_with_zip_shortest && !((::llvm::isa<::mlir::UnitAttr>(tblgen_with_zip_shortest))))
    return emitError(loc, "'transform.foreach' op ""attribute 'with_zip_shortest' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ForeachOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange ForeachOp::getTargetsMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

std::pair<unsigned, unsigned> ForeachOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult ForeachOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.with_zip_shortest;
       auto attr = dict.get("with_zip_shortest");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `with_zip_shortest` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ForeachOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.with_zip_shortest;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("with_zip_shortest",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ForeachOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.with_zip_shortest.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ForeachOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "with_zip_shortest")
      return prop.with_zip_shortest;
  return std::nullopt;
}

void ForeachOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "with_zip_shortest") {
       prop.with_zip_shortest = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.with_zip_shortest)>>(value);
       return;
    }
}

void ForeachOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.with_zip_shortest) attrs.append("with_zip_shortest", prop.with_zip_shortest);
}

::llvm::LogicalResult ForeachOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getWithZipShortestAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "with_zip_shortest", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ForeachOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.with_zip_shortest)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ForeachOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.with_zip_shortest);
}

bool ForeachOp::getWithZipShortest() {
  auto attr = getWithZipShortestAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void ForeachOp::setWithZipShortest(bool attrValue) {
    auto &odsProp = getProperties().with_zip_shortest;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void ForeachOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::ValueRange targets, /*optional*/::mlir::UnitAttr with_zip_shortest) {
  odsState.addOperands(targets);
  if (with_zip_shortest) {
    odsState.getOrAddProperties<Properties>().with_zip_shortest = with_zip_shortest;
  }
  (void)odsState.addRegion();
  odsState.addTypes(results);
}

void ForeachOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::ValueRange targets, /*optional*/bool with_zip_shortest) {
  odsState.addOperands(targets);
  if (with_zip_shortest) {
    odsState.getOrAddProperties<Properties>().with_zip_shortest = ((with_zip_shortest) ? odsBuilder.getUnitAttr() : nullptr);
  }
  (void)odsState.addRegion();
  odsState.addTypes(results);
}

void ForeachOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  for (unsigned i = 0; i != 1; ++i)
    (void)odsState.addRegion();
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ForeachOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult ForeachOp::verifyInvariantsImpl() {
  auto tblgen_with_zip_shortest = getProperties().with_zip_shortest; (void)tblgen_with_zip_shortest;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_with_zip_shortest, "with_zip_shortest")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;

    for (auto &region : ::llvm::MutableArrayRef((*this)->getRegion(0)))
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps1(*this, region, "body", index++)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::llvm::LogicalResult ForeachOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult ForeachOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> targetsOperands;
  ::llvm::SMLoc targetsOperandsLoc;
  (void)targetsOperandsLoc;
  ::llvm::SmallVector<::mlir::Type, 1> targetsTypes;
  ::llvm::SmallVector<::mlir::Type, 1> resultsTypes;
  std::unique_ptr<::mlir::Region> bodyRegion = std::make_unique<::mlir::Region>();

  targetsOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(targetsOperands))
    return ::mlir::failure();
  bool with_zip_shortestClause = false;
  while(true) {
if (succeeded(parser.parseOptionalKeyword("with_zip_shortest"))) {

  if (with_zip_shortestClause) {
    return parser.emitError(parser.getNameLoc())
          << "`with_zip_shortest` clause can appear at most once in the expansion of the "
             "oilist directive";
  }
  with_zip_shortestClause = true;
    result.getOrAddProperties<ForeachOp::Properties>().with_zip_shortest = parser.getBuilder().getUnitAttr();    } else  {
    break;
  }
}
  if (parser.parseColon())
    return ::mlir::failure();

  if (parser.parseTypeList(targetsTypes))
    return ::mlir::failure();
  if (::mlir::succeeded(parser.parseOptionalArrow())) {

  if (parser.parseTypeList(resultsTypes))
    return ::mlir::failure();
  }

  if (parser.parseRegion(*bodyRegion))
    return ::mlir::failure();

  ensureTerminator(*bodyRegion, parser.getBuilder(), result.location);
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  result.addRegion(std::move(bodyRegion));
  result.addTypes(resultsTypes);
  if (parser.resolveOperands(targetsOperands, targetsTypes, targetsOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ForeachOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTargets();
  if (false || ((getWithZipShortestAttr() && getWithZipShortestAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr)))) {
  _odsPrinter << ' ' << "with_zip_shortest";
  }
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter << getTargets().getTypes();
  if (!getResults().empty()) {
    _odsPrinter << ' ' << "->";
    _odsPrinter << ' ';
    _odsPrinter << getResults().getTypes();
  }
  _odsPrinter << ' ';

  {
    bool printTerminator = true;
    if (auto *term = getBody().empty() ? nullptr : getBody().begin()->getTerminator()) {
      printTerminator = !term->getAttrDictionary().empty() ||
                        term->getNumOperands() != 0 ||
                        term->getNumResults() != 0;
    }
    _odsPrinter.printRegion(getBody(), /*printEntryBlockArgs=*/true,
      /*printBlockTerminators=*/printTerminator);
  }
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("with_zip_shortest");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getWithZipShortestAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("with_zip_shortest");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ForeachOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetConsumersOfResult definitions
//===----------------------------------------------------------------------===//

namespace detail {
GetConsumersOfResultGenericAdaptorBase::GetConsumersOfResultGenericAdaptorBase(GetConsumersOfResult op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

uint64_t GetConsumersOfResultGenericAdaptorBase::getResultNumber() {
  auto attr = getResultNumberAttr();
  return attr.getValue().getZExtValue();
}

} // namespace detail
GetConsumersOfResultAdaptor::GetConsumersOfResultAdaptor(GetConsumersOfResult op) : GetConsumersOfResultGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetConsumersOfResultAdaptor::verify(::mlir::Location loc) {
  auto tblgen_result_number = getProperties().result_number; (void)tblgen_result_number;
  if (!tblgen_result_number) return emitError(loc, "'transform.get_consumers_of_result' op ""requires attribute 'result_number'");

  if (tblgen_result_number && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_result_number))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_result_number).getType().isSignlessInteger(64)))))
    return emitError(loc, "'transform.get_consumers_of_result' op ""attribute 'result_number' failed to satisfy constraint: 64-bit signless integer attribute");
  return ::mlir::success();
}

::llvm::LogicalResult GetConsumersOfResult::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.result_number;
       auto attr = dict.get("result_number");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `result_number` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GetConsumersOfResult::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.result_number;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("result_number",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GetConsumersOfResult::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.result_number.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GetConsumersOfResult::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "result_number")
      return prop.result_number;
  return std::nullopt;
}

void GetConsumersOfResult::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "result_number") {
       prop.result_number = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.result_number)>>(value);
       return;
    }
}

void GetConsumersOfResult::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.result_number) attrs.append("result_number", prop.result_number);
}

::llvm::LogicalResult GetConsumersOfResult::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getResultNumberAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(attr, "result_number", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GetConsumersOfResult::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.result_number)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetConsumersOfResult::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.result_number);
}

uint64_t GetConsumersOfResult::getResultNumber() {
  auto attr = getResultNumberAttr();
  return attr.getValue().getZExtValue();
}

void GetConsumersOfResult::setResultNumber(uint64_t attrValue) {
  getProperties().result_number = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), attrValue);
}

void GetConsumersOfResult::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type consumers, ::mlir::Value target, ::mlir::IntegerAttr result_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().result_number = result_number;
  odsState.addTypes(consumers);
}

void GetConsumersOfResult::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::IntegerAttr result_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().result_number = result_number;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetConsumersOfResult::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type consumers, ::mlir::Value target, uint64_t result_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().result_number = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), result_number);
  odsState.addTypes(consumers);
}

void GetConsumersOfResult::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, uint64_t result_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().result_number = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), result_number);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetConsumersOfResult::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GetConsumersOfResult::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult GetConsumersOfResult::verifyInvariantsImpl() {
  auto tblgen_result_number = getProperties().result_number; (void)tblgen_result_number;
  if (!tblgen_result_number) return emitOpError("requires attribute 'result_number'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(*this, tblgen_result_number, "result_number")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetConsumersOfResult::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult GetConsumersOfResult::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::IntegerAttr result_numberAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  if (parser.parseLSquare())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(result_numberAttr, parser.getBuilder().getIntegerType(64))) {
    return ::mlir::failure();
  }
  if (result_numberAttr) result.getOrAddProperties<GetConsumersOfResult::Properties>().result_number = result_numberAttr;
  if (parser.parseRSquare())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetConsumersOfResult::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << "[";
  _odsPrinter.printAttributeWithoutType(getResultNumberAttr());
  _odsPrinter << "]";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("result_number");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetConsumersOfResult)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetDefiningOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
GetDefiningOpAdaptor::GetDefiningOpAdaptor(GetDefiningOp op) : GetDefiningOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetDefiningOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void GetDefiningOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target) {
  odsState.addOperands(target);
  odsState.addTypes(result);
}

void GetDefiningOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target) {
  odsState.addOperands(target);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetDefiningOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult GetDefiningOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps5(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetDefiningOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult GetDefiningOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetDefiningOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetDefiningOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetOperandOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
GetOperandOpGenericAdaptorBase::GetOperandOpGenericAdaptorBase(GetOperandOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::ArrayRef<int64_t> GetOperandOpGenericAdaptorBase::getRawPositionList() {
  auto attr = getRawPositionListAttr();
  return attr;
}

::mlir::UnitAttr GetOperandOpGenericAdaptorBase::getIsInvertedAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().is_inverted);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetOperandOpGenericAdaptorBase::getIsInverted() {
  auto attr = getIsInvertedAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::UnitAttr GetOperandOpGenericAdaptorBase::getIsAllAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().is_all);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetOperandOpGenericAdaptorBase::getIsAll() {
  auto attr = getIsAllAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
GetOperandOpAdaptor::GetOperandOpAdaptor(GetOperandOp op) : GetOperandOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetOperandOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_is_all = getProperties().is_all; (void)tblgen_is_all;
  auto tblgen_is_inverted = getProperties().is_inverted; (void)tblgen_is_inverted;
  auto tblgen_raw_position_list = getProperties().raw_position_list; (void)tblgen_raw_position_list;
  if (!tblgen_raw_position_list) return emitError(loc, "'transform.get_operand' op ""requires attribute 'raw_position_list'");

  if (tblgen_raw_position_list && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_raw_position_list))))
    return emitError(loc, "'transform.get_operand' op ""attribute 'raw_position_list' failed to satisfy constraint: i64 dense array attribute");

  if (tblgen_is_inverted && !((::llvm::isa<::mlir::UnitAttr>(tblgen_is_inverted))))
    return emitError(loc, "'transform.get_operand' op ""attribute 'is_inverted' failed to satisfy constraint: unit attribute");

  if (tblgen_is_all && !((::llvm::isa<::mlir::UnitAttr>(tblgen_is_all))))
    return emitError(loc, "'transform.get_operand' op ""attribute 'is_all' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

::llvm::LogicalResult GetOperandOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.is_all;
       auto attr = dict.get("is_all");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `is_all` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.is_inverted;
       auto attr = dict.get("is_inverted");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `is_inverted` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.raw_position_list;
       auto attr = dict.get("raw_position_list");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `raw_position_list` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GetOperandOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.is_all;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("is_all",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.is_inverted;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("is_inverted",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.raw_position_list;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("raw_position_list",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GetOperandOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.is_all.getAsOpaquePointer()), 
    llvm::hash_value(prop.is_inverted.getAsOpaquePointer()), 
    llvm::hash_value(prop.raw_position_list.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GetOperandOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "is_all")
      return prop.is_all;

    if (name == "is_inverted")
      return prop.is_inverted;

    if (name == "raw_position_list")
      return prop.raw_position_list;
  return std::nullopt;
}

void GetOperandOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "is_all") {
       prop.is_all = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.is_all)>>(value);
       return;
    }

    if (name == "is_inverted") {
       prop.is_inverted = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.is_inverted)>>(value);
       return;
    }

    if (name == "raw_position_list") {
       prop.raw_position_list = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.raw_position_list)>>(value);
       return;
    }
}

void GetOperandOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.is_all) attrs.append("is_all", prop.is_all);

    if (prop.is_inverted) attrs.append("is_inverted", prop.is_inverted);

    if (prop.raw_position_list) attrs.append("raw_position_list", prop.raw_position_list);
}

::llvm::LogicalResult GetOperandOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getIsAllAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "is_all", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getIsInvertedAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "is_inverted", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRawPositionListAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps7(attr, "raw_position_list", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GetOperandOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.is_all)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.is_inverted)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.raw_position_list)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetOperandOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.is_all);

  writer.writeOptionalAttribute(prop.is_inverted);
  writer.writeAttribute(prop.raw_position_list);
}

::llvm::ArrayRef<int64_t> GetOperandOp::getRawPositionList() {
  auto attr = getRawPositionListAttr();
  return attr;
}

bool GetOperandOp::getIsInverted() {
  auto attr = getIsInvertedAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

bool GetOperandOp::getIsAll() {
  auto attr = getIsAllAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void GetOperandOp::setRawPositionList(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().raw_position_list = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void GetOperandOp::setIsInverted(bool attrValue) {
    auto &odsProp = getProperties().is_inverted;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetOperandOp::setIsAll(bool attrValue) {
    auto &odsProp = getProperties().is_all;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetOperandOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::mlir::DenseI64ArrayAttr raw_position_list, /*optional*/::mlir::UnitAttr is_inverted, /*optional*/::mlir::UnitAttr is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = raw_position_list;
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = is_inverted;
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = is_all;
  }
  odsState.addTypes(result);
}

void GetOperandOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::DenseI64ArrayAttr raw_position_list, /*optional*/::mlir::UnitAttr is_inverted, /*optional*/::mlir::UnitAttr is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = raw_position_list;
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = is_inverted;
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = is_all;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetOperandOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::llvm::ArrayRef<int64_t> raw_position_list, /*optional*/bool is_inverted, /*optional*/bool is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = odsBuilder.getDenseI64ArrayAttr(raw_position_list);
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = ((is_inverted) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = ((is_all) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes(result);
}

void GetOperandOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::llvm::ArrayRef<int64_t> raw_position_list, /*optional*/bool is_inverted, /*optional*/bool is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = odsBuilder.getDenseI64ArrayAttr(raw_position_list);
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = ((is_inverted) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = ((is_all) ? odsBuilder.getUnitAttr() : nullptr);
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetOperandOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GetOperandOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult GetOperandOp::verifyInvariantsImpl() {
  auto tblgen_is_all = getProperties().is_all; (void)tblgen_is_all;
  auto tblgen_is_inverted = getProperties().is_inverted; (void)tblgen_is_inverted;
  auto tblgen_raw_position_list = getProperties().raw_position_list; (void)tblgen_raw_position_list;
  if (!tblgen_raw_position_list) return emitOpError("requires attribute 'raw_position_list'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps7(*this, tblgen_raw_position_list, "raw_position_list")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_is_inverted, "is_inverted")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_is_all, "is_all")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps5(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetOperandOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult GetOperandOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::DenseI64ArrayAttr raw_position_listAttr;
  ::mlir::UnitAttr is_invertedAttr;
  ::mlir::UnitAttr is_allAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  if (parser.parseLSquare())
    return ::mlir::failure();
  {
    auto odsResult = parseTransformMatchDims(parser, raw_position_listAttr, is_invertedAttr, is_allAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<GetOperandOp::Properties>().raw_position_list = raw_position_listAttr;
    if (is_invertedAttr)
      result.getOrAddProperties<GetOperandOp::Properties>().is_inverted = is_invertedAttr;
    if (is_allAttr)
      result.getOrAddProperties<GetOperandOp::Properties>().is_all = is_allAttr;
  }
  if (parser.parseRSquare())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetOperandOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << "[";
  printTransformMatchDims(_odsPrinter, *this, getRawPositionListAttr(), getIsInvertedAttr(), getIsAllAttr());
  _odsPrinter << "]";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("raw_position_list");
  elidedAttrs.push_back("is_inverted");
  elidedAttrs.push_back("is_all");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getIsInvertedAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("is_inverted");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getIsAllAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("is_all");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetOperandOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetParentOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
GetParentOpGenericAdaptorBase::GetParentOpGenericAdaptorBase(GetParentOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::UnitAttr GetParentOpGenericAdaptorBase::getIsolatedFromAboveAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().isolated_from_above);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetParentOpGenericAdaptorBase::getIsolatedFromAbove() {
  auto attr = getIsolatedFromAboveAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::UnitAttr GetParentOpGenericAdaptorBase::getAllowEmptyResultsAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().allow_empty_results);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetParentOpGenericAdaptorBase::getAllowEmptyResults() {
  auto attr = getAllowEmptyResultsAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::std::optional< ::llvm::StringRef > GetParentOpGenericAdaptorBase::getOpName() {
  auto attr = getOpNameAttr();
  return attr ? ::std::optional< ::llvm::StringRef >(attr.getValue()) : (::std::nullopt);
}

::mlir::UnitAttr GetParentOpGenericAdaptorBase::getDeduplicateAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().deduplicate);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetParentOpGenericAdaptorBase::getDeduplicate() {
  auto attr = getDeduplicateAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::IntegerAttr GetParentOpGenericAdaptorBase::getNthParentAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::IntegerAttr>(getProperties().nth_parent);
  return attr;
}

uint64_t GetParentOpGenericAdaptorBase::getNthParent() {
  auto attr = getNthParentAttr();
  return attr.getValue().getZExtValue();
}

} // namespace detail
GetParentOpAdaptor::GetParentOpAdaptor(GetParentOp op) : GetParentOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetParentOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_allow_empty_results = getProperties().allow_empty_results; (void)tblgen_allow_empty_results;
  auto tblgen_deduplicate = getProperties().deduplicate; (void)tblgen_deduplicate;
  auto tblgen_isolated_from_above = getProperties().isolated_from_above; (void)tblgen_isolated_from_above;
  auto tblgen_nth_parent = getProperties().nth_parent; (void)tblgen_nth_parent;
  auto tblgen_op_name = getProperties().op_name; (void)tblgen_op_name;

  if (tblgen_isolated_from_above && !((::llvm::isa<::mlir::UnitAttr>(tblgen_isolated_from_above))))
    return emitError(loc, "'transform.get_parent_op' op ""attribute 'isolated_from_above' failed to satisfy constraint: unit attribute");

  if (tblgen_allow_empty_results && !((::llvm::isa<::mlir::UnitAttr>(tblgen_allow_empty_results))))
    return emitError(loc, "'transform.get_parent_op' op ""attribute 'allow_empty_results' failed to satisfy constraint: unit attribute");

  if (tblgen_op_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_op_name))))
    return emitError(loc, "'transform.get_parent_op' op ""attribute 'op_name' failed to satisfy constraint: string attribute");

  if (tblgen_deduplicate && !((::llvm::isa<::mlir::UnitAttr>(tblgen_deduplicate))))
    return emitError(loc, "'transform.get_parent_op' op ""attribute 'deduplicate' failed to satisfy constraint: unit attribute");

  if (tblgen_nth_parent && !((((::llvm::isa<::mlir::IntegerAttr>(tblgen_nth_parent))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_nth_parent).getType().isSignlessInteger(64)))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_nth_parent).getValue().isStrictlyPositive()))))
    return emitError(loc, "'transform.get_parent_op' op ""attribute 'nth_parent' failed to satisfy constraint: 64-bit signless integer attribute whose value is positive");
  return ::mlir::success();
}

::llvm::LogicalResult GetParentOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.allow_empty_results;
       auto attr = dict.get("allow_empty_results");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `allow_empty_results` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.deduplicate;
       auto attr = dict.get("deduplicate");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `deduplicate` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.isolated_from_above;
       auto attr = dict.get("isolated_from_above");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `isolated_from_above` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.nth_parent;
       auto attr = dict.get("nth_parent");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `nth_parent` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.op_name;
       auto attr = dict.get("op_name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `op_name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GetParentOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.allow_empty_results;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("allow_empty_results",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.deduplicate;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("deduplicate",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.isolated_from_above;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("isolated_from_above",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.nth_parent;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("nth_parent",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.op_name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("op_name",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GetParentOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.allow_empty_results.getAsOpaquePointer()), 
    llvm::hash_value(prop.deduplicate.getAsOpaquePointer()), 
    llvm::hash_value(prop.isolated_from_above.getAsOpaquePointer()), 
    llvm::hash_value(prop.nth_parent.getAsOpaquePointer()), 
    llvm::hash_value(prop.op_name.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GetParentOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "allow_empty_results")
      return prop.allow_empty_results;

    if (name == "deduplicate")
      return prop.deduplicate;

    if (name == "isolated_from_above")
      return prop.isolated_from_above;

    if (name == "nth_parent")
      return prop.nth_parent;

    if (name == "op_name")
      return prop.op_name;
  return std::nullopt;
}

void GetParentOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "allow_empty_results") {
       prop.allow_empty_results = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.allow_empty_results)>>(value);
       return;
    }

    if (name == "deduplicate") {
       prop.deduplicate = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.deduplicate)>>(value);
       return;
    }

    if (name == "isolated_from_above") {
       prop.isolated_from_above = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.isolated_from_above)>>(value);
       return;
    }

    if (name == "nth_parent") {
       prop.nth_parent = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.nth_parent)>>(value);
       return;
    }

    if (name == "op_name") {
       prop.op_name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.op_name)>>(value);
       return;
    }
}

void GetParentOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.allow_empty_results) attrs.append("allow_empty_results", prop.allow_empty_results);

    if (prop.deduplicate) attrs.append("deduplicate", prop.deduplicate);

    if (prop.isolated_from_above) attrs.append("isolated_from_above", prop.isolated_from_above);

    if (prop.nth_parent) attrs.append("nth_parent", prop.nth_parent);

    if (prop.op_name) attrs.append("op_name", prop.op_name);
}

::llvm::LogicalResult GetParentOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getAllowEmptyResultsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "allow_empty_results", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getDeduplicateAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "deduplicate", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getIsolatedFromAboveAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "isolated_from_above", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getNthParentAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps8(attr, "nth_parent", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getOpNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "op_name", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GetParentOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.allow_empty_results)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.deduplicate)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.isolated_from_above)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.nth_parent)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.op_name)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetParentOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.allow_empty_results);

  writer.writeOptionalAttribute(prop.deduplicate);

  writer.writeOptionalAttribute(prop.isolated_from_above);

  writer.writeOptionalAttribute(prop.nth_parent);

  writer.writeOptionalAttribute(prop.op_name);
}

bool GetParentOp::getIsolatedFromAbove() {
  auto attr = getIsolatedFromAboveAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

bool GetParentOp::getAllowEmptyResults() {
  auto attr = getAllowEmptyResultsAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::std::optional< ::llvm::StringRef > GetParentOp::getOpName() {
  auto attr = getOpNameAttr();
  return attr ? ::std::optional< ::llvm::StringRef >(attr.getValue()) : (::std::nullopt);
}

bool GetParentOp::getDeduplicate() {
  auto attr = getDeduplicateAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

uint64_t GetParentOp::getNthParent() {
  auto attr = getNthParentAttr();
  return attr.getValue().getZExtValue();
}

void GetParentOp::setIsolatedFromAbove(bool attrValue) {
    auto &odsProp = getProperties().isolated_from_above;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetParentOp::setAllowEmptyResults(bool attrValue) {
    auto &odsProp = getProperties().allow_empty_results;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetParentOp::setOpName(::std::optional<::llvm::StringRef> attrValue) {
    auto &odsProp = getProperties().op_name;
    if (attrValue)
      odsProp = ::mlir::Builder((*this)->getContext()).getStringAttr(*attrValue);
    else
      odsProp = nullptr;
}

void GetParentOp::setDeduplicate(bool attrValue) {
    auto &odsProp = getProperties().deduplicate;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetParentOp::setNthParent(uint64_t attrValue) {
  getProperties().nth_parent = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), attrValue);
}

void GetParentOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type parent, ::mlir::Value target, /*optional*/::mlir::UnitAttr isolated_from_above, /*optional*/::mlir::UnitAttr allow_empty_results, /*optional*/::mlir::StringAttr op_name, /*optional*/::mlir::UnitAttr deduplicate, ::mlir::IntegerAttr nth_parent) {
  odsState.addOperands(target);
  if (isolated_from_above) {
    odsState.getOrAddProperties<Properties>().isolated_from_above = isolated_from_above;
  }
  if (allow_empty_results) {
    odsState.getOrAddProperties<Properties>().allow_empty_results = allow_empty_results;
  }
  if (op_name) {
    odsState.getOrAddProperties<Properties>().op_name = op_name;
  }
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = deduplicate;
  }
  if (nth_parent) {
    odsState.getOrAddProperties<Properties>().nth_parent = nth_parent;
  }
  odsState.addTypes(parent);
}

void GetParentOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, /*optional*/::mlir::UnitAttr isolated_from_above, /*optional*/::mlir::UnitAttr allow_empty_results, /*optional*/::mlir::StringAttr op_name, /*optional*/::mlir::UnitAttr deduplicate, ::mlir::IntegerAttr nth_parent) {
  odsState.addOperands(target);
  if (isolated_from_above) {
    odsState.getOrAddProperties<Properties>().isolated_from_above = isolated_from_above;
  }
  if (allow_empty_results) {
    odsState.getOrAddProperties<Properties>().allow_empty_results = allow_empty_results;
  }
  if (op_name) {
    odsState.getOrAddProperties<Properties>().op_name = op_name;
  }
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = deduplicate;
  }
  if (nth_parent) {
    odsState.getOrAddProperties<Properties>().nth_parent = nth_parent;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetParentOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type parent, ::mlir::Value target, /*optional*/bool isolated_from_above, /*optional*/bool allow_empty_results, /*optional*/::mlir::StringAttr op_name, /*optional*/bool deduplicate, uint64_t nth_parent) {
  odsState.addOperands(target);
  if (isolated_from_above) {
    odsState.getOrAddProperties<Properties>().isolated_from_above = ((isolated_from_above) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (allow_empty_results) {
    odsState.getOrAddProperties<Properties>().allow_empty_results = ((allow_empty_results) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (op_name) {
    odsState.getOrAddProperties<Properties>().op_name = op_name;
  }
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = ((deduplicate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.getOrAddProperties<Properties>().nth_parent = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), nth_parent);
  odsState.addTypes(parent);
}

void GetParentOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, /*optional*/bool isolated_from_above, /*optional*/bool allow_empty_results, /*optional*/::mlir::StringAttr op_name, /*optional*/bool deduplicate, uint64_t nth_parent) {
  odsState.addOperands(target);
  if (isolated_from_above) {
    odsState.getOrAddProperties<Properties>().isolated_from_above = ((isolated_from_above) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (allow_empty_results) {
    odsState.getOrAddProperties<Properties>().allow_empty_results = ((allow_empty_results) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (op_name) {
    odsState.getOrAddProperties<Properties>().op_name = op_name;
  }
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = ((deduplicate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.getOrAddProperties<Properties>().nth_parent = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), nth_parent);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetParentOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GetParentOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void GetParentOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.nth_parent)
    properties.nth_parent = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), 1);
}

::llvm::LogicalResult GetParentOp::verifyInvariantsImpl() {
  auto tblgen_allow_empty_results = getProperties().allow_empty_results; (void)tblgen_allow_empty_results;
  auto tblgen_deduplicate = getProperties().deduplicate; (void)tblgen_deduplicate;
  auto tblgen_isolated_from_above = getProperties().isolated_from_above; (void)tblgen_isolated_from_above;
  auto tblgen_nth_parent = getProperties().nth_parent; (void)tblgen_nth_parent;
  auto tblgen_op_name = getProperties().op_name; (void)tblgen_op_name;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_isolated_from_above, "isolated_from_above")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_allow_empty_results, "allow_empty_results")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_op_name, "op_name")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_deduplicate, "deduplicate")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps8(*this, tblgen_nth_parent, "nth_parent")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetParentOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult GetParentOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetParentOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getIsolatedFromAboveAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("isolated_from_above");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getAllowEmptyResultsAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("allow_empty_results");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getDeduplicateAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("deduplicate");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getNthParentAttr();
     if(attr && (attr == odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), 1)))
       elidedAttrs.push_back("nth_parent");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetParentOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetProducerOfOperand definitions
//===----------------------------------------------------------------------===//

namespace detail {
GetProducerOfOperandGenericAdaptorBase::GetProducerOfOperandGenericAdaptorBase(GetProducerOfOperand op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

uint64_t GetProducerOfOperandGenericAdaptorBase::getOperandNumber() {
  auto attr = getOperandNumberAttr();
  return attr.getValue().getZExtValue();
}

} // namespace detail
GetProducerOfOperandAdaptor::GetProducerOfOperandAdaptor(GetProducerOfOperand op) : GetProducerOfOperandGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetProducerOfOperandAdaptor::verify(::mlir::Location loc) {
  auto tblgen_operand_number = getProperties().operand_number; (void)tblgen_operand_number;
  if (!tblgen_operand_number) return emitError(loc, "'transform.get_producer_of_operand' op ""requires attribute 'operand_number'");

  if (tblgen_operand_number && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_operand_number))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_operand_number).getType().isSignlessInteger(64)))))
    return emitError(loc, "'transform.get_producer_of_operand' op ""attribute 'operand_number' failed to satisfy constraint: 64-bit signless integer attribute");
  return ::mlir::success();
}

::llvm::LogicalResult GetProducerOfOperand::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.operand_number;
       auto attr = dict.get("operand_number");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `operand_number` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GetProducerOfOperand::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.operand_number;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("operand_number",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GetProducerOfOperand::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.operand_number.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GetProducerOfOperand::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "operand_number")
      return prop.operand_number;
  return std::nullopt;
}

void GetProducerOfOperand::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "operand_number") {
       prop.operand_number = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.operand_number)>>(value);
       return;
    }
}

void GetProducerOfOperand::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.operand_number) attrs.append("operand_number", prop.operand_number);
}

::llvm::LogicalResult GetProducerOfOperand::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getOperandNumberAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(attr, "operand_number", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GetProducerOfOperand::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.operand_number)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetProducerOfOperand::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.operand_number);
}

uint64_t GetProducerOfOperand::getOperandNumber() {
  auto attr = getOperandNumberAttr();
  return attr.getValue().getZExtValue();
}

void GetProducerOfOperand::setOperandNumber(uint64_t attrValue) {
  getProperties().operand_number = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), attrValue);
}

void GetProducerOfOperand::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type producer, ::mlir::Value target, ::mlir::IntegerAttr operand_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().operand_number = operand_number;
  odsState.addTypes(producer);
}

void GetProducerOfOperand::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::IntegerAttr operand_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().operand_number = operand_number;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetProducerOfOperand::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type producer, ::mlir::Value target, uint64_t operand_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().operand_number = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), operand_number);
  odsState.addTypes(producer);
}

void GetProducerOfOperand::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, uint64_t operand_number) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().operand_number = odsBuilder.getIntegerAttr(odsBuilder.getIntegerType(64), operand_number);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetProducerOfOperand::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GetProducerOfOperand::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult GetProducerOfOperand::verifyInvariantsImpl() {
  auto tblgen_operand_number = getProperties().operand_number; (void)tblgen_operand_number;
  if (!tblgen_operand_number) return emitOpError("requires attribute 'operand_number'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(*this, tblgen_operand_number, "operand_number")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetProducerOfOperand::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult GetProducerOfOperand::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::IntegerAttr operand_numberAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  if (parser.parseLSquare())
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(operand_numberAttr, parser.getBuilder().getIntegerType(64))) {
    return ::mlir::failure();
  }
  if (operand_numberAttr) result.getOrAddProperties<GetProducerOfOperand::Properties>().operand_number = operand_numberAttr;
  if (parser.parseRSquare())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetProducerOfOperand::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << "[";
  _odsPrinter.printAttributeWithoutType(getOperandNumberAttr());
  _odsPrinter << "]";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("operand_number");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetProducerOfOperand)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetResultOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
GetResultOpGenericAdaptorBase::GetResultOpGenericAdaptorBase(GetResultOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::ArrayRef<int64_t> GetResultOpGenericAdaptorBase::getRawPositionList() {
  auto attr = getRawPositionListAttr();
  return attr;
}

::mlir::UnitAttr GetResultOpGenericAdaptorBase::getIsInvertedAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().is_inverted);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetResultOpGenericAdaptorBase::getIsInverted() {
  auto attr = getIsInvertedAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

::mlir::UnitAttr GetResultOpGenericAdaptorBase::getIsAllAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().is_all);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetResultOpGenericAdaptorBase::getIsAll() {
  auto attr = getIsAllAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
GetResultOpAdaptor::GetResultOpAdaptor(GetResultOp op) : GetResultOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetResultOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_is_all = getProperties().is_all; (void)tblgen_is_all;
  auto tblgen_is_inverted = getProperties().is_inverted; (void)tblgen_is_inverted;
  auto tblgen_raw_position_list = getProperties().raw_position_list; (void)tblgen_raw_position_list;
  if (!tblgen_raw_position_list) return emitError(loc, "'transform.get_result' op ""requires attribute 'raw_position_list'");

  if (tblgen_raw_position_list && !((::llvm::isa<::mlir::DenseI64ArrayAttr>(tblgen_raw_position_list))))
    return emitError(loc, "'transform.get_result' op ""attribute 'raw_position_list' failed to satisfy constraint: i64 dense array attribute");

  if (tblgen_is_inverted && !((::llvm::isa<::mlir::UnitAttr>(tblgen_is_inverted))))
    return emitError(loc, "'transform.get_result' op ""attribute 'is_inverted' failed to satisfy constraint: unit attribute");

  if (tblgen_is_all && !((::llvm::isa<::mlir::UnitAttr>(tblgen_is_all))))
    return emitError(loc, "'transform.get_result' op ""attribute 'is_all' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

::llvm::LogicalResult GetResultOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.is_all;
       auto attr = dict.get("is_all");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `is_all` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.is_inverted;
       auto attr = dict.get("is_inverted");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `is_inverted` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.raw_position_list;
       auto attr = dict.get("raw_position_list");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `raw_position_list` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GetResultOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.is_all;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("is_all",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.is_inverted;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("is_inverted",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.raw_position_list;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("raw_position_list",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GetResultOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.is_all.getAsOpaquePointer()), 
    llvm::hash_value(prop.is_inverted.getAsOpaquePointer()), 
    llvm::hash_value(prop.raw_position_list.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GetResultOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "is_all")
      return prop.is_all;

    if (name == "is_inverted")
      return prop.is_inverted;

    if (name == "raw_position_list")
      return prop.raw_position_list;
  return std::nullopt;
}

void GetResultOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "is_all") {
       prop.is_all = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.is_all)>>(value);
       return;
    }

    if (name == "is_inverted") {
       prop.is_inverted = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.is_inverted)>>(value);
       return;
    }

    if (name == "raw_position_list") {
       prop.raw_position_list = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.raw_position_list)>>(value);
       return;
    }
}

void GetResultOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.is_all) attrs.append("is_all", prop.is_all);

    if (prop.is_inverted) attrs.append("is_inverted", prop.is_inverted);

    if (prop.raw_position_list) attrs.append("raw_position_list", prop.raw_position_list);
}

::llvm::LogicalResult GetResultOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getIsAllAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "is_all", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getIsInvertedAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "is_inverted", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getRawPositionListAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps7(attr, "raw_position_list", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GetResultOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.is_all)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.is_inverted)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.raw_position_list)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetResultOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.is_all);

  writer.writeOptionalAttribute(prop.is_inverted);
  writer.writeAttribute(prop.raw_position_list);
}

::llvm::ArrayRef<int64_t> GetResultOp::getRawPositionList() {
  auto attr = getRawPositionListAttr();
  return attr;
}

bool GetResultOp::getIsInverted() {
  auto attr = getIsInvertedAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

bool GetResultOp::getIsAll() {
  auto attr = getIsAllAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void GetResultOp::setRawPositionList(::llvm::ArrayRef<int64_t> attrValue) {
  getProperties().raw_position_list = ::mlir::Builder((*this)->getContext()).getDenseI64ArrayAttr(attrValue);
}

void GetResultOp::setIsInverted(bool attrValue) {
    auto &odsProp = getProperties().is_inverted;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetResultOp::setIsAll(bool attrValue) {
    auto &odsProp = getProperties().is_all;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetResultOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::mlir::DenseI64ArrayAttr raw_position_list, /*optional*/::mlir::UnitAttr is_inverted, /*optional*/::mlir::UnitAttr is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = raw_position_list;
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = is_inverted;
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = is_all;
  }
  odsState.addTypes(result);
}

void GetResultOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::DenseI64ArrayAttr raw_position_list, /*optional*/::mlir::UnitAttr is_inverted, /*optional*/::mlir::UnitAttr is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = raw_position_list;
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = is_inverted;
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = is_all;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetResultOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::llvm::ArrayRef<int64_t> raw_position_list, /*optional*/bool is_inverted, /*optional*/bool is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = odsBuilder.getDenseI64ArrayAttr(raw_position_list);
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = ((is_inverted) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = ((is_all) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes(result);
}

void GetResultOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::llvm::ArrayRef<int64_t> raw_position_list, /*optional*/bool is_inverted, /*optional*/bool is_all) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().raw_position_list = odsBuilder.getDenseI64ArrayAttr(raw_position_list);
  if (is_inverted) {
    odsState.getOrAddProperties<Properties>().is_inverted = ((is_inverted) ? odsBuilder.getUnitAttr() : nullptr);
  }
  if (is_all) {
    odsState.getOrAddProperties<Properties>().is_all = ((is_all) ? odsBuilder.getUnitAttr() : nullptr);
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetResultOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GetResultOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult GetResultOp::verifyInvariantsImpl() {
  auto tblgen_is_all = getProperties().is_all; (void)tblgen_is_all;
  auto tblgen_is_inverted = getProperties().is_inverted; (void)tblgen_is_inverted;
  auto tblgen_raw_position_list = getProperties().raw_position_list; (void)tblgen_raw_position_list;
  if (!tblgen_raw_position_list) return emitOpError("requires attribute 'raw_position_list'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps7(*this, tblgen_raw_position_list, "raw_position_list")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_is_inverted, "is_inverted")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_is_all, "is_all")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps5(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetResultOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult GetResultOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::DenseI64ArrayAttr raw_position_listAttr;
  ::mlir::UnitAttr is_invertedAttr;
  ::mlir::UnitAttr is_allAttr;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  if (parser.parseLSquare())
    return ::mlir::failure();
  {
    auto odsResult = parseTransformMatchDims(parser, raw_position_listAttr, is_invertedAttr, is_allAttr);
    if (odsResult) return ::mlir::failure();
    result.getOrAddProperties<GetResultOp::Properties>().raw_position_list = raw_position_listAttr;
    if (is_invertedAttr)
      result.getOrAddProperties<GetResultOp::Properties>().is_inverted = is_invertedAttr;
    if (is_allAttr)
      result.getOrAddProperties<GetResultOp::Properties>().is_all = is_allAttr;
  }
  if (parser.parseRSquare())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetResultOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  _odsPrinter << "[";
  printTransformMatchDims(_odsPrinter, *this, getRawPositionListAttr(), getIsInvertedAttr(), getIsAllAttr());
  _odsPrinter << "]";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("raw_position_list");
  elidedAttrs.push_back("is_inverted");
  elidedAttrs.push_back("is_all");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getIsInvertedAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("is_inverted");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getIsAllAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("is_all");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetResultOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::GetTypeOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
GetTypeOpGenericAdaptorBase::GetTypeOpGenericAdaptorBase(GetTypeOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::UnitAttr GetTypeOpGenericAdaptorBase::getElementalAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().elemental);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool GetTypeOpGenericAdaptorBase::getElemental() {
  auto attr = getElementalAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
GetTypeOpAdaptor::GetTypeOpAdaptor(GetTypeOp op) : GetTypeOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult GetTypeOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_elemental = getProperties().elemental; (void)tblgen_elemental;

  if (tblgen_elemental && !((::llvm::isa<::mlir::UnitAttr>(tblgen_elemental))))
    return emitError(loc, "'transform.get_type' op ""attribute 'elemental' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

::llvm::LogicalResult GetTypeOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.elemental;
       auto attr = dict.get("elemental");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `elemental` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute GetTypeOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.elemental;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("elemental",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code GetTypeOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.elemental.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> GetTypeOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "elemental")
      return prop.elemental;
  return std::nullopt;
}

void GetTypeOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "elemental") {
       prop.elemental = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.elemental)>>(value);
       return;
    }
}

void GetTypeOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.elemental) attrs.append("elemental", prop.elemental);
}

::llvm::LogicalResult GetTypeOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getElementalAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "elemental", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult GetTypeOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.elemental)))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetTypeOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.elemental);
}

bool GetTypeOp::getElemental() {
  auto attr = getElementalAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void GetTypeOp::setElemental(bool attrValue) {
    auto &odsProp = getProperties().elemental;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void GetTypeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type type_param, ::mlir::Value value, /*optional*/::mlir::UnitAttr elemental) {
  odsState.addOperands(value);
  if (elemental) {
    odsState.getOrAddProperties<Properties>().elemental = elemental;
  }
  odsState.addTypes(type_param);
}

void GetTypeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value value, /*optional*/::mlir::UnitAttr elemental) {
  odsState.addOperands(value);
  if (elemental) {
    odsState.getOrAddProperties<Properties>().elemental = elemental;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetTypeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type type_param, ::mlir::Value value, /*optional*/bool elemental) {
  odsState.addOperands(value);
  if (elemental) {
    odsState.getOrAddProperties<Properties>().elemental = ((elemental) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes(type_param);
}

void GetTypeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value value, /*optional*/bool elemental) {
  odsState.addOperands(value);
  if (elemental) {
    odsState.getOrAddProperties<Properties>().elemental = ((elemental) ? odsBuilder.getUnitAttr() : nullptr);
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void GetTypeOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<GetTypeOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult GetTypeOp::verifyInvariantsImpl() {
  auto tblgen_elemental = getProperties().elemental; (void)tblgen_elemental;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_elemental, "elemental")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps5(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps3(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult GetTypeOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult GetTypeOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand valueRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> valueOperands(&valueRawOperand, 1);  ::llvm::SMLoc valueOperandsLoc;
  (void)valueOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;
  if (::mlir::succeeded(parser.parseOptionalKeyword("elemental"))) {
    result.getOrAddProperties<GetTypeOp::Properties>().elemental = parser.getBuilder().getUnitAttr();  }

  valueOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(valueRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(valueOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void GetTypeOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  if ((getElementalAttr() && getElementalAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr))) {
    _odsPrinter << ' ' << "elemental";
  }
  _odsPrinter << ' ';
  _odsPrinter << getValue();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("elemental");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getElementalAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("elemental");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::GetTypeOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::IncludeOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
IncludeOpGenericAdaptorBase::IncludeOpGenericAdaptorBase(IncludeOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> IncludeOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::SymbolRefAttr IncludeOpGenericAdaptorBase::getTarget() {
  auto attr = getTargetAttr();
  return attr;
}

::mlir::transform::FailurePropagationMode IncludeOpGenericAdaptorBase::getFailurePropagationMode() {
  auto attr = getFailurePropagationModeAttr();
  return attr.getValue();
}

::std::optional< ::mlir::ArrayAttr > IncludeOpGenericAdaptorBase::getArgAttrs() {
  auto attr = getArgAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > IncludeOpGenericAdaptorBase::getResAttrs() {
  auto attr = getResAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

} // namespace detail
IncludeOpAdaptor::IncludeOpAdaptor(IncludeOp op) : IncludeOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult IncludeOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_arg_attrs = getProperties().arg_attrs; (void)tblgen_arg_attrs;
  auto tblgen_failure_propagation_mode = getProperties().failure_propagation_mode; (void)tblgen_failure_propagation_mode;
  if (!tblgen_failure_propagation_mode) return emitError(loc, "'transform.include' op ""requires attribute 'failure_propagation_mode'");
  auto tblgen_res_attrs = getProperties().res_attrs; (void)tblgen_res_attrs;
  auto tblgen_target = getProperties().target; (void)tblgen_target;
  if (!tblgen_target) return emitError(loc, "'transform.include' op ""requires attribute 'target'");

  if (tblgen_target && !((::llvm::isa<::mlir::SymbolRefAttr>(tblgen_target))))
    return emitError(loc, "'transform.include' op ""attribute 'target' failed to satisfy constraint: symbol reference attribute");

  if (tblgen_failure_propagation_mode && !((::llvm::isa<::mlir::transform::FailurePropagationModeAttr>(tblgen_failure_propagation_mode))))
    return emitError(loc, "'transform.include' op ""attribute 'failure_propagation_mode' failed to satisfy constraint: Silenceable error propagation policy");

  if (tblgen_arg_attrs && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_arg_attrs))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_arg_attrs), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::DictionaryAttr>(attr))); }))))
    return emitError(loc, "'transform.include' op ""attribute 'arg_attrs' failed to satisfy constraint: Array of dictionary attributes");

  if (tblgen_res_attrs && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_res_attrs))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_res_attrs), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::DictionaryAttr>(attr))); }))))
    return emitError(loc, "'transform.include' op ""attribute 'res_attrs' failed to satisfy constraint: Array of dictionary attributes");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> IncludeOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange IncludeOp::getOperandsMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

std::pair<unsigned, unsigned> IncludeOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult IncludeOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.arg_attrs;
       auto attr = dict.get("arg_attrs");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `arg_attrs` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.failure_propagation_mode;
       auto attr = dict.get("failure_propagation_mode");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `failure_propagation_mode` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.res_attrs;
       auto attr = dict.get("res_attrs");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `res_attrs` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.target;
       auto attr = dict.get("target");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `target` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute IncludeOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.arg_attrs;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("arg_attrs",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.failure_propagation_mode;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("failure_propagation_mode",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.res_attrs;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("res_attrs",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.target;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("target",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code IncludeOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.arg_attrs.getAsOpaquePointer()), 
    llvm::hash_value(prop.failure_propagation_mode.getAsOpaquePointer()), 
    llvm::hash_value(prop.res_attrs.getAsOpaquePointer()), 
    llvm::hash_value(prop.target.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> IncludeOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "arg_attrs")
      return prop.arg_attrs;

    if (name == "failure_propagation_mode")
      return prop.failure_propagation_mode;

    if (name == "res_attrs")
      return prop.res_attrs;

    if (name == "target")
      return prop.target;
  return std::nullopt;
}

void IncludeOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "arg_attrs") {
       prop.arg_attrs = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.arg_attrs)>>(value);
       return;
    }

    if (name == "failure_propagation_mode") {
       prop.failure_propagation_mode = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.failure_propagation_mode)>>(value);
       return;
    }

    if (name == "res_attrs") {
       prop.res_attrs = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.res_attrs)>>(value);
       return;
    }

    if (name == "target") {
       prop.target = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.target)>>(value);
       return;
    }
}

void IncludeOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.arg_attrs) attrs.append("arg_attrs", prop.arg_attrs);

    if (prop.failure_propagation_mode) attrs.append("failure_propagation_mode", prop.failure_propagation_mode);

    if (prop.res_attrs) attrs.append("res_attrs", prop.res_attrs);

    if (prop.target) attrs.append("target", prop.target);
}

::llvm::LogicalResult IncludeOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getArgAttrsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(attr, "arg_attrs", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getFailurePropagationModeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps9(attr, "failure_propagation_mode", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getResAttrsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(attr, "res_attrs", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getTargetAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps5(attr, "target", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult IncludeOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.arg_attrs)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.failure_propagation_mode)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.res_attrs)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.target)))
    return ::mlir::failure();
  return ::mlir::success();
}

void IncludeOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.arg_attrs);
  writer.writeAttribute(prop.failure_propagation_mode);

  writer.writeOptionalAttribute(prop.res_attrs);
  writer.writeAttribute(prop.target);
}

::mlir::SymbolRefAttr IncludeOp::getTarget() {
  auto attr = getTargetAttr();
  return attr;
}

::mlir::transform::FailurePropagationMode IncludeOp::getFailurePropagationMode() {
  auto attr = getFailurePropagationModeAttr();
  return attr.getValue();
}

::std::optional< ::mlir::ArrayAttr > IncludeOp::getArgAttrs() {
  auto attr = getArgAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > IncludeOp::getResAttrs() {
  auto attr = getResAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

void IncludeOp::setFailurePropagationMode(::mlir::transform::FailurePropagationMode attrValue) {
  getProperties().failure_propagation_mode = ::mlir::transform::FailurePropagationModeAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void IncludeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::SymbolRefAttr target, ::mlir::transform::FailurePropagationModeAttr failure_propagation_mode, ::mlir::ValueRange operands, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs) {
  odsState.addOperands(operands);
  odsState.getOrAddProperties<Properties>().target = target;
  odsState.getOrAddProperties<Properties>().failure_propagation_mode = failure_propagation_mode;
  if (arg_attrs) {
    odsState.getOrAddProperties<Properties>().arg_attrs = arg_attrs;
  }
  if (res_attrs) {
    odsState.getOrAddProperties<Properties>().res_attrs = res_attrs;
  }
  odsState.addTypes(results);
}

void IncludeOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::SymbolRefAttr target, ::mlir::transform::FailurePropagationMode failure_propagation_mode, ::mlir::ValueRange operands, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs) {
  odsState.addOperands(operands);
  odsState.getOrAddProperties<Properties>().target = target;
  odsState.getOrAddProperties<Properties>().failure_propagation_mode = ::mlir::transform::FailurePropagationModeAttr::get(odsBuilder.getContext(), failure_propagation_mode);
  if (arg_attrs) {
    odsState.getOrAddProperties<Properties>().arg_attrs = arg_attrs;
  }
  if (res_attrs) {
    odsState.getOrAddProperties<Properties>().res_attrs = res_attrs;
  }
  odsState.addTypes(results);
}

void IncludeOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<IncludeOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult IncludeOp::verifyInvariantsImpl() {
  auto tblgen_arg_attrs = getProperties().arg_attrs; (void)tblgen_arg_attrs;
  auto tblgen_failure_propagation_mode = getProperties().failure_propagation_mode; (void)tblgen_failure_propagation_mode;
  if (!tblgen_failure_propagation_mode) return emitOpError("requires attribute 'failure_propagation_mode'");
  auto tblgen_res_attrs = getProperties().res_attrs; (void)tblgen_res_attrs;
  auto tblgen_target = getProperties().target; (void)tblgen_target;
  if (!tblgen_target) return emitOpError("requires attribute 'target'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps5(*this, tblgen_target, "target")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps9(*this, tblgen_failure_propagation_mode, "failure_propagation_mode")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(*this, tblgen_arg_attrs, "arg_attrs")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(*this, tblgen_res_attrs, "res_attrs")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult IncludeOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult IncludeOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::SymbolRefAttr targetAttr;
  ::mlir::transform::FailurePropagationModeAttr failure_propagation_modeAttr;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> operandsOperands;
  ::llvm::SMLoc operandsOperandsLoc;
  (void)operandsOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> operandsTypes;
  ::llvm::ArrayRef<::mlir::Type> resultsTypes;

  if (parser.parseCustomAttributeWithFallback(targetAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (targetAttr) result.getOrAddProperties<IncludeOp::Properties>().target = targetAttr;
  if (parser.parseKeyword("failures"))
    return ::mlir::failure();
  if (parser.parseLParen())
    return ::mlir::failure();

  {
    ::llvm::StringRef attrStr;
    ::mlir::NamedAttrList attrStorage;
    auto loc = parser.getCurrentLocation();
    if (parser.parseOptionalKeyword(&attrStr, {"propagate","suppress"})) {
      ::mlir::StringAttr attrVal;
      ::mlir::OptionalParseResult parseResult =
        parser.parseOptionalAttribute(attrVal,
                                      parser.getBuilder().getNoneType(),
                                      "failure_propagation_mode", attrStorage);
      if (parseResult.has_value()) {
        if (failed(*parseResult))
          return ::mlir::failure();
        attrStr = attrVal.getValue();
      } else {
        return parser.emitError(loc, "expected string or keyword containing one of the following enum values for attribute 'failure_propagation_mode' [propagate, suppress]");
      }
    }
    if (!attrStr.empty()) {
      auto attrOptional = ::mlir::transform::symbolizeFailurePropagationMode(attrStr);
      if (!attrOptional)
        return parser.emitError(loc, "invalid ")
               << "failure_propagation_mode attribute specification: \"" << attrStr << '"';;

      failure_propagation_modeAttr = ::mlir::transform::FailurePropagationModeAttr::get(parser.getBuilder().getContext(), *attrOptional);
        result.getOrAddProperties<IncludeOp::Properties>().failure_propagation_mode = failure_propagation_modeAttr;
    }
  }
  if (parser.parseRParen())
    return ::mlir::failure();
  if (parser.parseLParen())
    return ::mlir::failure();

  operandsOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(operandsOperands))
    return ::mlir::failure();
  if (parser.parseRParen())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType operands__results_functionType;
  if (parser.parseType(operands__results_functionType))
    return ::mlir::failure();
  operandsTypes = operands__results_functionType.getInputs();
  resultsTypes = operands__results_functionType.getResults();
  result.addTypes(resultsTypes);
  if (parser.resolveOperands(operandsOperands, operandsTypes, operandsOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void IncludeOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getTargetAttr());
  _odsPrinter << ' ' << "failures";
  _odsPrinter << "(";

  {
    auto caseValue = getFailurePropagationMode();
    auto caseValueStr = stringifyFailurePropagationMode(caseValue);
    _odsPrinter << caseValueStr;
  }
  _odsPrinter << ")";
  _odsPrinter << ' ' << "(";
  _odsPrinter << getOperands();
  _odsPrinter << ")";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("target");
  elidedAttrs.push_back("failure_propagation_mode");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperands().getTypes(), getResults().getTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::IncludeOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::MatchOperationEmptyOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
MatchOperationEmptyOpAdaptor::MatchOperationEmptyOpAdaptor(MatchOperationEmptyOp op) : MatchOperationEmptyOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult MatchOperationEmptyOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void MatchOperationEmptyOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value operand_handle) {
  odsState.addOperands(operand_handle);
}

void MatchOperationEmptyOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value operand_handle) {
  odsState.addOperands(operand_handle);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MatchOperationEmptyOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult MatchOperationEmptyOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult MatchOperationEmptyOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult MatchOperationEmptyOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand operand_handleRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> operand_handleOperands(&operand_handleRawOperand, 1);  ::llvm::SMLoc operand_handleOperandsLoc;
  (void)operand_handleOperandsLoc;
  ::mlir::Type operand_handleRawType{};
  ::llvm::ArrayRef<::mlir::Type> operand_handleTypes(&operand_handleRawType, 1);

  operand_handleOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(operand_handleRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    operand_handleRawType = type;
  }
  if (parser.resolveOperands(operand_handleOperands, operand_handleTypes, operand_handleOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void MatchOperationEmptyOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getOperandHandle();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getOperandHandle().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::MatchOperationEmptyOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::MatchOperationNameOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
MatchOperationNameOpGenericAdaptorBase::MatchOperationNameOpGenericAdaptorBase(MatchOperationNameOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::ArrayAttr MatchOperationNameOpGenericAdaptorBase::getOpNames() {
  auto attr = getOpNamesAttr();
  return attr;
}

} // namespace detail
MatchOperationNameOpAdaptor::MatchOperationNameOpAdaptor(MatchOperationNameOp op) : MatchOperationNameOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult MatchOperationNameOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_op_names = getProperties().op_names; (void)tblgen_op_names;
  if (!tblgen_op_names) return emitError(loc, "'transform.match.operation_name' op ""requires attribute 'op_names'");

  if (tblgen_op_names && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_op_names))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_op_names), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::StringAttr>(attr))); }))))
    return emitError(loc, "'transform.match.operation_name' op ""attribute 'op_names' failed to satisfy constraint: string array attribute");
  return ::mlir::success();
}

::llvm::LogicalResult MatchOperationNameOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.op_names;
       auto attr = dict.get("op_names");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `op_names` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute MatchOperationNameOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.op_names;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("op_names",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code MatchOperationNameOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.op_names.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> MatchOperationNameOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "op_names")
      return prop.op_names;
  return std::nullopt;
}

void MatchOperationNameOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "op_names") {
       prop.op_names = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.op_names)>>(value);
       return;
    }
}

void MatchOperationNameOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.op_names) attrs.append("op_names", prop.op_names);
}

::llvm::LogicalResult MatchOperationNameOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getOpNamesAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(attr, "op_names", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult MatchOperationNameOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.op_names)))
    return ::mlir::failure();
  return ::mlir::success();
}

void MatchOperationNameOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.op_names);
}

::mlir::ArrayAttr MatchOperationNameOp::getOpNames() {
  auto attr = getOpNamesAttr();
  return attr;
}

void MatchOperationNameOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value operand_handle, ::mlir::ArrayAttr op_names) {
  odsState.addOperands(operand_handle);
  odsState.getOrAddProperties<Properties>().op_names = op_names;
}

void MatchOperationNameOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value operand_handle, ::mlir::ArrayAttr op_names) {
  odsState.addOperands(operand_handle);
  odsState.getOrAddProperties<Properties>().op_names = op_names;
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MatchOperationNameOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<MatchOperationNameOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult MatchOperationNameOp::verifyInvariantsImpl() {
  auto tblgen_op_names = getProperties().op_names; (void)tblgen_op_names;
  if (!tblgen_op_names) return emitOpError("requires attribute 'op_names'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps2(*this, tblgen_op_names, "op_names")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult MatchOperationNameOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult MatchOperationNameOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand operand_handleRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> operand_handleOperands(&operand_handleRawOperand, 1);  ::llvm::SMLoc operand_handleOperandsLoc;
  (void)operand_handleOperandsLoc;
  ::mlir::ArrayAttr op_namesAttr;
  ::mlir::Type operand_handleRawType{};
  ::llvm::ArrayRef<::mlir::Type> operand_handleTypes(&operand_handleRawType, 1);

  operand_handleOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(operand_handleRawOperand))
    return ::mlir::failure();

  if (parser.parseCustomAttributeWithFallback(op_namesAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (op_namesAttr) result.getOrAddProperties<MatchOperationNameOp::Properties>().op_names = op_namesAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    operand_handleRawType = type;
  }
  if (parser.resolveOperands(operand_handleOperands, operand_handleTypes, operand_handleOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void MatchOperationNameOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getOperandHandle();
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getOpNamesAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("op_names");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getOperandHandle().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::MatchOperationNameOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::MatchParamCmpIOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
MatchParamCmpIOpGenericAdaptorBase::MatchParamCmpIOpGenericAdaptorBase(MatchParamCmpIOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::transform::MatchCmpIPredicate MatchParamCmpIOpGenericAdaptorBase::getPredicate() {
  auto attr = getPredicateAttr();
  return attr.getValue();
}

} // namespace detail
MatchParamCmpIOpAdaptor::MatchParamCmpIOpAdaptor(MatchParamCmpIOp op) : MatchParamCmpIOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult MatchParamCmpIOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_predicate = getProperties().predicate; (void)tblgen_predicate;
  if (!tblgen_predicate) return emitError(loc, "'transform.match.param.cmpi' op ""requires attribute 'predicate'");

  if (tblgen_predicate && !((::llvm::isa<::mlir::transform::MatchCmpIPredicateAttr>(tblgen_predicate))))
    return emitError(loc, "'transform.match.param.cmpi' op ""attribute 'predicate' failed to satisfy constraint: allowed 32-bit signless integer cases: 0, 1, 2, 3, 4, 5");
  return ::mlir::success();
}

::llvm::LogicalResult MatchParamCmpIOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.predicate;
       auto attr = dict.get("predicate");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `predicate` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute MatchParamCmpIOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.predicate;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("predicate",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code MatchParamCmpIOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.predicate.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> MatchParamCmpIOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "predicate")
      return prop.predicate;
  return std::nullopt;
}

void MatchParamCmpIOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "predicate") {
       prop.predicate = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.predicate)>>(value);
       return;
    }
}

void MatchParamCmpIOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.predicate) attrs.append("predicate", prop.predicate);
}

::llvm::LogicalResult MatchParamCmpIOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getPredicateAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps11(attr, "predicate", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult MatchParamCmpIOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.predicate)))
    return ::mlir::failure();
  return ::mlir::success();
}

void MatchParamCmpIOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.predicate);
}

::mlir::transform::MatchCmpIPredicate MatchParamCmpIOp::getPredicate() {
  auto attr = getPredicateAttr();
  return attr.getValue();
}

void MatchParamCmpIOp::setPredicate(::mlir::transform::MatchCmpIPredicate attrValue) {
  getProperties().predicate = ::mlir::transform::MatchCmpIPredicateAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void MatchParamCmpIOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value param, ::mlir::Value reference, ::mlir::transform::MatchCmpIPredicateAttr predicate) {
  odsState.addOperands(param);
  odsState.addOperands(reference);
  odsState.getOrAddProperties<Properties>().predicate = predicate;
}

void MatchParamCmpIOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value param, ::mlir::Value reference, ::mlir::transform::MatchCmpIPredicateAttr predicate) {
  odsState.addOperands(param);
  odsState.addOperands(reference);
  odsState.getOrAddProperties<Properties>().predicate = predicate;
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MatchParamCmpIOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value param, ::mlir::Value reference, ::mlir::transform::MatchCmpIPredicate predicate) {
  odsState.addOperands(param);
  odsState.addOperands(reference);
  odsState.getOrAddProperties<Properties>().predicate = ::mlir::transform::MatchCmpIPredicateAttr::get(odsBuilder.getContext(), predicate);
}

void MatchParamCmpIOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value param, ::mlir::Value reference, ::mlir::transform::MatchCmpIPredicate predicate) {
  odsState.addOperands(param);
  odsState.addOperands(reference);
  odsState.getOrAddProperties<Properties>().predicate = ::mlir::transform::MatchCmpIPredicateAttr::get(odsBuilder.getContext(), predicate);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MatchParamCmpIOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 2u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<MatchParamCmpIOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult MatchParamCmpIOp::verifyInvariantsImpl() {
  auto tblgen_predicate = getProperties().predicate; (void)tblgen_predicate;
  if (!tblgen_predicate) return emitOpError("requires attribute 'predicate'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps11(*this, tblgen_predicate, "predicate")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps3(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult MatchParamCmpIOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult MatchParamCmpIOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::transform::MatchCmpIPredicateAttr predicateAttr;
  ::mlir::OpAsmParser::UnresolvedOperand paramRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> paramOperands(&paramRawOperand, 1);  ::llvm::SMLoc paramOperandsLoc;
  (void)paramOperandsLoc;
  ::mlir::OpAsmParser::UnresolvedOperand referenceRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> referenceOperands(&referenceRawOperand, 1);  ::llvm::SMLoc referenceOperandsLoc;
  (void)referenceOperandsLoc;
  ::mlir::Type paramRawType{};
  ::llvm::ArrayRef<::mlir::Type> paramTypes(&paramRawType, 1);

  {
    ::llvm::StringRef attrStr;
    ::mlir::NamedAttrList attrStorage;
    auto loc = parser.getCurrentLocation();
    if (parser.parseOptionalKeyword(&attrStr, {"eq","ne","lt","le","gt","ge"})) {
      ::mlir::StringAttr attrVal;
      ::mlir::OptionalParseResult parseResult =
        parser.parseOptionalAttribute(attrVal,
                                      parser.getBuilder().getNoneType(),
                                      "predicate", attrStorage);
      if (parseResult.has_value()) {
        if (failed(*parseResult))
          return ::mlir::failure();
        attrStr = attrVal.getValue();
      } else {
        return parser.emitError(loc, "expected string or keyword containing one of the following enum values for attribute 'predicate' [eq, ne, lt, le, gt, ge]");
      }
    }
    if (!attrStr.empty()) {
      auto attrOptional = ::mlir::transform::symbolizeMatchCmpIPredicate(attrStr);
      if (!attrOptional)
        return parser.emitError(loc, "invalid ")
               << "predicate attribute specification: \"" << attrStr << '"';;

      predicateAttr = ::mlir::transform::MatchCmpIPredicateAttr::get(parser.getBuilder().getContext(), *attrOptional);
        result.getOrAddProperties<MatchParamCmpIOp::Properties>().predicate = predicateAttr;
    }
  }

  paramOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(paramRawOperand))
    return ::mlir::failure();
  if (parser.parseComma())
    return ::mlir::failure();

  referenceOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(referenceRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformParamTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    paramRawType = type;
  }
  if (parser.resolveOperands(paramOperands, paramTypes, paramOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(referenceOperands, paramTypes[0], referenceOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void MatchParamCmpIOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';

  {
    auto caseValue = getPredicate();
    auto caseValueStr = stringifyMatchCmpIPredicate(caseValue);
    _odsPrinter << caseValueStr;
  }
  _odsPrinter << ' ';
  _odsPrinter << getParam();
  _odsPrinter << ",";
  _odsPrinter << ' ';
  _odsPrinter << getReference();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("predicate");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getParam().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformParamTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::MatchParamCmpIOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::MergeHandlesOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
MergeHandlesOpGenericAdaptorBase::MergeHandlesOpGenericAdaptorBase(MergeHandlesOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> MergeHandlesOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::UnitAttr MergeHandlesOpGenericAdaptorBase::getDeduplicateAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().deduplicate);
  if (!attr)
    attr = ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr);
  return attr;
}

bool MergeHandlesOpGenericAdaptorBase::getDeduplicate() {
  auto attr = getDeduplicateAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder(odsAttrs.getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

} // namespace detail
MergeHandlesOpAdaptor::MergeHandlesOpAdaptor(MergeHandlesOp op) : MergeHandlesOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult MergeHandlesOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_deduplicate = getProperties().deduplicate; (void)tblgen_deduplicate;

  if (tblgen_deduplicate && !((::llvm::isa<::mlir::UnitAttr>(tblgen_deduplicate))))
    return emitError(loc, "'transform.merge_handles' op ""attribute 'deduplicate' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> MergeHandlesOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange MergeHandlesOp::getHandlesMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult MergeHandlesOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.deduplicate;
       auto attr = dict.get("deduplicate");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `deduplicate` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute MergeHandlesOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.deduplicate;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("deduplicate",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code MergeHandlesOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.deduplicate.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> MergeHandlesOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "deduplicate")
      return prop.deduplicate;
  return std::nullopt;
}

void MergeHandlesOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "deduplicate") {
       prop.deduplicate = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.deduplicate)>>(value);
       return;
    }
}

void MergeHandlesOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.deduplicate) attrs.append("deduplicate", prop.deduplicate);
}

::llvm::LogicalResult MergeHandlesOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getDeduplicateAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "deduplicate", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult MergeHandlesOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.deduplicate)))
    return ::mlir::failure();
  return ::mlir::success();
}

void MergeHandlesOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.deduplicate);
}

bool MergeHandlesOp::getDeduplicate() {
  auto attr = getDeduplicateAttr();
    if (!attr)
      return ((false) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr) != nullptr;
  return attr != nullptr;
}

void MergeHandlesOp::setDeduplicate(bool attrValue) {
    auto &odsProp = getProperties().deduplicate;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::ValueRange handles, /*optional*/::mlir::UnitAttr deduplicate) {
  odsState.addOperands(handles);
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = deduplicate;
  }
  odsState.addTypes(result);
}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange handles, /*optional*/::mlir::UnitAttr deduplicate) {
  odsState.addOperands(handles);
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = deduplicate;
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::ValueRange handles, /*optional*/bool deduplicate) {
  odsState.addOperands(handles);
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = ((deduplicate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes(result);
}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange handles, /*optional*/bool deduplicate) {
  odsState.addOperands(handles);
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = ((deduplicate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void MergeHandlesOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<MergeHandlesOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange handles, /*optional*/::mlir::UnitAttr deduplicate) {
  odsState.addOperands(handles);
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = deduplicate;
  }
  odsState.addTypes({handles.front().getType()});

}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange handles, /*optional*/bool deduplicate) {
  odsState.addOperands(handles);
  if (deduplicate) {
    odsState.getOrAddProperties<Properties>().deduplicate = ((deduplicate) ? odsBuilder.getUnitAttr() : nullptr);
  }
  odsState.addTypes({handles.front().getType()});

}

void MergeHandlesOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes({operands[0].getType()});

}

::llvm::LogicalResult MergeHandlesOp::verifyInvariantsImpl() {
  auto tblgen_deduplicate = getProperties().deduplicate; (void)tblgen_deduplicate;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_deduplicate, "deduplicate")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps6(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult MergeHandlesOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult MergeHandlesOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> handlesOperands;
  ::llvm::SMLoc handlesOperandsLoc;
  (void)handlesOperandsLoc;
  ::mlir::Type resultRawType{};
  ::llvm::ArrayRef<::mlir::Type> resultTypes(&resultRawType, 1);
  if (::mlir::succeeded(parser.parseOptionalKeyword("deduplicate"))) {
    result.getOrAddProperties<MergeHandlesOp::Properties>().deduplicate = parser.getBuilder().getUnitAttr();  }

  handlesOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(handlesOperands))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::Type type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    resultRawType = type;
  }
  result.addTypes(resultTypes);
  if (parser.resolveOperands(handlesOperands, resultTypes[0], handlesOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void MergeHandlesOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  if ((getDeduplicateAttr() && getDeduplicateAttr() != ((false) ? ::mlir::OpBuilder((*this)->getContext()).getUnitAttr() : nullptr))) {
    _odsPrinter << ' ' << "deduplicate";
  }
  _odsPrinter << ' ';
  _odsPrinter << getHandles();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("deduplicate");
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getDeduplicateAttr();
     if(attr && (attr == ((false) ? odsBuilder.getUnitAttr() : nullptr)))
       elidedAttrs.push_back("deduplicate");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getResult().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::Type>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::MergeHandlesOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::NamedSequenceOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
NamedSequenceOpGenericAdaptorBase::NamedSequenceOpGenericAdaptorBase(NamedSequenceOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef NamedSequenceOpGenericAdaptorBase::getSymName() {
  auto attr = getSymNameAttr();
  return attr.getValue();
}

::mlir::FunctionType NamedSequenceOpGenericAdaptorBase::getFunctionType() {
  auto attr = getFunctionTypeAttr();
  return ::llvm::cast<::mlir::FunctionType>(attr.getValue());
}

::std::optional< ::llvm::StringRef > NamedSequenceOpGenericAdaptorBase::getSymVisibility() {
  auto attr = getSymVisibilityAttr();
  return attr ? ::std::optional< ::llvm::StringRef >(attr.getValue()) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > NamedSequenceOpGenericAdaptorBase::getArgAttrs() {
  auto attr = getArgAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > NamedSequenceOpGenericAdaptorBase::getResAttrs() {
  auto attr = getResAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

} // namespace detail
NamedSequenceOpAdaptor::NamedSequenceOpAdaptor(NamedSequenceOp op) : NamedSequenceOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult NamedSequenceOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_arg_attrs = getProperties().arg_attrs; (void)tblgen_arg_attrs;
  auto tblgen_function_type = getProperties().function_type; (void)tblgen_function_type;
  if (!tblgen_function_type) return emitError(loc, "'transform.named_sequence' op ""requires attribute 'function_type'");
  auto tblgen_res_attrs = getProperties().res_attrs; (void)tblgen_res_attrs;
  auto tblgen_sym_name = getProperties().sym_name; (void)tblgen_sym_name;
  if (!tblgen_sym_name) return emitError(loc, "'transform.named_sequence' op ""requires attribute 'sym_name'");
  auto tblgen_sym_visibility = getProperties().sym_visibility; (void)tblgen_sym_visibility;

  if (tblgen_sym_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_sym_name))))
    return emitError(loc, "'transform.named_sequence' op ""attribute 'sym_name' failed to satisfy constraint: string attribute");

  if (tblgen_function_type && !(((::llvm::isa<::mlir::TypeAttr>(tblgen_function_type))) && ((::llvm::isa<::mlir::FunctionType>(::llvm::cast<::mlir::TypeAttr>(tblgen_function_type).getValue()))) && ((true))))
    return emitError(loc, "'transform.named_sequence' op ""attribute 'function_type' failed to satisfy constraint: function type attribute");

  if (tblgen_sym_visibility && !((::llvm::isa<::mlir::StringAttr>(tblgen_sym_visibility))))
    return emitError(loc, "'transform.named_sequence' op ""attribute 'sym_visibility' failed to satisfy constraint: string attribute");

  if (tblgen_arg_attrs && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_arg_attrs))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_arg_attrs), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::DictionaryAttr>(attr))); }))))
    return emitError(loc, "'transform.named_sequence' op ""attribute 'arg_attrs' failed to satisfy constraint: Array of dictionary attributes");

  if (tblgen_res_attrs && !(((::llvm::isa<::mlir::ArrayAttr>(tblgen_res_attrs))) && (::llvm::all_of(::llvm::cast<::mlir::ArrayAttr>(tblgen_res_attrs), [&](::mlir::Attribute attr) { return attr && ((::llvm::isa<::mlir::DictionaryAttr>(attr))); }))))
    return emitError(loc, "'transform.named_sequence' op ""attribute 'res_attrs' failed to satisfy constraint: Array of dictionary attributes");
  return ::mlir::success();
}

::llvm::LogicalResult NamedSequenceOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.arg_attrs;
       auto attr = dict.get("arg_attrs");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `arg_attrs` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.function_type;
       auto attr = dict.get("function_type");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `function_type` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.res_attrs;
       auto attr = dict.get("res_attrs");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `res_attrs` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.sym_name;
       auto attr = dict.get("sym_name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `sym_name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.sym_visibility;
       auto attr = dict.get("sym_visibility");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `sym_visibility` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute NamedSequenceOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.arg_attrs;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("arg_attrs",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.function_type;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("function_type",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.res_attrs;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("res_attrs",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.sym_name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("sym_name",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.sym_visibility;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("sym_visibility",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code NamedSequenceOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.arg_attrs.getAsOpaquePointer()), 
    llvm::hash_value(prop.function_type.getAsOpaquePointer()), 
    llvm::hash_value(prop.res_attrs.getAsOpaquePointer()), 
    llvm::hash_value(prop.sym_name.getAsOpaquePointer()), 
    llvm::hash_value(prop.sym_visibility.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> NamedSequenceOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "arg_attrs")
      return prop.arg_attrs;

    if (name == "function_type")
      return prop.function_type;

    if (name == "res_attrs")
      return prop.res_attrs;

    if (name == "sym_name")
      return prop.sym_name;

    if (name == "sym_visibility")
      return prop.sym_visibility;
  return std::nullopt;
}

void NamedSequenceOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "arg_attrs") {
       prop.arg_attrs = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.arg_attrs)>>(value);
       return;
    }

    if (name == "function_type") {
       prop.function_type = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.function_type)>>(value);
       return;
    }

    if (name == "res_attrs") {
       prop.res_attrs = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.res_attrs)>>(value);
       return;
    }

    if (name == "sym_name") {
       prop.sym_name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.sym_name)>>(value);
       return;
    }

    if (name == "sym_visibility") {
       prop.sym_visibility = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.sym_visibility)>>(value);
       return;
    }
}

void NamedSequenceOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.arg_attrs) attrs.append("arg_attrs", prop.arg_attrs);

    if (prop.function_type) attrs.append("function_type", prop.function_type);

    if (prop.res_attrs) attrs.append("res_attrs", prop.res_attrs);

    if (prop.sym_name) attrs.append("sym_name", prop.sym_name);

    if (prop.sym_visibility) attrs.append("sym_visibility", prop.sym_visibility);
}

::llvm::LogicalResult NamedSequenceOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getArgAttrsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(attr, "arg_attrs", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getFunctionTypeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps12(attr, "function_type", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getResAttrsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(attr, "res_attrs", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSymNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "sym_name", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSymVisibilityAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "sym_visibility", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult NamedSequenceOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.arg_attrs)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.function_type)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.res_attrs)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readAttribute(prop.sym_name)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.sym_visibility)))
    return ::mlir::failure();
  return ::mlir::success();
}

void NamedSequenceOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.arg_attrs);
  writer.writeAttribute(prop.function_type);

  writer.writeOptionalAttribute(prop.res_attrs);
  writer.writeAttribute(prop.sym_name);

  writer.writeOptionalAttribute(prop.sym_visibility);
}

::llvm::StringRef NamedSequenceOp::getSymName() {
  auto attr = getSymNameAttr();
  return attr.getValue();
}

::mlir::FunctionType NamedSequenceOp::getFunctionType() {
  auto attr = getFunctionTypeAttr();
  return ::llvm::cast<::mlir::FunctionType>(attr.getValue());
}

::std::optional< ::llvm::StringRef > NamedSequenceOp::getSymVisibility() {
  auto attr = getSymVisibilityAttr();
  return attr ? ::std::optional< ::llvm::StringRef >(attr.getValue()) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > NamedSequenceOp::getArgAttrs() {
  auto attr = getArgAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

::std::optional< ::mlir::ArrayAttr > NamedSequenceOp::getResAttrs() {
  auto attr = getResAttrsAttr();
  return attr ? ::std::optional< ::mlir::ArrayAttr >(attr) : (::std::nullopt);
}

void NamedSequenceOp::setSymName(::llvm::StringRef attrValue) {
  getProperties().sym_name = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void NamedSequenceOp::setSymVisibility(::std::optional<::llvm::StringRef> attrValue) {
    auto &odsProp = getProperties().sym_visibility;
    if (attrValue)
      odsProp = ::mlir::Builder((*this)->getContext()).getStringAttr(*attrValue);
    else
      odsProp = nullptr;
}

void NamedSequenceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::StringAttr sym_name, ::mlir::TypeAttr function_type, /*optional*/::mlir::StringAttr sym_visibility, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs) {
  odsState.getOrAddProperties<Properties>().sym_name = sym_name;
  odsState.getOrAddProperties<Properties>().function_type = function_type;
  if (sym_visibility) {
    odsState.getOrAddProperties<Properties>().sym_visibility = sym_visibility;
  }
  if (arg_attrs) {
    odsState.getOrAddProperties<Properties>().arg_attrs = arg_attrs;
  }
  if (res_attrs) {
    odsState.getOrAddProperties<Properties>().res_attrs = res_attrs;
  }
  (void)odsState.addRegion();
}

void NamedSequenceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::StringAttr sym_name, ::mlir::TypeAttr function_type, /*optional*/::mlir::StringAttr sym_visibility, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs) {
  odsState.getOrAddProperties<Properties>().sym_name = sym_name;
  odsState.getOrAddProperties<Properties>().function_type = function_type;
  if (sym_visibility) {
    odsState.getOrAddProperties<Properties>().sym_visibility = sym_visibility;
  }
  if (arg_attrs) {
    odsState.getOrAddProperties<Properties>().arg_attrs = arg_attrs;
  }
  if (res_attrs) {
    odsState.getOrAddProperties<Properties>().res_attrs = res_attrs;
  }
  (void)odsState.addRegion();
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void NamedSequenceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::llvm::StringRef sym_name, ::mlir::TypeAttr function_type, /*optional*/::mlir::StringAttr sym_visibility, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs) {
  odsState.getOrAddProperties<Properties>().sym_name = odsBuilder.getStringAttr(sym_name);
  odsState.getOrAddProperties<Properties>().function_type = function_type;
  if (sym_visibility) {
    odsState.getOrAddProperties<Properties>().sym_visibility = sym_visibility;
  }
  if (arg_attrs) {
    odsState.getOrAddProperties<Properties>().arg_attrs = arg_attrs;
  }
  if (res_attrs) {
    odsState.getOrAddProperties<Properties>().res_attrs = res_attrs;
  }
  (void)odsState.addRegion();
}

void NamedSequenceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef sym_name, ::mlir::TypeAttr function_type, /*optional*/::mlir::StringAttr sym_visibility, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs) {
  odsState.getOrAddProperties<Properties>().sym_name = odsBuilder.getStringAttr(sym_name);
  odsState.getOrAddProperties<Properties>().function_type = function_type;
  if (sym_visibility) {
    odsState.getOrAddProperties<Properties>().sym_visibility = sym_visibility;
  }
  if (arg_attrs) {
    odsState.getOrAddProperties<Properties>().arg_attrs = arg_attrs;
  }
  if (res_attrs) {
    odsState.getOrAddProperties<Properties>().res_attrs = res_attrs;
  }
  (void)odsState.addRegion();
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void NamedSequenceOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  for (unsigned i = 0; i != 1; ++i)
    (void)odsState.addRegion();
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<NamedSequenceOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult NamedSequenceOp::verifyInvariantsImpl() {
  auto tblgen_arg_attrs = getProperties().arg_attrs; (void)tblgen_arg_attrs;
  auto tblgen_function_type = getProperties().function_type; (void)tblgen_function_type;
  if (!tblgen_function_type) return emitOpError("requires attribute 'function_type'");
  auto tblgen_res_attrs = getProperties().res_attrs; (void)tblgen_res_attrs;
  auto tblgen_sym_name = getProperties().sym_name; (void)tblgen_sym_name;
  if (!tblgen_sym_name) return emitOpError("requires attribute 'sym_name'");
  auto tblgen_sym_visibility = getProperties().sym_visibility; (void)tblgen_sym_visibility;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_sym_name, "sym_name")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps12(*this, tblgen_function_type, "function_type")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_sym_visibility, "sym_visibility")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(*this, tblgen_arg_attrs, "arg_attrs")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps10(*this, tblgen_res_attrs, "res_attrs")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;

    for (auto &region : ::llvm::MutableArrayRef((*this)->getRegion(0)))
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps2(*this, region, "body", index++)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::llvm::LogicalResult NamedSequenceOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::NamedSequenceOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::NumAssociationsOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
NumAssociationsOpAdaptor::NumAssociationsOpAdaptor(NumAssociationsOp op) : NumAssociationsOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult NumAssociationsOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void NumAssociationsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type num, ::mlir::Value handle) {
  odsState.addOperands(handle);
  odsState.addTypes(num);
}

void NumAssociationsOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value handle) {
  odsState.addOperands(handle);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void NumAssociationsOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult NumAssociationsOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps6(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps3(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult NumAssociationsOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult NumAssociationsOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand handleRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> handleOperands(&handleRawOperand, 1);  ::llvm::SMLoc handleOperandsLoc;
  (void)handleOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  handleOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(handleRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(handleOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void NumAssociationsOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getHandle();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::NumAssociationsOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ParamConstantOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
ParamConstantOpGenericAdaptorBase::ParamConstantOpGenericAdaptorBase(ParamConstantOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::Attribute ParamConstantOpGenericAdaptorBase::getValue() {
  auto attr = getValueAttr();
  return attr;
}

} // namespace detail
ParamConstantOpAdaptor::ParamConstantOpAdaptor(ParamConstantOp op) : ParamConstantOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ParamConstantOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_value = getProperties().value; (void)tblgen_value;
  if (!tblgen_value) return emitError(loc, "'transform.param.constant' op ""requires attribute 'value'");

  if (tblgen_value && !((true)))
    return emitError(loc, "'transform.param.constant' op ""attribute 'value' failed to satisfy constraint: any attribute");
  return ::mlir::success();
}

::llvm::LogicalResult ParamConstantOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.value;
       auto attr = dict.get("value");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `value` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute ParamConstantOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.value;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("value",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code ParamConstantOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.value.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> ParamConstantOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "value")
      return prop.value;
  return std::nullopt;
}

void ParamConstantOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "value") {
       prop.value = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.value)>>(value);
       return;
    }
}

void ParamConstantOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.value) attrs.append("value", prop.value);
}

::llvm::LogicalResult ParamConstantOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getValueAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps13(attr, "value", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult ParamConstantOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.value)))
    return ::mlir::failure();
  return ::mlir::success();
}

void ParamConstantOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.value);
}

::mlir::Attribute ParamConstantOp::getValue() {
  auto attr = getValueAttr();
  return attr;
}

void ParamConstantOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type param, ::mlir::Attribute value) {
  odsState.getOrAddProperties<Properties>().value = value;
  odsState.addTypes(param);
}

void ParamConstantOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Attribute value) {
  odsState.getOrAddProperties<Properties>().value = value;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void ParamConstantOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 0u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<ParamConstantOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult ParamConstantOp::verifyInvariantsImpl() {
  auto tblgen_value = getProperties().value; (void)tblgen_value;
  if (!tblgen_value) return emitOpError("requires attribute 'value'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps13(*this, tblgen_value, "value")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps3(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult ParamConstantOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ParamConstantOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::Attribute valueAttr;
  ::mlir::Type paramRawType{};
  ::llvm::ArrayRef<::mlir::Type> paramTypes(&paramRawType, 1);

  if (parser.parseAttribute(valueAttr, ::mlir::Type{}))
    return ::mlir::failure();
  if (valueAttr) result.getOrAddProperties<ParamConstantOp::Properties>().value = valueAttr;
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseArrow())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformParamTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    paramRawType = type;
  }
  result.addTypes(paramTypes);
  return ::mlir::success();
}

void ParamConstantOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttribute(getValueAttr());
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("value");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << "->";
  _odsPrinter << ' ';
  {
    auto type = getParam().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformParamTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ParamConstantOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::PrintOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
PrintOpGenericAdaptorBase::PrintOpGenericAdaptorBase(PrintOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> PrintOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::std::optional< ::llvm::StringRef > PrintOpGenericAdaptorBase::getName() {
  auto attr = getNameAttr();
  return attr ? ::std::optional< ::llvm::StringRef >(attr.getValue()) : (::std::nullopt);
}

::std::optional<bool> PrintOpGenericAdaptorBase::getAssumeVerified() {
  auto attr = getAssumeVerifiedAttr();
  return attr ? ::std::optional<bool>(attr != nullptr) : (::std::nullopt);
}

::std::optional<bool> PrintOpGenericAdaptorBase::getUseLocalScope() {
  auto attr = getUseLocalScopeAttr();
  return attr ? ::std::optional<bool>(attr != nullptr) : (::std::nullopt);
}

::std::optional<bool> PrintOpGenericAdaptorBase::getSkipRegions() {
  auto attr = getSkipRegionsAttr();
  return attr ? ::std::optional<bool>(attr != nullptr) : (::std::nullopt);
}

} // namespace detail
PrintOpAdaptor::PrintOpAdaptor(PrintOp op) : PrintOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult PrintOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_assume_verified = getProperties().assume_verified; (void)tblgen_assume_verified;
  auto tblgen_name = getProperties().name; (void)tblgen_name;
  auto tblgen_skip_regions = getProperties().skip_regions; (void)tblgen_skip_regions;
  auto tblgen_use_local_scope = getProperties().use_local_scope; (void)tblgen_use_local_scope;

  if (tblgen_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_name))))
    return emitError(loc, "'transform.print' op ""attribute 'name' failed to satisfy constraint: string attribute");

  if (tblgen_assume_verified && !((::llvm::isa<::mlir::UnitAttr>(tblgen_assume_verified))))
    return emitError(loc, "'transform.print' op ""attribute 'assume_verified' failed to satisfy constraint: unit attribute");

  if (tblgen_use_local_scope && !((::llvm::isa<::mlir::UnitAttr>(tblgen_use_local_scope))))
    return emitError(loc, "'transform.print' op ""attribute 'use_local_scope' failed to satisfy constraint: unit attribute");

  if (tblgen_skip_regions && !((::llvm::isa<::mlir::UnitAttr>(tblgen_skip_regions))))
    return emitError(loc, "'transform.print' op ""attribute 'skip_regions' failed to satisfy constraint: unit attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> PrintOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange PrintOp::getTargetMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

::llvm::LogicalResult PrintOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.assume_verified;
       auto attr = dict.get("assume_verified");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `assume_verified` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.name;
       auto attr = dict.get("name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.skip_regions;
       auto attr = dict.get("skip_regions");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `skip_regions` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.use_local_scope;
       auto attr = dict.get("use_local_scope");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `use_local_scope` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute PrintOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.assume_verified;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("assume_verified",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("name",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.skip_regions;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("skip_regions",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.use_local_scope;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("use_local_scope",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code PrintOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.assume_verified.getAsOpaquePointer()), 
    llvm::hash_value(prop.name.getAsOpaquePointer()), 
    llvm::hash_value(prop.skip_regions.getAsOpaquePointer()), 
    llvm::hash_value(prop.use_local_scope.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> PrintOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "assume_verified")
      return prop.assume_verified;

    if (name == "name")
      return prop.name;

    if (name == "skip_regions")
      return prop.skip_regions;

    if (name == "use_local_scope")
      return prop.use_local_scope;
  return std::nullopt;
}

void PrintOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "assume_verified") {
       prop.assume_verified = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.assume_verified)>>(value);
       return;
    }

    if (name == "name") {
       prop.name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.name)>>(value);
       return;
    }

    if (name == "skip_regions") {
       prop.skip_regions = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.skip_regions)>>(value);
       return;
    }

    if (name == "use_local_scope") {
       prop.use_local_scope = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.use_local_scope)>>(value);
       return;
    }
}

void PrintOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.assume_verified) attrs.append("assume_verified", prop.assume_verified);

    if (prop.name) attrs.append("name", prop.name);

    if (prop.skip_regions) attrs.append("skip_regions", prop.skip_regions);

    if (prop.use_local_scope) attrs.append("use_local_scope", prop.use_local_scope);
}

::llvm::LogicalResult PrintOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getAssumeVerifiedAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "assume_verified", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "name", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getSkipRegionsAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "skip_regions", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getUseLocalScopeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(attr, "use_local_scope", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult PrintOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.assume_verified)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.name)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.skip_regions)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.use_local_scope)))
    return ::mlir::failure();
  return ::mlir::success();
}

void PrintOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.assume_verified);

  writer.writeOptionalAttribute(prop.name);

  writer.writeOptionalAttribute(prop.skip_regions);

  writer.writeOptionalAttribute(prop.use_local_scope);
}

::std::optional< ::llvm::StringRef > PrintOp::getName() {
  auto attr = getNameAttr();
  return attr ? ::std::optional< ::llvm::StringRef >(attr.getValue()) : (::std::nullopt);
}

::std::optional<bool> PrintOp::getAssumeVerified() {
  auto attr = getAssumeVerifiedAttr();
  return attr ? ::std::optional<bool>(attr != nullptr) : (::std::nullopt);
}

::std::optional<bool> PrintOp::getUseLocalScope() {
  auto attr = getUseLocalScopeAttr();
  return attr ? ::std::optional<bool>(attr != nullptr) : (::std::nullopt);
}

::std::optional<bool> PrintOp::getSkipRegions() {
  auto attr = getSkipRegionsAttr();
  return attr ? ::std::optional<bool>(attr != nullptr) : (::std::nullopt);
}

void PrintOp::setName(::std::optional<::llvm::StringRef> attrValue) {
    auto &odsProp = getProperties().name;
    if (attrValue)
      odsProp = ::mlir::Builder((*this)->getContext()).getStringAttr(*attrValue);
    else
      odsProp = nullptr;
}

void PrintOp::setAssumeVerified(bool attrValue) {
    auto &odsProp = getProperties().assume_verified;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void PrintOp::setUseLocalScope(bool attrValue) {
    auto &odsProp = getProperties().use_local_scope;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void PrintOp::setSkipRegions(bool attrValue) {
    auto &odsProp = getProperties().skip_regions;
    if (attrValue)
      odsProp = ((attrValue) ? ::mlir::Builder((*this)->getContext()).getUnitAttr() : nullptr);
    else
      odsProp = nullptr;
}

void PrintOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, /*optional*/::mlir::Value target, /*optional*/::mlir::StringAttr name, /*optional*/::mlir::UnitAttr assume_verified, /*optional*/::mlir::UnitAttr use_local_scope, /*optional*/::mlir::UnitAttr skip_regions) {
  if (target)
    odsState.addOperands(target);
  if (name) {
    odsState.getOrAddProperties<Properties>().name = name;
  }
  if (assume_verified) {
    odsState.getOrAddProperties<Properties>().assume_verified = assume_verified;
  }
  if (use_local_scope) {
    odsState.getOrAddProperties<Properties>().use_local_scope = use_local_scope;
  }
  if (skip_regions) {
    odsState.getOrAddProperties<Properties>().skip_regions = skip_regions;
  }
}

void PrintOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, /*optional*/::mlir::Value target, /*optional*/::mlir::StringAttr name, /*optional*/::mlir::UnitAttr assume_verified, /*optional*/::mlir::UnitAttr use_local_scope, /*optional*/::mlir::UnitAttr skip_regions) {
  if (target)
    odsState.addOperands(target);
  if (name) {
    odsState.getOrAddProperties<Properties>().name = name;
  }
  if (assume_verified) {
    odsState.getOrAddProperties<Properties>().assume_verified = assume_verified;
  }
  if (use_local_scope) {
    odsState.getOrAddProperties<Properties>().use_local_scope = use_local_scope;
  }
  if (skip_regions) {
    odsState.getOrAddProperties<Properties>().skip_regions = skip_regions;
  }
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void PrintOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<PrintOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult PrintOp::verifyInvariantsImpl() {
  auto tblgen_assume_verified = getProperties().assume_verified; (void)tblgen_assume_verified;
  auto tblgen_name = getProperties().name; (void)tblgen_name;
  auto tblgen_skip_regions = getProperties().skip_regions; (void)tblgen_skip_regions;
  auto tblgen_use_local_scope = getProperties().use_local_scope; (void)tblgen_use_local_scope;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_name, "name")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_assume_verified, "assume_verified")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_use_local_scope, "use_local_scope")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps3(*this, tblgen_skip_regions, "skip_regions")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    if (valueGroup0.size() > 1) {
      return emitOpError("operand group starting at #") << index
          << " requires 0 or 1 element, but found " << valueGroup0.size();
    }

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult PrintOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult PrintOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> targetOperands;
  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::llvm::SmallVector<::mlir::Type, 1> targetTypes;

  {
    targetOperandsLoc = parser.getCurrentLocation();
    ::mlir::OpAsmParser::UnresolvedOperand operand;
    ::mlir::OptionalParseResult parseResult =
                                    parser.parseOptionalOperand(operand);
    if (parseResult.has_value()) {
      if (failed(*parseResult))
        return ::mlir::failure();
      targetOperands.push_back(operand);
    }
  }
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (::mlir::succeeded(parser.parseOptionalColon())) {

  {
    ::mlir::Type optionalType;
    ::mlir::OptionalParseResult parseResult =
                                    parser.parseOptionalType(optionalType);
    if (parseResult.has_value()) {
      if (failed(*parseResult))
        return ::mlir::failure();
      targetTypes.push_back(optionalType);
    }
  }
  }
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void PrintOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  if (::mlir::Value value = getTarget())
    _odsPrinter << value;
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  if (getTarget()) {
    _odsPrinter << ' ' << ":";
    _odsPrinter << ' ';
    _odsPrinter << (getTarget() ? ::llvm::ArrayRef<::mlir::Type>(getTarget().getType()) : ::llvm::ArrayRef<::mlir::Type>());
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::PrintOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::ReplicateOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
std::pair<unsigned, unsigned> ReplicateOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

} // namespace detail
ReplicateOpAdaptor::ReplicateOpAdaptor(ReplicateOp op) : ReplicateOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult ReplicateOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

std::pair<unsigned, unsigned> ReplicateOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {false, true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 1) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange ReplicateOp::getHandlesMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

std::pair<unsigned, unsigned> ReplicateOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

void ReplicateOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange replicated, ::mlir::Value pattern, ::mlir::ValueRange handles) {
  odsState.addOperands(pattern);
  odsState.addOperands(handles);
  odsState.addTypes(replicated);
}

void ReplicateOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() >= 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult ReplicateOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  if (!(((this->getODSOperands(1).getType()) == (this->getODSResults(0).getType()) && (this->getODSResults(0).getType()) == (this->getODSOperands(1).getType()))))
    return emitOpError("failed to verify that all of {handles, replicated} have same type");
  return ::mlir::success();
}

::llvm::LogicalResult ReplicateOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult ReplicateOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand patternRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> patternOperands(&patternRawOperand, 1);  ::llvm::SMLoc patternOperandsLoc;
  (void)patternOperandsLoc;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> handlesOperands;
  ::llvm::SMLoc handlesOperandsLoc;
  (void)handlesOperandsLoc;
  ::mlir::Type patternRawType{};
  ::llvm::ArrayRef<::mlir::Type> patternTypes(&patternRawType, 1);
  ::llvm::SmallVector<::mlir::Type, 1> handlesTypes;
  if (parser.parseKeyword("num"))
    return ::mlir::failure();
  if (parser.parseLParen())
    return ::mlir::failure();

  patternOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(patternRawOperand))
    return ::mlir::failure();
  if (parser.parseRParen())
    return ::mlir::failure();

  handlesOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(handlesOperands))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    patternRawType = type;
  }
  if (parser.parseComma())
    return ::mlir::failure();

  if (parser.parseTypeList(handlesTypes))
    return ::mlir::failure();
  result.addTypes(handlesTypes);
  if (parser.resolveOperands(patternOperands, patternTypes, patternOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(handlesOperands, handlesTypes, handlesOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void ReplicateOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ' << "num";
  _odsPrinter << "(";
  _odsPrinter << getPattern();
  _odsPrinter << ")";
  _odsPrinter << ' ';
  _odsPrinter << getHandles();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getPattern().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
  _odsPrinter << ",";
  _odsPrinter << ' ';
  _odsPrinter << getHandles().getTypes();
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::ReplicateOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::SelectOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
SelectOpGenericAdaptorBase::SelectOpGenericAdaptorBase(SelectOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::llvm::StringRef SelectOpGenericAdaptorBase::getOpName() {
  auto attr = getOpNameAttr();
  return attr.getValue();
}

} // namespace detail
SelectOpAdaptor::SelectOpAdaptor(SelectOp op) : SelectOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult SelectOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_op_name = getProperties().op_name; (void)tblgen_op_name;
  if (!tblgen_op_name) return emitError(loc, "'transform.select' op ""requires attribute 'op_name'");

  if (tblgen_op_name && !((::llvm::isa<::mlir::StringAttr>(tblgen_op_name))))
    return emitError(loc, "'transform.select' op ""attribute 'op_name' failed to satisfy constraint: string attribute");
  return ::mlir::success();
}

::llvm::LogicalResult SelectOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.op_name;
       auto attr = dict.get("op_name");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `op_name` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute SelectOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.op_name;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("op_name",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code SelectOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.op_name.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> SelectOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "op_name")
      return prop.op_name;
  return std::nullopt;
}

void SelectOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "op_name") {
       prop.op_name = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.op_name)>>(value);
       return;
    }
}

void SelectOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.op_name) attrs.append("op_name", prop.op_name);
}

::llvm::LogicalResult SelectOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getOpNameAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(attr, "op_name", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult SelectOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.op_name)))
    return ::mlir::failure();
  return ::mlir::success();
}

void SelectOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.op_name);
}

::llvm::StringRef SelectOp::getOpName() {
  auto attr = getOpNameAttr();
  return attr.getValue();
}

void SelectOp::setOpName(::llvm::StringRef attrValue) {
  getProperties().op_name = ::mlir::Builder((*this)->getContext()).getStringAttr(attrValue);
}

void SelectOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::mlir::StringAttr op_name) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().op_name = op_name;
  odsState.addTypes(result);
}

void SelectOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::mlir::StringAttr op_name) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().op_name = op_name;
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void SelectOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value target, ::llvm::StringRef op_name) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().op_name = odsBuilder.getStringAttr(op_name);
  odsState.addTypes(result);
}

void SelectOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target, ::llvm::StringRef op_name) {
  odsState.addOperands(target);
  odsState.getOrAddProperties<Properties>().op_name = odsBuilder.getStringAttr(op_name);
  assert(resultTypes.size() == 1u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void SelectOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 1u && "mismatched number of return types");
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<SelectOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult SelectOp::verifyInvariantsImpl() {
  auto tblgen_op_name = getProperties().op_name; (void)tblgen_op_name;
  if (!tblgen_op_name) return emitOpError("requires attribute 'op_name'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps1(*this, tblgen_op_name, "op_name")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult SelectOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult SelectOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::StringAttr op_nameAttr;
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  if (parser.parseCustomAttributeWithFallback(op_nameAttr, parser.getBuilder().getType<::mlir::NoneType>())) {
    return ::mlir::failure();
  }
  if (op_nameAttr) result.getOrAddProperties<SelectOp::Properties>().op_name = op_nameAttr;
  if (parser.parseKeyword("in"))
    return ::mlir::failure();

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(targetOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void SelectOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter.printAttributeWithoutType(getOpNameAttr());
  _odsPrinter << ' ' << "in";
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("op_name");
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::SelectOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::SequenceOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
SequenceOpGenericAdaptorBase::SequenceOpGenericAdaptorBase(SequenceOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

std::pair<unsigned, unsigned> SequenceOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  ::llvm::ArrayRef<int32_t> sizeAttr = getProperties().operandSegmentSizes;

  unsigned start = 0;
  for (unsigned i = 0; i < index; ++i)
    start += sizeAttr[i];
  return {start, sizeAttr[index]};
}

::mlir::transform::FailurePropagationMode SequenceOpGenericAdaptorBase::getFailurePropagationMode() {
  auto attr = getFailurePropagationModeAttr();
  return attr.getValue();
}

} // namespace detail
SequenceOpAdaptor::SequenceOpAdaptor(SequenceOp op) : SequenceOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult SequenceOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_failure_propagation_mode = getProperties().failure_propagation_mode; (void)tblgen_failure_propagation_mode;
  if (!tblgen_failure_propagation_mode) return emitError(loc, "'transform.sequence' op ""requires attribute 'failure_propagation_mode'");

  if (tblgen_failure_propagation_mode && !((::llvm::isa<::mlir::transform::FailurePropagationModeAttr>(tblgen_failure_propagation_mode))))
    return emitError(loc, "'transform.sequence' op ""attribute 'failure_propagation_mode' failed to satisfy constraint: Silenceable error propagation policy");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> SequenceOp::getODSOperandIndexAndLength(unsigned index) {
  ::llvm::ArrayRef<int32_t> sizeAttr = getProperties().operandSegmentSizes;

  unsigned start = 0;
  for (unsigned i = 0; i < index; ++i)
    start += sizeAttr[i];
  return {start, sizeAttr[index]};
}

::mlir::MutableOperandRange SequenceOp::getRootMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second, ::mlir::MutableOperandRange::OperandSegment(0u, {getOperandSegmentSizesAttrName(), ::mlir::DenseI32ArrayAttr::get(getContext(), getProperties().operandSegmentSizes)}));
  return mutableRange;
}

::mlir::MutableOperandRange SequenceOp::getExtraBindingsMutable() {
  auto range = getODSOperandIndexAndLength(1);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second, ::mlir::MutableOperandRange::OperandSegment(1u, {getOperandSegmentSizesAttrName(), ::mlir::DenseI32ArrayAttr::get(getContext(), getProperties().operandSegmentSizes)}));
  return mutableRange;
}

std::pair<unsigned, unsigned> SequenceOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult SequenceOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.failure_propagation_mode;
       auto attr = dict.get("failure_propagation_mode");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `failure_propagation_mode` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
{

      auto setFromAttr = [] (auto &propStorage, ::mlir::Attribute propAttr,
               ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) -> ::mlir::LogicalResult {
        return convertFromAttribute(propStorage, propAttr, emitError);
      };
         auto attr = dict.get("operandSegmentSizes");   if (!attr) attr = dict.get("operand_segment_sizes");;
;
      if (attr && ::mlir::failed(setFromAttr(prop.operandSegmentSizes, attr, emitError)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::mlir::Attribute SequenceOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.failure_propagation_mode;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("failure_propagation_mode",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.operandSegmentSizes;
      auto attr = [&]() -> ::mlir::Attribute {
        return ::mlir::DenseI32ArrayAttr::get(ctx, propStorage);
      }();
      attrs.push_back(odsBuilder.getNamedAttr("operandSegmentSizes", attr));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code SequenceOp::computePropertiesHash(const Properties &prop) {
  auto hash_operandSegmentSizes = [] (const auto &propStorage) -> llvm::hash_code {
    return ::llvm::hash_combine_range(std::begin(propStorage), std::end(propStorage));;
  };
  return llvm::hash_combine(
    llvm::hash_value(prop.failure_propagation_mode.getAsOpaquePointer()), 
    hash_operandSegmentSizes(prop.operandSegmentSizes));
}

std::optional<mlir::Attribute> SequenceOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "failure_propagation_mode")
      return prop.failure_propagation_mode;
    if (name == "operand_segment_sizes" || name == "operandSegmentSizes") return [&]() -> ::mlir::Attribute { return ::mlir::DenseI32ArrayAttr::get(ctx, prop.operandSegmentSizes); }();
  return std::nullopt;
}

void SequenceOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "failure_propagation_mode") {
       prop.failure_propagation_mode = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.failure_propagation_mode)>>(value);
       return;
    }
        if (name == "operand_segment_sizes" || name == "operandSegmentSizes") {
       auto arrAttr = ::llvm::dyn_cast_or_null<::mlir::DenseI32ArrayAttr>(value);
       if (!arrAttr) return;
       if (arrAttr.size() != sizeof(prop.operandSegmentSizes) / sizeof(int32_t))
         return;
       llvm::copy(arrAttr.asArrayRef(), prop.operandSegmentSizes.begin());
       return;
    }
}

void SequenceOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.failure_propagation_mode) attrs.append("failure_propagation_mode", prop.failure_propagation_mode);
  attrs.append("operandSegmentSizes", [&]() -> ::mlir::Attribute { return ::mlir::DenseI32ArrayAttr::get(ctx, prop.operandSegmentSizes); }());
}

::llvm::LogicalResult SequenceOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getFailurePropagationModeAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps9(attr, "failure_propagation_mode", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult SequenceOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readAttribute(prop.failure_propagation_mode)))
    return ::mlir::failure();

  if (reader.getBytecodeVersion() < /*kNativePropertiesODSSegmentSize=*/6) {
    auto &propStorage = prop.operandSegmentSizes;
    ::mlir::DenseI32ArrayAttr attr;
    if (::mlir::failed(reader.readAttribute(attr))) return ::mlir::failure();
    if (attr.size() > static_cast<int64_t>(sizeof(propStorage) / sizeof(int32_t))) {
      reader.emitError("size mismatch for operand/result_segment_size");
      return ::mlir::failure();
    }
    ::llvm::copy(::llvm::ArrayRef<int32_t>(attr), propStorage.begin());
  }

  {
    auto &propStorage = prop.operandSegmentSizes;
    auto readProp = [&]() {

  if (reader.getBytecodeVersion() >= /*kNativePropertiesODSSegmentSize=*/6)
    return reader.readSparseArray(::llvm::MutableArrayRef(propStorage));
;
      return ::mlir::success();
    };
    if (::mlir::failed(readProp()))
      return ::mlir::failure();
  }
  return ::mlir::success();
}

void SequenceOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;
  writer.writeAttribute(prop.failure_propagation_mode);

if (writer.getBytecodeVersion() < /*kNativePropertiesODSSegmentSize=*/6) {
  auto &propStorage = prop.operandSegmentSizes;
  writer.writeAttribute(::mlir::DenseI32ArrayAttr::get(this->getContext(), propStorage));
}

  {
    auto &propStorage = prop.operandSegmentSizes;

  if (writer.getBytecodeVersion() >= /*kNativePropertiesODSSegmentSize=*/6)
    writer.writeSparseArray(::llvm::ArrayRef(propStorage));
;
  }
}

::mlir::transform::FailurePropagationMode SequenceOp::getFailurePropagationMode() {
  auto attr = getFailurePropagationModeAttr();
  return attr.getValue();
}

void SequenceOp::setFailurePropagationMode(::mlir::transform::FailurePropagationMode attrValue) {
  getProperties().failure_propagation_mode = ::mlir::transform::FailurePropagationModeAttr::get(::mlir::Builder((*this)->getContext()).getContext(), attrValue);
}

void SequenceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::transform::FailurePropagationModeAttr failure_propagation_mode, /*optional*/::mlir::Value root, ::mlir::ValueRange extra_bindings) {
  if (root)
    odsState.addOperands(root);
  odsState.addOperands(extra_bindings);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({(root ? 1 : 0), static_cast<int32_t>(extra_bindings.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().failure_propagation_mode = failure_propagation_mode;
  (void)odsState.addRegion();
  odsState.addTypes(results);
}

void SequenceOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::transform::FailurePropagationMode failure_propagation_mode, /*optional*/::mlir::Value root, ::mlir::ValueRange extra_bindings) {
  if (root)
    odsState.addOperands(root);
  odsState.addOperands(extra_bindings);
  ::llvm::copy(::llvm::ArrayRef<int32_t>({(root ? 1 : 0), static_cast<int32_t>(extra_bindings.size())}), odsState.getOrAddProperties<Properties>().operandSegmentSizes.begin());
  odsState.getOrAddProperties<Properties>().failure_propagation_mode = ::mlir::transform::FailurePropagationModeAttr::get(odsBuilder.getContext(), failure_propagation_mode);
  (void)odsState.addRegion();
  odsState.addTypes(results);
}

void SequenceOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  for (unsigned i = 0; i != 1; ++i)
    (void)odsState.addRegion();
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<SequenceOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

::llvm::LogicalResult SequenceOp::verifyInvariantsImpl() {
  auto tblgen_failure_propagation_mode = getProperties().failure_propagation_mode; (void)tblgen_failure_propagation_mode;
  if (!tblgen_failure_propagation_mode) return emitOpError("requires attribute 'failure_propagation_mode'");

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps9(*this, tblgen_failure_propagation_mode, "failure_propagation_mode")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    if (valueGroup0.size() > 1) {
      return emitOpError("operand group starting at #") << index
          << " requires 0 or 1 element, but found " << valueGroup0.size();
    }

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
    auto valueGroup1 = getODSOperands(1);

    for (auto v : valueGroup1) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps2(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;

    for (auto &region : ::llvm::MutableArrayRef((*this)->getRegion(0)))
      if (::mlir::failed(__mlir_ods_local_region_constraint_TransformOps1(*this, region, "body", index++)))
        return ::mlir::failure();
  }
  return ::mlir::success();
}

::llvm::LogicalResult SequenceOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult SequenceOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> rootOperands;
  ::llvm::SMLoc rootOperandsLoc;
  (void)rootOperandsLoc;
  ::llvm::SmallVector<::mlir::Type, 1> rootTypes;
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> extra_bindingsOperands;
  ::llvm::SMLoc extra_bindingsOperandsLoc;
  (void)extra_bindingsOperandsLoc;
  ::llvm::SmallVector<::mlir::Type, 1> extra_bindingsTypes;
  ::llvm::SmallVector<::mlir::Type, 1> resultsTypes;
  ::mlir::transform::FailurePropagationModeAttr failure_propagation_modeAttr;
  ::llvm::SmallVector<std::unique_ptr<::mlir::Region>, 2> fullRegions;
  {
    rootOperandsLoc = parser.getCurrentLocation();
    ::std::optional<::mlir::OpAsmParser::UnresolvedOperand> rootOperand;
    ::mlir::Type rootType;
    extra_bindingsOperandsLoc = parser.getCurrentLocation();
    auto odsResult = parseSequenceOpOperands(parser, rootOperand, rootType, extra_bindingsOperands, extra_bindingsTypes);
    if (odsResult) return ::mlir::failure();
    if (rootOperand.has_value())
      rootOperands.push_back(*rootOperand);
    if (rootType)
      rootTypes.push_back(rootType);
  }
  if (::mlir::succeeded(parser.parseOptionalArrow())) {

  if (parser.parseTypeList(resultsTypes))
    return ::mlir::failure();
  }
  if (parser.parseKeyword("failures"))
    return ::mlir::failure();
  if (parser.parseLParen())
    return ::mlir::failure();

  {
    ::llvm::StringRef attrStr;
    ::mlir::NamedAttrList attrStorage;
    auto loc = parser.getCurrentLocation();
    if (parser.parseOptionalKeyword(&attrStr, {"propagate","suppress"})) {
      ::mlir::StringAttr attrVal;
      ::mlir::OptionalParseResult parseResult =
        parser.parseOptionalAttribute(attrVal,
                                      parser.getBuilder().getNoneType(),
                                      "failure_propagation_mode", attrStorage);
      if (parseResult.has_value()) {
        if (failed(*parseResult))
          return ::mlir::failure();
        attrStr = attrVal.getValue();
      } else {
        return parser.emitError(loc, "expected string or keyword containing one of the following enum values for attribute 'failure_propagation_mode' [propagate, suppress]");
      }
    }
    if (!attrStr.empty()) {
      auto attrOptional = ::mlir::transform::symbolizeFailurePropagationMode(attrStr);
      if (!attrOptional)
        return parser.emitError(loc, "invalid ")
               << "failure_propagation_mode attribute specification: \"" << attrStr << '"';;

      failure_propagation_modeAttr = ::mlir::transform::FailurePropagationModeAttr::get(parser.getBuilder().getContext(), *attrOptional);
        result.getOrAddProperties<SequenceOp::Properties>().failure_propagation_mode = failure_propagation_modeAttr;
    }
  }
  if (parser.parseRParen())
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDictWithKeyword(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }

  {
    std::unique_ptr<::mlir::Region> region;
    auto firstRegionResult = parser.parseOptionalRegion(region);
    if (firstRegionResult.has_value()) {
      if (failed(*firstRegionResult))
        return ::mlir::failure();
      fullRegions.emplace_back(std::move(region));

      // Parse any trailing regions.
      while (succeeded(parser.parseOptionalComma())) {
        region = std::make_unique<::mlir::Region>();
        if (parser.parseRegion(*region))
          return ::mlir::failure();
        fullRegions.emplace_back(std::move(region));
      }
    }
  }

  for (auto &region : fullRegions)
    ensureTerminator(*region, parser.getBuilder(), result.location);
  result.addRegions(fullRegions);
::llvm::copy(::llvm::ArrayRef<int32_t>({static_cast<int32_t>(rootOperands.size()), static_cast<int32_t>(extra_bindingsOperands.size())}), result.getOrAddProperties<SequenceOp::Properties>().operandSegmentSizes.begin());
  result.addTypes(resultsTypes);
  if (parser.resolveOperands(rootOperands, rootTypes, rootOperandsLoc, result.operands))
    return ::mlir::failure();
  if (parser.resolveOperands(extra_bindingsOperands, extra_bindingsTypes, extra_bindingsOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void SequenceOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  printSequenceOpOperands(_odsPrinter, *this, getRoot(), (getRoot() ? getRoot().getType() : ::mlir::Type()), getExtraBindings(), getExtraBindings().getTypes());
  if (!getResults().empty()) {
    _odsPrinter << ' ' << "->";
    _odsPrinter << ' ';
    _odsPrinter << getResults().getTypes();
  }
  _odsPrinter << ' ' << "failures";
  _odsPrinter << "(";

  {
    auto caseValue = getFailurePropagationMode();
    auto caseValueStr = stringifyFailurePropagationMode(caseValue);
    _odsPrinter << caseValueStr;
  }
  _odsPrinter << ")";
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  elidedAttrs.push_back("operandSegmentSizes");
  elidedAttrs.push_back("failure_propagation_mode");
  _odsPrinter.printOptionalAttrDictWithKeyword((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ';
    llvm::interleaveComma(getOperation()->getRegions(), _odsPrinter, [&](::mlir::Region &region) {

  {
    bool printTerminator = true;
    if (auto *term = region.empty() ? nullptr : region.begin()->getTerminator()) {
      printTerminator = !term->getAttrDictionary().empty() ||
                        term->getNumOperands() != 0 ||
                        term->getNumResults() != 0;
    }
    _odsPrinter.printRegion(region, /*printEntryBlockArgs=*/true,
      /*printBlockTerminators=*/printTerminator);
  }
    });
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::SequenceOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::SplitHandleOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
SplitHandleOpGenericAdaptorBase::SplitHandleOpGenericAdaptorBase(SplitHandleOp op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), properties(op.getProperties()), odsRegions(op->getRegions()) {}

::mlir::BoolAttr SplitHandleOpGenericAdaptorBase::getPassThroughEmptyHandleAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().pass_through_empty_handle);
  return attr;
}

bool SplitHandleOpGenericAdaptorBase::getPassThroughEmptyHandle() {
  auto attr = getPassThroughEmptyHandleAttr();
  return attr.getValue();
}

::mlir::BoolAttr SplitHandleOpGenericAdaptorBase::getFailOnPayloadTooSmallAttr() {
  auto attr = ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().fail_on_payload_too_small);
  return attr;
}

bool SplitHandleOpGenericAdaptorBase::getFailOnPayloadTooSmall() {
  auto attr = getFailOnPayloadTooSmallAttr();
  return attr.getValue();
}

::std::optional<uint64_t> SplitHandleOpGenericAdaptorBase::getOverflowResult() {
  auto attr = getOverflowResultAttr();
  return attr ? ::std::optional<uint64_t>(attr.getValue().getZExtValue()) : (::std::nullopt);
}

} // namespace detail
SplitHandleOpAdaptor::SplitHandleOpAdaptor(SplitHandleOp op) : SplitHandleOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult SplitHandleOpAdaptor::verify(::mlir::Location loc) {
  auto tblgen_fail_on_payload_too_small = getProperties().fail_on_payload_too_small; (void)tblgen_fail_on_payload_too_small;
  auto tblgen_overflow_result = getProperties().overflow_result; (void)tblgen_overflow_result;
  auto tblgen_pass_through_empty_handle = getProperties().pass_through_empty_handle; (void)tblgen_pass_through_empty_handle;

  if (tblgen_pass_through_empty_handle && !((::llvm::isa<::mlir::BoolAttr>(tblgen_pass_through_empty_handle))))
    return emitError(loc, "'transform.split_handle' op ""attribute 'pass_through_empty_handle' failed to satisfy constraint: bool attribute");

  if (tblgen_fail_on_payload_too_small && !((::llvm::isa<::mlir::BoolAttr>(tblgen_fail_on_payload_too_small))))
    return emitError(loc, "'transform.split_handle' op ""attribute 'fail_on_payload_too_small' failed to satisfy constraint: bool attribute");

  if (tblgen_overflow_result && !(((::llvm::isa<::mlir::IntegerAttr>(tblgen_overflow_result))) && ((::llvm::cast<::mlir::IntegerAttr>(tblgen_overflow_result).getType().isSignlessInteger(64)))))
    return emitError(loc, "'transform.split_handle' op ""attribute 'overflow_result' failed to satisfy constraint: 64-bit signless integer attribute");
  return ::mlir::success();
}

std::pair<unsigned, unsigned> SplitHandleOp::getODSResultIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumResults() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::llvm::LogicalResult SplitHandleOp::setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
  ::mlir::DictionaryAttr dict = ::llvm::dyn_cast<::mlir::DictionaryAttr>(attr);
  if (!dict) {
    emitError() << "expected DictionaryAttr to set properties";
    return ::mlir::failure();
  }

  {
    auto &propStorage = prop.fail_on_payload_too_small;
       auto attr = dict.get("fail_on_payload_too_small");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `fail_on_payload_too_small` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.overflow_result;
       auto attr = dict.get("overflow_result");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `overflow_result` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }

  {
    auto &propStorage = prop.pass_through_empty_handle;
       auto attr = dict.get("pass_through_empty_handle");
    if (attr) {
      auto convertedAttr = ::llvm::dyn_cast<std::remove_reference_t<decltype(propStorage)>>(attr);
      if (convertedAttr) {
        propStorage = convertedAttr;
      } else {
        emitError() << "Invalid attribute `pass_through_empty_handle` in property conversion: " << attr;
        return ::mlir::failure();
      }
    }
  }
  return ::mlir::success();
}

::mlir::Attribute SplitHandleOp::getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop) {
    ::mlir::SmallVector<::mlir::NamedAttribute> attrs;
    ::mlir::Builder odsBuilder{ctx};

    {
      const auto &propStorage = prop.fail_on_payload_too_small;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("fail_on_payload_too_small",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.overflow_result;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("overflow_result",
                                       propStorage));
    }

    {
      const auto &propStorage = prop.pass_through_empty_handle;
      if (propStorage)
        attrs.push_back(odsBuilder.getNamedAttr("pass_through_empty_handle",
                                       propStorage));
    }

  if (!attrs.empty())
    return odsBuilder.getDictionaryAttr(attrs);
  return {};
}

llvm::hash_code SplitHandleOp::computePropertiesHash(const Properties &prop) {
  return llvm::hash_combine(
    llvm::hash_value(prop.fail_on_payload_too_small.getAsOpaquePointer()), 
    llvm::hash_value(prop.overflow_result.getAsOpaquePointer()), 
    llvm::hash_value(prop.pass_through_empty_handle.getAsOpaquePointer()));
}

std::optional<mlir::Attribute> SplitHandleOp::getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name) {
    if (name == "fail_on_payload_too_small")
      return prop.fail_on_payload_too_small;

    if (name == "overflow_result")
      return prop.overflow_result;

    if (name == "pass_through_empty_handle")
      return prop.pass_through_empty_handle;
  return std::nullopt;
}

void SplitHandleOp::setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value) {
    if (name == "fail_on_payload_too_small") {
       prop.fail_on_payload_too_small = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.fail_on_payload_too_small)>>(value);
       return;
    }

    if (name == "overflow_result") {
       prop.overflow_result = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.overflow_result)>>(value);
       return;
    }

    if (name == "pass_through_empty_handle") {
       prop.pass_through_empty_handle = ::llvm::dyn_cast_or_null<std::remove_reference_t<decltype(prop.pass_through_empty_handle)>>(value);
       return;
    }
}

void SplitHandleOp::populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs) {
    if (prop.fail_on_payload_too_small) attrs.append("fail_on_payload_too_small", prop.fail_on_payload_too_small);

    if (prop.overflow_result) attrs.append("overflow_result", prop.overflow_result);

    if (prop.pass_through_empty_handle) attrs.append("pass_through_empty_handle", prop.pass_through_empty_handle);
}

::llvm::LogicalResult SplitHandleOp::verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError) {
    {
      ::mlir::Attribute attr = attrs.get(getFailOnPayloadTooSmallAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps14(attr, "fail_on_payload_too_small", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getOverflowResultAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(attr, "overflow_result", emitError)))
        return ::mlir::failure();
    }

    {
      ::mlir::Attribute attr = attrs.get(getPassThroughEmptyHandleAttrName(opName));
      if (attr && ::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps14(attr, "pass_through_empty_handle", emitError)))
        return ::mlir::failure();
    }
    return ::mlir::success();
}

::llvm::LogicalResult SplitHandleOp::readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state) {
  auto &prop = state.getOrAddProperties<Properties>(); (void)prop;
  if (::mlir::failed(reader.readOptionalAttribute(prop.fail_on_payload_too_small)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.overflow_result)))
    return ::mlir::failure();

  if (::mlir::failed(reader.readOptionalAttribute(prop.pass_through_empty_handle)))
    return ::mlir::failure();
  return ::mlir::success();
}

void SplitHandleOp::writeProperties(::mlir::DialectBytecodeWriter &writer) {
  auto &prop = getProperties(); (void)prop;

  writer.writeOptionalAttribute(prop.fail_on_payload_too_small);

  writer.writeOptionalAttribute(prop.overflow_result);

  writer.writeOptionalAttribute(prop.pass_through_empty_handle);
}

bool SplitHandleOp::getPassThroughEmptyHandle() {
  auto attr = getPassThroughEmptyHandleAttr();
  return attr.getValue();
}

bool SplitHandleOp::getFailOnPayloadTooSmall() {
  auto attr = getFailOnPayloadTooSmallAttr();
  return attr.getValue();
}

::std::optional<uint64_t> SplitHandleOp::getOverflowResult() {
  auto attr = getOverflowResultAttr();
  return attr ? ::std::optional<uint64_t>(attr.getValue().getZExtValue()) : (::std::nullopt);
}

void SplitHandleOp::setPassThroughEmptyHandle(bool attrValue) {
  getProperties().pass_through_empty_handle = ::mlir::Builder((*this)->getContext()).getBoolAttr(attrValue);
}

void SplitHandleOp::setFailOnPayloadTooSmall(bool attrValue) {
  getProperties().fail_on_payload_too_small = ::mlir::Builder((*this)->getContext()).getBoolAttr(attrValue);
}

void SplitHandleOp::setOverflowResult(::std::optional<uint64_t> attrValue) {
    auto &odsProp = getProperties().overflow_result;
    if (attrValue)
      odsProp = ::mlir::Builder((*this)->getContext()).getIntegerAttr(::mlir::Builder((*this)->getContext()).getIntegerType(64), *attrValue);
    else
      odsProp = nullptr;
}

void SplitHandleOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::Value handle, ::mlir::BoolAttr pass_through_empty_handle, ::mlir::BoolAttr fail_on_payload_too_small, /*optional*/::mlir::IntegerAttr overflow_result) {
  odsState.addOperands(handle);
  if (pass_through_empty_handle) {
    odsState.getOrAddProperties<Properties>().pass_through_empty_handle = pass_through_empty_handle;
  }
  if (fail_on_payload_too_small) {
    odsState.getOrAddProperties<Properties>().fail_on_payload_too_small = fail_on_payload_too_small;
  }
  if (overflow_result) {
    odsState.getOrAddProperties<Properties>().overflow_result = overflow_result;
  }
  odsState.addTypes(results);
}

void SplitHandleOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::Value handle, bool pass_through_empty_handle, bool fail_on_payload_too_small, /*optional*/::mlir::IntegerAttr overflow_result) {
  odsState.addOperands(handle);
  odsState.getOrAddProperties<Properties>().pass_through_empty_handle = odsBuilder.getBoolAttr(pass_through_empty_handle);
  odsState.getOrAddProperties<Properties>().fail_on_payload_too_small = odsBuilder.getBoolAttr(fail_on_payload_too_small);
  if (overflow_result) {
    odsState.getOrAddProperties<Properties>().overflow_result = overflow_result;
  }
  odsState.addTypes(results);
}

void SplitHandleOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  odsState.addTypes(resultTypes);

  if (!attributes.empty()) {
    ::mlir::OpaqueProperties properties =
      &odsState.getOrAddProperties<SplitHandleOp::Properties>();
    std::optional<::mlir::RegisteredOperationName> info =
      odsState.name.getRegisteredInfo();
    if (failed(info->setOpPropertiesFromAttribute(odsState.name, properties,
        odsState.attributes.getDictionary(odsState.getContext()), nullptr)))
      ::llvm::report_fatal_error("Property conversion failed.");
  }
}

void SplitHandleOp::populateDefaultProperties(::mlir::OperationName opName, Properties &properties) {
  ::mlir::Builder odsBuilder(opName.getContext());
  if (!properties.pass_through_empty_handle)
    properties.pass_through_empty_handle = odsBuilder.getBoolAttr(true);
  if (!properties.fail_on_payload_too_small)
    properties.fail_on_payload_too_small = odsBuilder.getBoolAttr(true);
}

::llvm::LogicalResult SplitHandleOp::verifyInvariantsImpl() {
  auto tblgen_fail_on_payload_too_small = getProperties().fail_on_payload_too_small; (void)tblgen_fail_on_payload_too_small;
  auto tblgen_overflow_result = getProperties().overflow_result; (void)tblgen_overflow_result;
  auto tblgen_pass_through_empty_handle = getProperties().pass_through_empty_handle; (void)tblgen_pass_through_empty_handle;

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps14(*this, tblgen_pass_through_empty_handle, "pass_through_empty_handle")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps14(*this, tblgen_fail_on_payload_too_small, "fail_on_payload_too_small")))
    return ::mlir::failure();

  if (::mlir::failed(__mlir_ods_local_attr_constraint_TransformOps4(*this, tblgen_overflow_result, "overflow_result")))
    return ::mlir::failure();
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps6(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSResults(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "result", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult SplitHandleOp::verifyInvariants() {
  if(::mlir::succeeded(verifyInvariantsImpl()) && ::mlir::succeeded(verify()))
    return ::mlir::success();
  return ::mlir::failure();
}

::mlir::ParseResult SplitHandleOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand handleRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> handleOperands(&handleRawOperand, 1);  ::llvm::SMLoc handleOperandsLoc;
  (void)handleOperandsLoc;
  ::llvm::ArrayRef<::mlir::Type> allOperandTypes;
  ::llvm::ArrayRef<::mlir::Type> allResultTypes;

  handleOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(handleRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
    if (failed(verifyInherentAttrs(result.name, result.attributes, [&]() {
        return parser.emitError(loc) << "'" << result.name.getStringRef() << "' op ";
      })))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  ::mlir::FunctionType allOperand__allResult_functionType;
  if (parser.parseType(allOperand__allResult_functionType))
    return ::mlir::failure();
  allOperandTypes = allOperand__allResult_functionType.getInputs();
  allResultTypes = allOperand__allResult_functionType.getResults();
  result.addTypes(allResultTypes);
  if (parser.resolveOperands(handleOperands, allOperandTypes, parser.getNameLoc(), result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void SplitHandleOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getHandle();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getPassThroughEmptyHandleAttr();
     if(attr && (attr == odsBuilder.getBoolAttr(true)))
       elidedAttrs.push_back("pass_through_empty_handle");
  }
  {
     ::mlir::Builder odsBuilder(getContext());
     ::mlir::Attribute attr = getFailOnPayloadTooSmallAttr();
     if(attr && (attr == odsBuilder.getBoolAttr(true)))
       elidedAttrs.push_back("fail_on_payload_too_small");
  }
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  _odsPrinter.printFunctionalType(getOperation()->getOperandTypes(), getOperation()->getResultTypes());
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::SplitHandleOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::VerifyOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
} // namespace detail
VerifyOpAdaptor::VerifyOpAdaptor(VerifyOp op) : VerifyOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult VerifyOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

void VerifyOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value target) {
  odsState.addOperands(target);
}

void VerifyOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value target) {
  odsState.addOperands(target);
  assert(resultTypes.size() == 0u && "mismatched number of results");
  odsState.addTypes(resultTypes);
}

void VerifyOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  assert(operands.size() == 1u && "mismatched number of parameters");
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult VerifyOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps1(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult VerifyOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult VerifyOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::mlir::OpAsmParser::UnresolvedOperand targetRawOperand{};
  ::llvm::ArrayRef<::mlir::OpAsmParser::UnresolvedOperand> targetOperands(&targetRawOperand, 1);  ::llvm::SMLoc targetOperandsLoc;
  (void)targetOperandsLoc;
  ::mlir::Type targetRawType{};
  ::llvm::ArrayRef<::mlir::Type> targetTypes(&targetRawType, 1);

  targetOperandsLoc = parser.getCurrentLocation();
  if (parser.parseOperand(targetRawOperand))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (parser.parseColon())
    return ::mlir::failure();

  {
    ::mlir::transform::TransformHandleTypeInterface type;
    if (parser.parseCustomTypeWithFallback(type))
      return ::mlir::failure();
    targetRawType = type;
  }
  if (parser.resolveOperands(targetOperands, targetTypes, targetOperandsLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void VerifyOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getTarget();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  _odsPrinter << ' ' << ":";
  _odsPrinter << ' ';
  {
    auto type = getTarget().getType();
    if (auto validType = ::llvm::dyn_cast<::mlir::transform::TransformHandleTypeInterface>(type))
      _odsPrinter.printStrippedAttrOrType(validType);
   else
     _odsPrinter << type;
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::VerifyOp)

namespace mlir {
namespace transform {

//===----------------------------------------------------------------------===//
// ::mlir::transform::YieldOp definitions
//===----------------------------------------------------------------------===//

namespace detail {
std::pair<unsigned, unsigned> YieldOpGenericAdaptorBase::getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (odsOperandsSize - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

} // namespace detail
YieldOpAdaptor::YieldOpAdaptor(YieldOp op) : YieldOpGenericAdaptor(op->getOperands(), op) {}

::llvm::LogicalResult YieldOpAdaptor::verify(::mlir::Location loc) {
  return ::mlir::success();
}

std::pair<unsigned, unsigned> YieldOp::getODSOperandIndexAndLength(unsigned index) {
  bool isVariadic[] = {true};
  int prevVariadicCount = 0;
  for (unsigned i = 0; i < index; ++i)
    if (isVariadic[i]) ++prevVariadicCount;

  // Calculate how many dynamic values a static variadic operand corresponds to.
  // This assumes all static variadic operands have the same dynamic value count.
  int variadicSize = (getOperation()->getNumOperands() - 0) / 1;
  // `index` passed in as the parameter is the static index which counts each
  // operand (variadic or not) as size 1. So here for each previous static variadic
  // operand, we need to offset by (variadicSize - 1) to get where the dynamic
  // value pack for this static operand starts.
  int start = index + (variadicSize - 1) * prevVariadicCount;
  int size = isVariadic[index] ? variadicSize : 1;
  return {start, size};
}

::mlir::MutableOperandRange YieldOp::getOperandsMutable() {
  auto range = getODSOperandIndexAndLength(0);
  auto mutableRange = ::mlir::MutableOperandRange(getOperation(), range.first, range.second);
  return mutableRange;
}

void YieldOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState) {
      return build(odsBuilder, odsState, ::mlir::ValueRange());
    
}

void YieldOp::build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands) {
  odsState.addOperands(operands);
}

void YieldOp::build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes) {
  odsState.addOperands(operands);
  odsState.addAttributes(attributes);
  assert(resultTypes.size() == 0u && "mismatched number of return types");
  odsState.addTypes(resultTypes);
}

::llvm::LogicalResult YieldOp::verifyInvariantsImpl() {
  {
    unsigned index = 0; (void)index;
    auto valueGroup0 = getODSOperands(0);

    for (auto v : valueGroup0) {
      if (::mlir::failed(__mlir_ods_local_type_constraint_TransformOps4(*this, v.getType(), "operand", index++)))
        return ::mlir::failure();
    }
  }
  return ::mlir::success();
}

::llvm::LogicalResult YieldOp::verifyInvariants() {
  return verifyInvariantsImpl();
}

::mlir::ParseResult YieldOp::parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result) {
  ::llvm::SmallVector<::mlir::OpAsmParser::UnresolvedOperand, 4> allOperands;
  ::llvm::SmallVector<::mlir::Type, 1> operandsTypes;
  [[maybe_unused]] ::llvm::SMLoc allOperandLoc = parser.getCurrentLocation();
  if (parser.parseOperandList(allOperands))
    return ::mlir::failure();
  {
    auto loc = parser.getCurrentLocation();(void)loc;
    if (parser.parseOptionalAttrDict(result.attributes))
      return ::mlir::failure();
  }
  if (::mlir::succeeded(parser.parseOptionalColon())) {

  if (parser.parseTypeList(operandsTypes))
    return ::mlir::failure();
  }
  if (parser.resolveOperands(allOperands, operandsTypes, allOperandLoc, result.operands))
    return ::mlir::failure();
  return ::mlir::success();
}

void YieldOp::print(::mlir::OpAsmPrinter &_odsPrinter) {
  _odsPrinter << ' ';
  _odsPrinter << getOperation()->getOperands();
  ::llvm::SmallVector<::llvm::StringRef, 2> elidedAttrs;
  _odsPrinter.printOptionalAttrDict((*this)->getAttrs(), elidedAttrs);
  if (!getOperands().empty()) {
    _odsPrinter << ' ' << ":";
    _odsPrinter << ' ';
    _odsPrinter << getOperands().getTypes();
  }
}

} // namespace transform
} // namespace mlir
MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::transform::YieldOp)


#endif  // GET_OP_CLASSES

