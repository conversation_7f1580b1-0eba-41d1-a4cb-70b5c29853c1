# Example: Traditional approach for oil production prediction
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import numpy as np
import pandas as pd

# Sample oil well data
well_data = {
    'reservoir_pressure': [3500, 3200, 2800, 3100, 2900],
    'temperature': [85, 82, 78, 80, 79],
    'porosity': [0.15, 0.18, 0.12, 0.16, 0.14],
    'permeability': [50, 75, 30, 60, 45],
    'production': [150, 180, 100, 160, 120]  # barrels per day
}

df = pd.DataFrame(well_data)
X = df[['reservoir_pressure', 'temperature', 'porosity', 'permeability']]
y = df['production']

# Traditional linear regression
lr_model = LinearRegression()
lr_model.fit(X, y)
print(f"Linear Regression R²: {lr_model.score(X, y):.3f}")

# Random Forest (shallow ensemble)
rf_model = RandomForestRegressor(n_estimators=10, max_depth=3)
rf_model.fit(X, y)
print(f"Random Forest R²: {rf_model.score(X, y):.3f}")