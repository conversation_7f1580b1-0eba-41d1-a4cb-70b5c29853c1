"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8038],{60148:(t,e,a)=>{a.d(e,{CP:()=>l,HT:()=>h,PB:()=>d,aC:()=>c,lC:()=>n,m:()=>o,tk:()=>i});var s=a(75905);var r=a(16750);var i=(0,s.K2)(((t,e)=>{const a=t.append("rect");a.attr("x",e.x);a.attr("y",e.y);a.attr("fill",e.fill);a.attr("stroke",e.stroke);a.attr("width",e.width);a.attr("height",e.height);if(e.name){a.attr("name",e.name)}if(e.rx){a.attr("rx",e.rx)}if(e.ry){a.attr("ry",e.ry)}if(e.attrs!==void 0){for(const t in e.attrs){a.attr(t,e.attrs[t])}}if(e.class){a.attr("class",e.class)}return a}),"drawRect");var n=(0,s.K2)(((t,e)=>{const a={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"};const s=i(t,a);s.lower()}),"drawBackgroundRect");var o=(0,s.K2)(((t,e)=>{const a=e.text.replace(s.H1," ");const r=t.append("text");r.attr("x",e.x);r.attr("y",e.y);r.attr("class","legend");r.style("text-anchor",e.anchor);if(e.class){r.attr("class",e.class)}const i=r.append("tspan");i.attr("x",e.x+e.textMargin*2);i.text(a);return r}),"drawText");var c=(0,s.K2)(((t,e,a,s)=>{const i=t.append("image");i.attr("x",e);i.attr("y",a);const n=(0,r.J)(s);i.attr("xlink:href",n)}),"drawImage");var l=(0,s.K2)(((t,e,a,s)=>{const i=t.append("use");i.attr("x",e);i.attr("y",a);const n=(0,r.J)(s);i.attr("xlink:href",`#${n}`)}),"drawEmbeddedImage");var d=(0,s.K2)((()=>{const t={x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0};return t}),"getNoteRect");var h=(0,s.K2)((()=>{const t={x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:true};return t}),"getTextObj")},13249:(t,e,a)=>{a.d(e,{m:()=>r});var s=a(75905);var r=class{constructor(t){this.init=t;this.records=this.init()}static{(0,s.K2)(this,"ImperativeState")}reset(){this.records=this.init()}}},38038:(t,e,a)=>{a.d(e,{diagram:()=>Tt});var s=a(60148);var r=a(13249);var i=a(96049);var n=a(75905);var o=a(24982);var c=a(16750);var l=function(){var t=(0,n.K2)((function(t,e,a,s){for(a=a||{},s=t.length;s--;a[t[s]]=e);return a}),"o"),e=[1,2],a=[1,3],s=[1,4],r=[2,4],i=[1,9],o=[1,11],c=[1,13],l=[1,14],d=[1,16],h=[1,17],p=[1,18],g=[1,24],u=[1,25],f=[1,26],x=[1,27],y=[1,28],b=[1,29],m=[1,30],T=[1,31],E=[1,32],w=[1,33],v=[1,34],k=[1,35],I=[1,36],L=[1,37],_=[1,38],P=[1,39],A=[1,41],N=[1,42],M=[1,43],D=[1,44],S=[1,45],O=[1,46],R=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],Y=[4,5,16,50,52,53],K=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],C=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],B=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],V=[68,69,70],F=[1,122];var W={trace:(0,n.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:(0,n.K2)((function t(e,a,s,r,i,n,o){var c=n.length-1;switch(i){case 3:r.apply(n[c]);return n[c];break;case 4:case 9:this.$=[];break;case 5:case 10:n[c-1].push(n[c]);this.$=n[c-1];break;case 6:case 7:case 11:case 12:this.$=n[c];break;case 8:case 13:this.$=[];break;case 15:n[c].type="createParticipant";this.$=n[c];break;case 16:n[c-1].unshift({type:"boxStart",boxData:r.parseBoxData(n[c-2])});n[c-1].push({type:"boxEnd",boxText:n[c-2]});this.$=n[c-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(n[c-2]),sequenceIndexStep:Number(n[c-1]),sequenceVisible:true,signalType:r.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(n[c-1]),sequenceIndexStep:1,sequenceVisible:true,signalType:r.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:false,signalType:r.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:true,signalType:r.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:n[c-1].actor};break;case 23:this.$={type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:n[c-1].actor};break;case 29:r.setDiagramTitle(n[c].substring(6));this.$=n[c].substring(6);break;case 30:r.setDiagramTitle(n[c].substring(7));this.$=n[c].substring(7);break;case 31:this.$=n[c].trim();r.setAccTitle(this.$);break;case 32:case 33:this.$=n[c].trim();r.setAccDescription(this.$);break;case 34:n[c-1].unshift({type:"loopStart",loopText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.LOOP_START});n[c-1].push({type:"loopEnd",loopText:n[c-2],signalType:r.LINETYPE.LOOP_END});this.$=n[c-1];break;case 35:n[c-1].unshift({type:"rectStart",color:r.parseMessage(n[c-2]),signalType:r.LINETYPE.RECT_START});n[c-1].push({type:"rectEnd",color:r.parseMessage(n[c-2]),signalType:r.LINETYPE.RECT_END});this.$=n[c-1];break;case 36:n[c-1].unshift({type:"optStart",optText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.OPT_START});n[c-1].push({type:"optEnd",optText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.OPT_END});this.$=n[c-1];break;case 37:n[c-1].unshift({type:"altStart",altText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.ALT_START});n[c-1].push({type:"altEnd",signalType:r.LINETYPE.ALT_END});this.$=n[c-1];break;case 38:n[c-1].unshift({type:"parStart",parText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.PAR_START});n[c-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END});this.$=n[c-1];break;case 39:n[c-1].unshift({type:"parStart",parText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.PAR_OVER_START});n[c-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END});this.$=n[c-1];break;case 40:n[c-1].unshift({type:"criticalStart",criticalText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.CRITICAL_START});n[c-1].push({type:"criticalEnd",signalType:r.LINETYPE.CRITICAL_END});this.$=n[c-1];break;case 41:n[c-1].unshift({type:"breakStart",breakText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.BREAK_START});n[c-1].push({type:"breakEnd",optText:r.parseMessage(n[c-2]),signalType:r.LINETYPE.BREAK_END});this.$=n[c-1];break;case 43:this.$=n[c-3].concat([{type:"option",optionText:r.parseMessage(n[c-1]),signalType:r.LINETYPE.CRITICAL_OPTION},n[c]]);break;case 45:this.$=n[c-3].concat([{type:"and",parText:r.parseMessage(n[c-1]),signalType:r.LINETYPE.PAR_AND},n[c]]);break;case 47:this.$=n[c-3].concat([{type:"else",altText:r.parseMessage(n[c-1]),signalType:r.LINETYPE.ALT_ELSE},n[c]]);break;case 48:n[c-3].draw="participant";n[c-3].type="addParticipant";n[c-3].description=r.parseMessage(n[c-1]);this.$=n[c-3];break;case 49:n[c-1].draw="participant";n[c-1].type="addParticipant";this.$=n[c-1];break;case 50:n[c-3].draw="actor";n[c-3].type="addParticipant";n[c-3].description=r.parseMessage(n[c-1]);this.$=n[c-3];break;case 51:n[c-1].draw="actor";n[c-1].type="addParticipant";this.$=n[c-1];break;case 52:n[c-1].type="destroyParticipant";this.$=n[c-1];break;case 53:this.$=[n[c-1],{type:"addNote",placement:n[c-2],actor:n[c-1].actor,text:n[c]}];break;case 54:n[c-2]=[].concat(n[c-1],n[c-1]).slice(0,2);n[c-2][0]=n[c-2][0].actor;n[c-2][1]=n[c-2][1].actor;this.$=[n[c-1],{type:"addNote",placement:r.PLACEMENT.OVER,actor:n[c-2].slice(0,2),text:n[c]}];break;case 55:this.$=[n[c-1],{type:"addLinks",actor:n[c-1].actor,text:n[c]}];break;case 56:this.$=[n[c-1],{type:"addALink",actor:n[c-1].actor,text:n[c]}];break;case 57:this.$=[n[c-1],{type:"addProperties",actor:n[c-1].actor,text:n[c]}];break;case 58:this.$=[n[c-1],{type:"addDetails",actor:n[c-1].actor,text:n[c]}];break;case 61:this.$=[n[c-2],n[c]];break;case 62:this.$=n[c];break;case 63:this.$=r.PLACEMENT.LEFTOF;break;case 64:this.$=r.PLACEMENT.RIGHTOF;break;case 65:this.$=[n[c-4],n[c-1],{type:"addMessage",from:n[c-4].actor,to:n[c-1].actor,signalType:n[c-3],msg:n[c],activate:true},{type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:n[c-1].actor}];break;case 66:this.$=[n[c-4],n[c-1],{type:"addMessage",from:n[c-4].actor,to:n[c-1].actor,signalType:n[c-3],msg:n[c]},{type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:n[c-4].actor}];break;case 67:this.$=[n[c-3],n[c-1],{type:"addMessage",from:n[c-3].actor,to:n[c-1].actor,signalType:n[c-2],msg:n[c]}];break;case 68:this.$={type:"addParticipant",actor:n[c]};break;case 69:this.$=r.LINETYPE.SOLID_OPEN;break;case 70:this.$=r.LINETYPE.DOTTED_OPEN;break;case 71:this.$=r.LINETYPE.SOLID;break;case 72:this.$=r.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=r.LINETYPE.DOTTED;break;case 74:this.$=r.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=r.LINETYPE.SOLID_CROSS;break;case 76:this.$=r.LINETYPE.DOTTED_CROSS;break;case 77:this.$=r.LINETYPE.SOLID_POINT;break;case 78:this.$=r.LINETYPE.DOTTED_POINT;break;case 79:this.$=r.parseMessage(n[c].trim().substring(1));break}}),"anonymous"),table:[{3:1,4:e,5:a,6:s},{1:[3]},{3:5,4:e,5:a,6:s},{3:6,4:e,5:a,6:s},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:o,8:8,9:10,12:12,13:c,14:l,17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},t(R,[2,5]),{9:47,12:12,13:c,14:l,17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},t(R,[2,7]),t(R,[2,8]),t(R,[2,14]),{12:48,50:L,52:_,53:P},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:O},{22:55,70:O},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(R,[2,29]),t(R,[2,30]),{32:[1,61]},{34:[1,62]},t(R,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:O},{22:72,70:O},{22:73,70:O},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:O},{22:90,70:O},{22:91,70:O},{22:92,70:O},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(R,[2,6]),t(R,[2,15]),t(Y,[2,9],{10:93}),t(R,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(R,[2,21]),{5:[1,97]},{5:[1,98]},t(R,[2,24]),t(R,[2,25]),t(R,[2,26]),t(R,[2,27]),t(R,[2,28]),t(R,[2,31]),t(R,[2,32]),t(K,r,{7:99}),t(K,r,{7:100}),t(K,r,{7:101}),t(C,r,{40:102,7:103}),t(B,r,{42:104,7:105}),t(B,r,{7:105,42:106}),t($,r,{45:107,7:108}),t(K,r,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:O},t(V,[2,69]),t(V,[2,70]),t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),t(V,[2,77]),t(V,[2,78]),{22:118,70:O},{22:120,58:119,70:O},{70:[2,63]},{70:[2,64]},{56:121,81:F},{56:123,81:F},{56:124,81:F},{56:125,81:F},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:L,52:_,53:P},{5:[1,131]},t(R,[2,19]),t(R,[2,20]),t(R,[2,22]),t(R,[2,23]),{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,132],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,133],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,134],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{16:[1,135]},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,46],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,49:[1,136],50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{16:[1,137]},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,44],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,48:[1,138],50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{16:[1,139]},{16:[1,140]},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,42],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,47:[1,141],50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,142],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:f,33:x,35:y,36:b,37:m,38:T,39:E,41:w,43:v,44:k,46:I,50:L,52:_,53:P,54:A,59:N,60:M,61:D,62:S,70:O},{15:[1,143]},t(R,[2,49]),{15:[1,144]},t(R,[2,51]),t(R,[2,52]),{22:145,70:O},{22:146,70:O},{56:147,81:F},{56:148,81:F},{56:149,81:F},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(R,[2,16]),t(Y,[2,10]),{12:151,50:L,52:_,53:P},t(Y,[2,12]),t(Y,[2,13]),t(R,[2,18]),t(R,[2,34]),t(R,[2,35]),t(R,[2,36]),t(R,[2,37]),{15:[1,152]},t(R,[2,38]),{15:[1,153]},t(R,[2,39]),t(R,[2,40]),{15:[1,154]},t(R,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:F},{56:158,81:F},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:O},t(Y,[2,11]),t(C,r,{7:103,40:160}),t(B,r,{7:105,42:161}),t($,r,{7:108,45:162}),t(R,[2,48]),t(R,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:(0,n.K2)((function t(e,a){if(a.recoverable){this.trace(e)}else{var s=new Error(e);s.hash=a;throw s}}),"parseError"),parse:(0,n.K2)((function t(e){var a=this,s=[0],r=[],i=[null],o=[],c=this.table,l="",d=0,h=0,p=0,g=2,u=1;var f=o.slice.call(arguments,1);var x=Object.create(this.lexer);var y={yy:{}};for(var b in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,b)){y.yy[b]=this.yy[b]}}x.setInput(e,y.yy);y.yy.lexer=x;y.yy.parser=this;if(typeof x.yylloc=="undefined"){x.yylloc={}}var m=x.yylloc;o.push(m);var T=x.options&&x.options.ranges;if(typeof y.yy.parseError==="function"){this.parseError=y.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function E(t){s.length=s.length-2*t;i.length=i.length-t;o.length=o.length-t}(0,n.K2)(E,"popStack");function w(){var t;t=r.pop()||x.lex()||u;if(typeof t!=="number"){if(t instanceof Array){r=t;t=r.pop()}t=a.symbols_[t]||t}return t}(0,n.K2)(w,"lex");var v,k,I,L,_,P,A={},N,M,D,S;while(true){I=s[s.length-1];if(this.defaultActions[I]){L=this.defaultActions[I]}else{if(v===null||typeof v=="undefined"){v=w()}L=c[I]&&c[I][v]}if(typeof L==="undefined"||!L.length||!L[0]){var O="";S=[];for(N in c[I]){if(this.terminals_[N]&&N>g){S.push("'"+this.terminals_[N]+"'")}}if(x.showPosition){O="Parse error on line "+(d+1)+":\n"+x.showPosition()+"\nExpecting "+S.join(", ")+", got '"+(this.terminals_[v]||v)+"'"}else{O="Parse error on line "+(d+1)+": Unexpected "+(v==u?"end of input":"'"+(this.terminals_[v]||v)+"'")}this.parseError(O,{text:x.match,token:this.terminals_[v]||v,line:x.yylineno,loc:m,expected:S})}if(L[0]instanceof Array&&L.length>1){throw new Error("Parse Error: multiple actions possible at state: "+I+", token: "+v)}switch(L[0]){case 1:s.push(v);i.push(x.yytext);o.push(x.yylloc);s.push(L[1]);v=null;if(!k){h=x.yyleng;l=x.yytext;d=x.yylineno;m=x.yylloc;if(p>0){p--}}else{v=k;k=null}break;case 2:M=this.productions_[L[1]][1];A.$=i[i.length-M];A._$={first_line:o[o.length-(M||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(M||1)].first_column,last_column:o[o.length-1].last_column};if(T){A._$.range=[o[o.length-(M||1)].range[0],o[o.length-1].range[1]]}P=this.performAction.apply(A,[l,h,d,y.yy,L[1],i,o].concat(f));if(typeof P!=="undefined"){return P}if(M){s=s.slice(0,-1*M*2);i=i.slice(0,-1*M);o=o.slice(0,-1*M)}s.push(this.productions_[L[1]][0]);i.push(A.$);o.push(A._$);D=c[s[s.length-2]][s[s.length-1]];s.push(D);break;case 3:return true}}return true}),"parse")};var q=function(){var t={EOF:1,parseError:(0,n.K2)((function t(e,a){if(this.yy.parser){this.yy.parser.parseError(e,a)}else{throw new Error(e)}}),"parseError"),setInput:(0,n.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,n.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,n.K2)((function(t){var e=t.length;var a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(a.length-1){this.yylineno-=a.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===s.length?this.yylloc.first_column:0)+s[s.length-a.length].length-a[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,n.K2)((function(){this._more=true;return this}),"more"),reject:(0,n.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,n.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,n.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,n.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,n.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,n.K2)((function(t,e){var a,s,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}s=t[0].match(/(?:\r\n?|\n).*/g);if(s){this.yylineno+=s.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(a){return a}else if(this._backtrack){for(var i in r){this[i]=r[i]}return false}return false}),"test_match"),next:(0,n.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,a,s;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var i=0;i<r.length;i++){a=this._input.match(this.rules[r[i]]);if(a&&(!e||a[0].length>e[0].length)){e=a;s=i;if(this.options.backtrack_lexer){t=this.test_match(a,r[i]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[s]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,n.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,n.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,n.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,n.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,n.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,n.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,n.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,n.K2)((function t(e,a,s,r){var i=r;switch(s){case 0:return 5;break;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;break;case 7:this.begin("LINE");return 14;break;case 8:this.begin("ID");return 50;break;case 9:this.begin("ID");return 52;break;case 10:return 13;break;case 11:this.begin("ID");return 53;break;case 12:a.yytext=a.yytext.trim();this.begin("ALIAS");return 70;break;case 13:this.popState();this.popState();this.begin("LINE");return 51;break;case 14:this.popState();this.popState();return 5;break;case 15:this.begin("LINE");return 36;break;case 16:this.begin("LINE");return 37;break;case 17:this.begin("LINE");return 38;break;case 18:this.begin("LINE");return 39;break;case 19:this.begin("LINE");return 49;break;case 20:this.begin("LINE");return 41;break;case 21:this.begin("LINE");return 43;break;case 22:this.begin("LINE");return 48;break;case 23:this.begin("LINE");return 44;break;case 24:this.begin("LINE");return 47;break;case 25:this.begin("LINE");return 46;break;case 26:this.popState();return 15;break;case 27:return 16;break;case 28:return 65;break;case 29:return 66;break;case 30:return 59;break;case 31:return 60;break;case 32:return 61;break;case 33:return 62;break;case 34:return 57;break;case 35:return 54;break;case 36:this.begin("ID");return 21;break;case 37:this.begin("ID");return 23;break;case 38:return 29;break;case 39:return 30;break;case 40:this.begin("acc_title");return 31;break;case 41:this.popState();return"acc_title_value";break;case 42:this.begin("acc_descr");return 33;break;case 43:this.popState();return"acc_descr_value";break;case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";break;case 47:return 6;break;case 48:return 18;break;case 49:return 20;break;case 50:return 64;break;case 51:return 5;break;case 52:a.yytext=a.yytext.trim();return 70;break;case 53:return 73;break;case 54:return 74;break;case 55:return 75;break;case 56:return 76;break;case 57:return 71;break;case 58:return 72;break;case 59:return 77;break;case 60:return 78;break;case 61:return 79;break;case 62:return 80;break;case 63:return 81;break;case 64:return 68;break;case 65:return 69;break;case 66:return 5;break;case 67:return"INVALID";break}}),"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:false},acc_descr:{rules:[43],inclusive:false},acc_title:{rules:[41],inclusive:false},ID:{rules:[2,3,12],inclusive:false},ALIAS:{rules:[2,3,13,14],inclusive:false},LINE:{rules:[2,3,26],inclusive:false},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:true}}};return t}();W.lexer=q;function z(){this.yy={}}(0,n.K2)(z,"Parser");z.prototype=W;W.Parser=z;return new z}();l.parser=l;var d=l;var h={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34};var p={FILLED:0,OPEN:1};var g={LEFTOF:0,RIGHTOF:1,OVER:2};var u=class{constructor(){this.state=new r.m((()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:false,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})));this.setAccTitle=n.SV;this.setAccDescription=n.EI;this.setDiagramTitle=n.ke;this.getAccTitle=n.iN;this.getAccDescription=n.m7;this.getDiagramTitle=n.ab;this.apply=this.apply.bind(this);this.parseBoxData=this.parseBoxData.bind(this);this.parseMessage=this.parseMessage.bind(this);this.clear();this.setWrap((0,n.D7)().wrap);this.LINETYPE=h;this.ARROWTYPE=p;this.PLACEMENT=g}static{(0,n.K2)(this,"SequenceDB")}addBox(t){this.state.records.boxes.push({name:t.text,wrap:t.wrap??this.autoWrap(),fill:t.color,actorKeys:[]});this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,e,a,s){let r=this.state.records.currentBox;const i=this.state.records.actors.get(t);if(i){if(this.state.records.currentBox&&i.box&&this.state.records.currentBox!==i.box){throw new Error(`A same participant should only be defined in one Box: ${i.name} can't be in '${i.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`)}r=i.box?i.box:this.state.records.currentBox;i.box=r;if(i&&e===i.name&&a==null){return}}if(a?.text==null){a={text:e,type:s}}if(s==null||a.text==null){a={text:e,type:s}}this.state.records.actors.set(t,{box:r,name:e,description:a.text,wrap:a.wrap??this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:s??"participant"});if(this.state.records.prevActor){const e=this.state.records.actors.get(this.state.records.prevActor);if(e){e.nextActor=t}}if(this.state.records.currentBox){this.state.records.currentBox.actorKeys.push(t)}this.state.records.prevActor=t}activationCount(t){let e;let a=0;if(!t){return 0}for(e=0;e<this.state.records.messages.length;e++){if(this.state.records.messages[e].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[e].from===t){a++}if(this.state.records.messages[e].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[e].from===t){a--}}return a}addMessage(t,e,a,s){this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:a.text,wrap:a.wrap??this.autoWrap(),answer:s})}addSignal(t,e,a,s,r=false){if(s===this.LINETYPE.ACTIVE_END){const e=this.activationCount(t??"");if(e<1){const e=new Error("Trying to inactivate an inactive participant ("+t+")");e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]};throw e}}this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:a?.text??"",wrap:a?.wrap??this.autoWrap(),type:s,activate:r});return true}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some((t=>t.name))}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=true}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=false}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(t===void 0){return{}}t=t.trim();const e=/^:?wrap:/.exec(t)!==null?true:/^:?nowrap:/.exec(t)!==null?false:void 0;const a=(e===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim();return{cleanedText:a,wrap:e}}autoWrap(){if(this.state.records.wrapEnabled!==void 0){return this.state.records.wrapEnabled}return(0,n.D7)().sequence?.wrap??false}clear(){this.state.reset();(0,n.IU)()}parseMessage(t){const e=t.trim();const{wrap:a,cleanedText:s}=this.extractWrap(e);const r={text:s,wrap:a};n.Rm.debug(`parseMessage: ${JSON.stringify(r)}`);return r}parseBoxData(t){const e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t);let a=e?.[1]?e[1].trim():"transparent";let s=e?.[2]?e[2].trim():void 0;if(window?.CSS){if(!window.CSS.supports("color",a)){a="transparent";s=t.trim()}}else{const e=(new Option).style;e.color=a;if(e.color!==a){a="transparent";s=t.trim()}}const{wrap:r,cleanedText:i}=this.extractWrap(s);return{text:i?(0,n.jZ)(i,(0,n.D7)()):void 0,color:a,wrap:r}}addNote(t,e,a){const s={actor:t,placement:e,message:a.text,wrap:a.wrap??this.autoWrap()};const r=[].concat(t,t);this.state.records.notes.push(s);this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:r[0],to:r[1],message:a.text,wrap:a.wrap??this.autoWrap(),type:this.LINETYPE.NOTE,placement:e})}addLinks(t,e){const a=this.getActor(t);try{let t=(0,n.jZ)(e.text,(0,n.D7)());t=t.replace(/&equals;/g,"=");t=t.replace(/&amp;/g,"&");const s=JSON.parse(t);this.insertLinks(a,s)}catch(s){n.Rm.error("error while parsing actor link text",s)}}addALink(t,e){const a=this.getActor(t);try{const t={};let s=(0,n.jZ)(e.text,(0,n.D7)());const r=s.indexOf("@");s=s.replace(/&equals;/g,"=");s=s.replace(/&amp;/g,"&");const i=s.slice(0,r-1).trim();const o=s.slice(r+1).trim();t[i]=o;this.insertLinks(a,t)}catch(s){n.Rm.error("error while parsing actor link text",s)}}insertLinks(t,e){if(t.links==null){t.links=e}else{for(const a in e){t.links[a]=e[a]}}}addProperties(t,e){const a=this.getActor(t);try{const t=(0,n.jZ)(e.text,(0,n.D7)());const s=JSON.parse(t);this.insertProperties(a,s)}catch(s){n.Rm.error("error while parsing actor properties text",s)}}insertProperties(t,e){if(t.properties==null){t.properties=e}else{for(const a in e){t.properties[a]=e[a]}}}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,e){const a=this.getActor(t);const s=document.getElementById(e.text);try{const t=s.innerHTML;const e=JSON.parse(t);if(e.properties){this.insertProperties(a,e.properties)}if(e.links){this.insertLinks(a,e.links)}}catch(r){n.Rm.error("error while parsing actor details text",r)}}getActorProperty(t,e){if(t?.properties!==void 0){return t.properties[e]}return void 0}apply(t){if(Array.isArray(t)){t.forEach((t=>{this.apply(t)}))}else{switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:false,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor)){throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior")}this.state.records.lastCreated=t.actor;this.addActor(t.actor,t.actor,t.description,t.draw);this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor;this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated){throw new Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.")}else{this.state.records.lastCreated=void 0}}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed){throw new Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.")}else{this.state.records.lastDestroyed=void 0}}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"rectEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"optEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"altStart":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"altEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":(0,n.SV)(t.text);break;case"parStart":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"parEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break}}}getConfig(){return(0,n.D7)().sequence}};var f=(0,n.K2)((t=>`.actor {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${t.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${t.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${t.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${t.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${t.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .messageText {\n    fill: ${t.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${t.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${t.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${t.noteBorderColor};\n    fill: ${t.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${t.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${t.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n    stroke-width: 2px;\n  }\n`),"getStyles");var x=f;var y=18*2;var b="actor-top";var m="actor-bottom";var T="actor-box";var E="actor-man";var w=(0,n.K2)((function(t,e){return(0,s.tk)(t,e)}),"drawRect");var v=(0,n.K2)((function(t,e,a,s,r){if(e.links===void 0||e.links===null||Object.keys(e.links).length===0){return{height:0,width:0}}const i=e.links;const n=e.actorCnt;const o=e.rectData;var l="none";if(r){l="block !important"}const d=t.append("g");d.attr("id","actor"+n+"_popup");d.attr("class","actorPopupMenu");d.attr("display",l);var h="";if(o.class!==void 0){h=" "+o.class}let p=o.width>a?o.width:a;const g=d.append("rect");g.attr("class","actorPopupMenuPanel"+h);g.attr("x",o.x);g.attr("y",o.height);g.attr("fill",o.fill);g.attr("stroke",o.stroke);g.attr("width",p);g.attr("height",o.height);g.attr("rx",o.rx);g.attr("ry",o.ry);if(i!=null){var u=20;for(let t in i){var f=d.append("a");var x=(0,c.J)(i[t]);f.attr("xlink:href",x);f.attr("target","_blank");U(s)(t,f,o.x+10,o.height+u,p,20,{class:"actor"},s);u+=30}}g.attr("height",u);return{height:o.height+u,width:p}}),"drawPopup");var k=(0,n.K2)((function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"}),"popupMenuToggle");var I=(0,n.K2)((async function(t,e,a=null){let s=t.append("foreignObject");const r=await(0,n.VJ)(e.text,(0,n.zj)());const i=s.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(r);const o=i.node().getBoundingClientRect();s.attr("height",Math.round(o.height)).attr("width",Math.round(o.width));if(e.class==="noteText"){const a=t.node().firstChild;a.setAttribute("height",o.height+2*e.textMargin);const r=a.getBBox();s.attr("x",Math.round(r.x+r.width/2-o.width/2)).attr("y",Math.round(r.y+r.height/2-o.height/2))}else if(a){let{startx:t,stopx:r,starty:i}=a;if(t>r){const e=t;t=r;r=e}s.attr("x",Math.round(t+Math.abs(t-r)/2-o.width/2));if(e.class==="loopText"){s.attr("y",Math.round(i))}else{s.attr("y",Math.round(i-o.height))}}return[s]}),"drawKatex");var L=(0,n.K2)((function(t,e){let a=0;let s=0;const r=e.text.split(n.Y2.lineBreakRegex);const[o,c]=(0,i.I5)(e.fontSize);let l=[];let d=0;let h=(0,n.K2)((()=>e.y),"yfunc");if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0){switch(e.valign){case"top":case"start":h=(0,n.K2)((()=>Math.round(e.y+e.textMargin)),"yfunc");break;case"middle":case"center":h=(0,n.K2)((()=>Math.round(e.y+(a+s+e.textMargin)/2)),"yfunc");break;case"bottom":case"end":h=(0,n.K2)((()=>Math.round(e.y+(a+s+2*e.textMargin)-e.textMargin)),"yfunc");break}}if(e.anchor!==void 0&&e.textMargin!==void 0&&e.width!==void 0){switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin);e.anchor="start";e.dominantBaseline="middle";e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2);e.anchor="middle";e.dominantBaseline="middle";e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin);e.anchor="end";e.dominantBaseline="middle";e.alignmentBaseline="middle";break}}for(let[n,p]of r.entries()){if(e.textMargin!==void 0&&e.textMargin===0&&o!==void 0){d=n*o}const r=t.append("text");r.attr("x",e.x);r.attr("y",h());if(e.anchor!==void 0){r.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline)}if(e.fontFamily!==void 0){r.style("font-family",e.fontFamily)}if(c!==void 0){r.style("font-size",c)}if(e.fontWeight!==void 0){r.style("font-weight",e.fontWeight)}if(e.fill!==void 0){r.attr("fill",e.fill)}if(e.class!==void 0){r.attr("class",e.class)}if(e.dy!==void 0){r.attr("dy",e.dy)}else if(d!==0){r.attr("dy",d)}const g=p||i.pe;if(e.tspan){const t=r.append("tspan");t.attr("x",e.x);if(e.fill!==void 0){t.attr("fill",e.fill)}t.text(g)}else{r.text(g)}if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0){s+=(r._groups||r)[0][0].getBBox().height;a=s}l.push(r)}return l}),"drawText");var _=(0,n.K2)((function(t,e){function a(t,e,a,s,r){return t+","+e+" "+(t+a)+","+e+" "+(t+a)+","+(e+s-r)+" "+(t+a-r*1.2)+","+(e+s)+" "+t+","+(e+s)}(0,n.K2)(a,"genPoints");const s=t.append("polygon");s.attr("points",a(e.x,e.y,e.width,e.height,7));s.attr("class","labelBox");e.y=e.y+e.height/2;L(t,e);return s}),"drawLabel");var P=-1;var A=(0,n.K2)(((t,e,a,s)=>{if(!t.select){return}a.forEach((a=>{const r=e.get(a);const i=t.select("#actor"+r.actorCnt);if(!s.mirrorActors&&r.stopy){i.attr("y2",r.stopy+r.height/2)}else if(s.mirrorActors){i.attr("y2",r.stopy)}}))}),"fixLifeLineHeights");var N=(0,n.K2)((function(t,e,a,r){const i=r?e.stopy:e.starty;const o=e.x+e.width/2;const c=i+e.height;const l=t.append("g").lower();var d=l;if(!r){P++;if(Object.keys(e.links||{}).length&&!a.forceMenus){d.attr("onclick",k(`actor${P}_popup`)).attr("cursor","pointer")}d.append("line").attr("id","actor"+P).attr("x1",o).attr("y1",c).attr("x2",o).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name);d=l.append("g");e.actorCnt=P;if(e.links!=null){d.attr("id","root-"+P)}}const h=(0,s.PB)();var p="actor";if(e.properties?.class){p=e.properties.class}else{h.fill="#eaeaea"}if(r){p+=` ${m}`}else{p+=` ${b}`}h.x=e.x;h.y=i;h.width=e.width;h.height=e.height;h.class=p;h.rx=3;h.ry=3;h.name=e.name;const g=w(d,h);e.rectData=h;if(e.properties?.icon){const t=e.properties.icon.trim();if(t.charAt(0)==="@"){(0,s.CP)(d,h.x+h.width-20,h.y+10,t.substr(1))}else{(0,s.aC)(d,h.x+h.width-20,h.y+10,t)}}j(a,(0,n.Wi)(e.description))(e.description,d,h.x,h.y,h.width,h.height,{class:`actor ${T}`},a);let u=e.height;if(g.node){const t=g.node().getBBox();e.height=t.height;u=t.height}return u}),"drawActorTypeParticipant");var M=(0,n.K2)((function(t,e,a,r){const i=r?e.stopy:e.starty;const o=e.x+e.width/2;const c=i+80;const l=t.append("g").lower();if(!r){P++;l.append("line").attr("id","actor"+P).attr("x1",o).attr("y1",c).attr("x2",o).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name);e.actorCnt=P}const d=t.append("g");let h=E;if(r){h+=` ${m}`}else{h+=` ${b}`}d.attr("class",h);d.attr("name",e.name);const p=(0,s.PB)();p.x=e.x;p.y=i;p.fill="#eaeaea";p.width=e.width;p.height=e.height;p.class="actor";p.rx=3;p.ry=3;d.append("line").attr("id","actor-man-torso"+P).attr("x1",o).attr("y1",i+25).attr("x2",o).attr("y2",i+45);d.append("line").attr("id","actor-man-arms"+P).attr("x1",o-y/2).attr("y1",i+33).attr("x2",o+y/2).attr("y2",i+33);d.append("line").attr("x1",o-y/2).attr("y1",i+60).attr("x2",o).attr("y2",i+45);d.append("line").attr("x1",o).attr("y1",i+45).attr("x2",o+y/2-2).attr("y2",i+60);const g=d.append("circle");g.attr("cx",e.x+e.width/2);g.attr("cy",i+10);g.attr("r",15);g.attr("width",e.width);g.attr("height",e.height);const u=d.node().getBBox();e.height=u.height;j(a,(0,n.Wi)(e.description))(e.description,d,p.x,p.y+35,p.width,p.height,{class:`actor ${E}`},a);return e.height}),"drawActorTypeActor");var D=(0,n.K2)((async function(t,e,a,s){switch(e.type){case"actor":return await M(t,e,a,s);case"participant":return await N(t,e,a,s)}}),"drawActor");var S=(0,n.K2)((function(t,e,a){const s=t.append("g");const r=s;K(r,e);if(e.name){j(a)(e.name,r,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a)}r.lower()}),"drawBox");var O=(0,n.K2)((function(t){return t.append("g")}),"anchorElement");var R=(0,n.K2)((function(t,e,a,r,i){const n=(0,s.PB)();const o=e.anchored;n.x=e.startx;n.y=e.starty;n.class="activation"+i%3;n.width=e.stopx-e.startx;n.height=a-e.starty;w(o,n)}),"drawActivation");var Y=(0,n.K2)((async function(t,e,a,r){const{boxMargin:i,boxTextMargin:o,labelBoxHeight:c,labelBoxWidth:l,messageFontFamily:d,messageFontSize:h,messageFontWeight:p}=r;const g=t.append("g");const u=(0,n.K2)((function(t,e,a,s){return g.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",s).attr("class","loopLine")}),"drawLoopLine");u(e.startx,e.starty,e.stopx,e.starty);u(e.stopx,e.starty,e.stopx,e.stopy);u(e.startx,e.stopy,e.stopx,e.stopy);u(e.startx,e.starty,e.startx,e.stopy);if(e.sections!==void 0){e.sections.forEach((function(t){u(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")}))}let f=(0,s.HT)();f.text=a;f.x=e.startx;f.y=e.starty;f.fontFamily=d;f.fontSize=h;f.fontWeight=p;f.anchor="middle";f.valign="middle";f.tspan=false;f.width=l||50;f.height=c||20;f.textMargin=o;f.class="labelText";_(g,f);f=z();f.text=e.title;f.x=e.startx+l/2+(e.stopx-e.startx)/2;f.y=e.starty+i+o;f.anchor="middle";f.valign="middle";f.textMargin=o;f.class="loopText";f.fontFamily=d;f.fontSize=h;f.fontWeight=p;f.wrap=true;let x=(0,n.Wi)(f.text)?await I(g,f,e):L(g,f);if(e.sectionTitles!==void 0){for(const[t,a]of Object.entries(e.sectionTitles)){if(a.message){f.text=a.message;f.x=e.startx+(e.stopx-e.startx)/2;f.y=e.sections[t].y+i+o;f.class="loopText";f.anchor="middle";f.valign="middle";f.tspan=false;f.fontFamily=d;f.fontSize=h;f.fontWeight=p;f.wrap=e.wrap;if((0,n.Wi)(f.text)){e.starty=e.sections[t].y;await I(g,f,e)}else{L(g,f)}let s=Math.round(x.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));e.sections[t].height+=s-(i+o)}}}e.height=Math.round(e.stopy-e.starty);return g}),"drawLoop");var K=(0,n.K2)((function(t,e){(0,s.lC)(t,e)}),"drawBackgroundRect");var C=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")}),"insertDatabaseIcon");var B=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")}),"insertComputerIcon");var $=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")}),"insertClockIcon");var V=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")}),"insertArrowHead");var F=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")}),"insertArrowFilledHead");var W=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)}),"insertSequenceNumber");var q=(0,n.K2)((function(t){const e=t.append("defs");const a=e.append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5);a.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")}),"insertArrowCrossHead");var z=(0,n.K2)((function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:true,valign:void 0}}),"getTextObj");var H=(0,n.K2)((function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}}),"getNoteRect");var j=function(){function t(t,e,a,s,i,n,o){const c=e.append("text").attr("x",a+i/2).attr("y",s+n/2+5).style("text-anchor","middle").text(t);r(c,o)}(0,n.K2)(t,"byText");function e(t,e,a,s,o,c,l,d){const{actorFontSize:h,actorFontFamily:p,actorFontWeight:g}=d;const[u,f]=(0,i.I5)(h);const x=t.split(n.Y2.lineBreakRegex);for(let i=0;i<x.length;i++){const t=i*u-u*(x.length-1)/2;const n=e.append("text").attr("x",a+o/2).attr("y",s).style("text-anchor","middle").style("font-size",f).style("font-weight",g).style("font-family",p);n.append("tspan").attr("x",a+o/2).attr("dy",t).text(x[i]);n.attr("y",s+c/2).attr("dominant-baseline","central").attr("alignment-baseline","central");r(n,l)}}(0,n.K2)(e,"byTspan");function a(t,a,s,i,n,o,c,l){const d=a.append("switch");const h=d.append("foreignObject").attr("x",s).attr("y",i).attr("width",n).attr("height",o);const p=h.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");p.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,d,s,i,n,o,c,l);r(p,c)}(0,n.K2)(a,"byFo");async function s(t,a,s,i,o,c,l,d){const h=await(0,n.Dl)(t,(0,n.zj)());const p=a.append("switch");const g=p.append("foreignObject").attr("x",s+o/2-h.width/2).attr("y",i+c/2-h.height/2).attr("width",h.width).attr("height",h.height);const u=g.append("xhtml:div").style("height","100%").style("width","100%");u.append("div").style("text-align","center").style("vertical-align","middle").html(await(0,n.VJ)(t,(0,n.zj)()));e(t,p,s,i,o,c,l,d);r(u,l)}(0,n.K2)(s,"byKatex");function r(t,e){for(const a in e){if(e.hasOwnProperty(a)){t.attr(a,e[a])}}}(0,n.K2)(r,"_setTextAttrs");return function(r,i=false){if(i){return s}return r.textPlacement==="fo"?a:r.textPlacement==="old"?t:e}}();var U=function(){function t(t,e,a,r,i,n,o){const c=e.append("text").attr("x",a).attr("y",r).style("text-anchor","start").text(t);s(c,o)}(0,n.K2)(t,"byText");function e(t,e,a,r,i,o,c,l){const{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l;const g=t.split(n.Y2.lineBreakRegex);for(let n=0;n<g.length;n++){const t=n*d-d*(g.length-1)/2;const i=e.append("text").attr("x",a).attr("y",r).style("text-anchor","start").style("font-size",d).style("font-weight",p).style("font-family",h);i.append("tspan").attr("x",a).attr("dy",t).text(g[n]);i.attr("y",r+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central");s(i,c)}}(0,n.K2)(e,"byTspan");function a(t,a,r,i,n,o,c,l){const d=a.append("switch");const h=d.append("foreignObject").attr("x",r).attr("y",i).attr("width",n).attr("height",o);const p=h.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");p.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,d,r,i,n,o,c,l);s(p,c)}(0,n.K2)(a,"byFo");function s(t,e){for(const a in e){if(e.hasOwnProperty(a)){t.attr(a,e[a])}}}(0,n.K2)(s,"_setTextAttrs");return function(s){return s.textPlacement==="fo"?a:s.textPlacement==="old"?t:e}}();var X={drawRect:w,drawText:L,drawLabel:_,drawActor:D,drawBox:S,drawPopup:v,anchorElement:O,drawActivation:R,drawLoop:Y,drawBackgroundRect:K,insertArrowHead:V,insertArrowFilledHead:F,insertSequenceNumber:W,insertArrowCrossHead:q,insertDatabaseIcon:C,insertComputerIcon:B,insertClockIcon:$,getTextObj:z,getNoteRect:H,fixLifeLineHeights:A,sanitizeUrl:c.J};var J={};var G={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:(0,n.K2)((function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map((t=>t.height||0)))+(this.loops.length===0?0:this.loops.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(this.messages.length===0?0:this.messages.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(this.notes.length===0?0:this.notes.map((t=>t.height||0)).reduce(((t,e)=>t+e)))}),"getHeight"),clear:(0,n.K2)((function(){this.actors=[];this.boxes=[];this.loops=[];this.messages=[];this.notes=[]}),"clear"),addBox:(0,n.K2)((function(t){this.boxes.push(t)}),"addBox"),addActor:(0,n.K2)((function(t){this.actors.push(t)}),"addActor"),addLoop:(0,n.K2)((function(t){this.loops.push(t)}),"addLoop"),addMessage:(0,n.K2)((function(t){this.messages.push(t)}),"addMessage"),addNote:(0,n.K2)((function(t){this.notes.push(t)}),"addNote"),lastActor:(0,n.K2)((function(){return this.actors[this.actors.length-1]}),"lastActor"),lastLoop:(0,n.K2)((function(){return this.loops[this.loops.length-1]}),"lastLoop"),lastMessage:(0,n.K2)((function(){return this.messages[this.messages.length-1]}),"lastMessage"),lastNote:(0,n.K2)((function(){return this.notes[this.notes.length-1]}),"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:(0,n.K2)((function(){this.sequenceItems=[];this.activations=[];this.models.clear();this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0};this.verticalPos=0;ot((0,n.D7)())}),"init"),updateVal:(0,n.K2)((function(t,e,a,s){if(t[e]===void 0){t[e]=a}else{t[e]=s(a,t[e])}}),"updateVal"),updateBounds:(0,n.K2)((function(t,e,a,s){const r=this;let i=0;function o(o){return(0,n.K2)((function n(c){i++;const l=r.sequenceItems.length-i+1;r.updateVal(c,"starty",e-l*J.boxMargin,Math.min);r.updateVal(c,"stopy",s+l*J.boxMargin,Math.max);r.updateVal(G.data,"startx",t-l*J.boxMargin,Math.min);r.updateVal(G.data,"stopx",a+l*J.boxMargin,Math.max);if(!(o==="activation")){r.updateVal(c,"startx",t-l*J.boxMargin,Math.min);r.updateVal(c,"stopx",a+l*J.boxMargin,Math.max);r.updateVal(G.data,"starty",e-l*J.boxMargin,Math.min);r.updateVal(G.data,"stopy",s+l*J.boxMargin,Math.max)}}),"updateItemBounds")}(0,n.K2)(o,"updateFn");this.sequenceItems.forEach(o());this.activations.forEach(o("activation"))}),"updateBounds"),insert:(0,n.K2)((function(t,e,a,s){const r=n.Y2.getMin(t,a);const i=n.Y2.getMax(t,a);const o=n.Y2.getMin(e,s);const c=n.Y2.getMax(e,s);this.updateVal(G.data,"startx",r,Math.min);this.updateVal(G.data,"starty",o,Math.min);this.updateVal(G.data,"stopx",i,Math.max);this.updateVal(G.data,"stopy",c,Math.max);this.updateBounds(r,o,i,c)}),"insert"),newActivation:(0,n.K2)((function(t,e,a){const s=a.get(t.from);const r=ct(t.from).length||0;const i=s.x+s.width/2+(r-1)*J.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+J.activationWidth,stopy:void 0,actor:t.from,anchored:X.anchorElement(e)})}),"newActivation"),endActivation:(0,n.K2)((function(t){const e=this.activations.map((function(t){return t.actor})).lastIndexOf(t.from);return this.activations.splice(e,1)[0]}),"endActivation"),createLoop:(0,n.K2)((function(t={message:void 0,wrap:false,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}}),"createLoop"),newLoop:(0,n.K2)((function(t={message:void 0,wrap:false,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))}),"newLoop"),endLoop:(0,n.K2)((function(){return this.sequenceItems.pop()}),"endLoop"),isLoopOverlap:(0,n.K2)((function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:false}),"isLoopOverlap"),addSectionToLoop:(0,n.K2)((function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[];e.sectionTitles=e.sectionTitles||[];e.sections.push({y:G.getVerticalPos(),height:0});e.sectionTitles.push(t);this.sequenceItems.push(e)}),"addSectionToLoop"),saveVerticalPos:(0,n.K2)((function(){if(this.isLoopOverlap()){this.savedVerticalPos=this.verticalPos}}),"saveVerticalPos"),resetVerticalPos:(0,n.K2)((function(){if(this.isLoopOverlap()){this.verticalPos=this.savedVerticalPos}}),"resetVerticalPos"),bumpVerticalPos:(0,n.K2)((function(t){this.verticalPos=this.verticalPos+t;this.data.stopy=n.Y2.getMax(this.data.stopy,this.verticalPos)}),"bumpVerticalPos"),getVerticalPos:(0,n.K2)((function(){return this.verticalPos}),"getVerticalPos"),getBounds:(0,n.K2)((function(){return{bounds:this.data,models:this.models}}),"getBounds")};var Z=(0,n.K2)((async function(t,e){G.bumpVerticalPos(J.boxMargin);e.height=J.boxMargin;e.starty=G.getVerticalPos();const a=(0,s.PB)();a.x=e.startx;a.y=e.starty;a.width=e.width||J.width;a.class="note";const r=t.append("g");const i=X.drawRect(r,a);const o=(0,s.HT)();o.x=e.startx;o.y=e.starty;o.width=a.width;o.dy="1em";o.text=e.message;o.class="noteText";o.fontFamily=J.noteFontFamily;o.fontSize=J.noteFontSize;o.fontWeight=J.noteFontWeight;o.anchor=J.noteAlign;o.textMargin=J.noteMargin;o.valign="center";const c=(0,n.Wi)(o.text)?await I(r,o):L(r,o);const l=Math.round(c.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));i.attr("height",l+2*J.noteMargin);e.height+=l+2*J.noteMargin;G.bumpVerticalPos(l+2*J.noteMargin);e.stopy=e.starty+l+2*J.noteMargin;e.stopx=e.startx+a.width;G.insert(e.startx,e.starty,e.stopx,e.stopy);G.models.addNote(e)}),"drawNote");var Q=(0,n.K2)((t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight})),"messageFont");var tt=(0,n.K2)((t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight})),"noteFont");var et=(0,n.K2)((t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight})),"actorFont");async function at(t,e){G.bumpVerticalPos(10);const{startx:a,stopx:s,message:r}=e;const o=n.Y2.splitBreaks(r).length;const c=(0,n.Wi)(r);const l=c?await(0,n.Dl)(r,(0,n.D7)()):i._K.calculateTextDimensions(r,Q(J));if(!c){const t=l.height/o;e.height+=t;G.bumpVerticalPos(t)}let d;let h=l.height-10;const p=l.width;if(a===s){d=G.getVerticalPos()+h;if(!J.rightAngles){h+=J.boxMargin;d=G.getVerticalPos()+h}h+=30;const t=n.Y2.getMax(p/2,J.width/2);G.insert(a-t,G.getVerticalPos()-10+h,s+t,G.getVerticalPos()+30+h)}else{h+=J.boxMargin;d=G.getVerticalPos()+h;G.insert(a,d-10,s,d)}G.bumpVerticalPos(h);e.height+=h;e.stopy=e.starty+e.height;G.insert(e.fromBounds,e.starty,e.toBounds,e.stopy);return d}(0,n.K2)(at,"boundMessage");var st=(0,n.K2)((async function(t,e,a,r){const{startx:o,stopx:c,starty:l,message:d,type:h,sequenceIndex:p,sequenceVisible:g}=e;const u=i._K.calculateTextDimensions(d,Q(J));const f=(0,s.HT)();f.x=o;f.y=l+10;f.width=c-o;f.class="messageText";f.dy="1em";f.text=d;f.fontFamily=J.messageFontFamily;f.fontSize=J.messageFontSize;f.fontWeight=J.messageFontWeight;f.anchor=J.messageAlign;f.valign="center";f.textMargin=J.wrapPadding;f.tspan=false;if((0,n.Wi)(f.text)){await I(t,f,{startx:o,stopx:c,starty:a})}else{L(t,f)}const x=u.width;let y;if(o===c){if(J.rightAngles){y=t.append("path").attr("d",`M  ${o},${a} H ${o+n.Y2.getMax(J.width/2,x/2)} V ${a+25} H ${o}`)}else{y=t.append("path").attr("d","M "+o+","+a+" C "+(o+60)+","+(a-10)+" "+(o+60)+","+(a+30)+" "+o+","+(a+20))}}else{y=t.append("line");y.attr("x1",o);y.attr("y1",a);y.attr("x2",c);y.attr("y2",a)}if(h===r.db.LINETYPE.DOTTED||h===r.db.LINETYPE.DOTTED_CROSS||h===r.db.LINETYPE.DOTTED_POINT||h===r.db.LINETYPE.DOTTED_OPEN||h===r.db.LINETYPE.BIDIRECTIONAL_DOTTED){y.style("stroke-dasharray","3, 3");y.attr("class","messageLine1")}else{y.attr("class","messageLine0")}let b="";if(J.arrowMarkerAbsolute){b=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search;b=b.replace(/\(/g,"\\(");b=b.replace(/\)/g,"\\)")}y.attr("stroke-width",2);y.attr("stroke","none");y.style("fill","none");if(h===r.db.LINETYPE.SOLID||h===r.db.LINETYPE.DOTTED){y.attr("marker-end","url("+b+"#arrowhead)")}if(h===r.db.LINETYPE.BIDIRECTIONAL_SOLID||h===r.db.LINETYPE.BIDIRECTIONAL_DOTTED){y.attr("marker-start","url("+b+"#arrowhead)");y.attr("marker-end","url("+b+"#arrowhead)")}if(h===r.db.LINETYPE.SOLID_POINT||h===r.db.LINETYPE.DOTTED_POINT){y.attr("marker-end","url("+b+"#filled-head)")}if(h===r.db.LINETYPE.SOLID_CROSS||h===r.db.LINETYPE.DOTTED_CROSS){y.attr("marker-end","url("+b+"#crosshead)")}if(g||J.showSequenceNumbers){y.attr("marker-start","url("+b+"#sequencenumber)");t.append("text").attr("x",o).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(p)}}),"drawMessage");var rt=(0,n.K2)((function(t,e,a,s,r,i,o){let c=0;let l=0;let d=void 0;let h=0;for(const p of s){const t=e.get(p);const s=t.box;if(d&&d!=s){if(!o){G.models.addBox(d)}l+=J.boxMargin+d.margin}if(s&&s!=d){if(!o){s.x=c+l;s.y=r}l+=s.margin}t.width=t.width||J.width;t.height=n.Y2.getMax(t.height||J.height,J.height);t.margin=t.margin||J.actorMargin;h=n.Y2.getMax(h,t.height);if(a.get(t.name)){l+=t.width/2}t.x=c+l;t.starty=G.getVerticalPos();G.insert(t.x,r,t.x+t.width,t.height);c+=t.width+l;if(t.box){t.box.width=c+s.margin-t.box.x}l=t.margin;d=t.box;G.models.addActor(t)}if(d&&!o){G.models.addBox(d)}G.bumpVerticalPos(h)}),"addActorRenderingData");var it=(0,n.K2)((async function(t,e,a,s){if(!s){for(const s of a){const a=e.get(s);await X.drawActor(t,a,J,false)}}else{let s=0;G.bumpVerticalPos(J.boxMargin*2);for(const r of a){const a=e.get(r);if(!a.stopy){a.stopy=G.getVerticalPos()}const i=await X.drawActor(t,a,J,true);s=n.Y2.getMax(s,i)}G.bumpVerticalPos(s+J.boxMargin)}}),"drawActors");var nt=(0,n.K2)((function(t,e,a,s){let r=0;let i=0;for(const n of a){const a=e.get(n);const o=ut(a);const c=X.drawPopup(t,a,o,J,J.forceMenus,s);if(c.height>r){r=c.height}if(c.width+a.x>i){i=c.width+a.x}}return{maxHeight:r,maxWidth:i}}),"drawActorsPopup");var ot=(0,n.K2)((function(t){(0,n.hH)(J,t);if(t.fontFamily){J.actorFontFamily=J.noteFontFamily=J.messageFontFamily=t.fontFamily}if(t.fontSize){J.actorFontSize=J.noteFontSize=J.messageFontSize=t.fontSize}if(t.fontWeight){J.actorFontWeight=J.noteFontWeight=J.messageFontWeight=t.fontWeight}}),"setConf");var ct=(0,n.K2)((function(t){return G.activations.filter((function(e){return e.actor===t}))}),"actorActivations");var lt=(0,n.K2)((function(t,e){const a=e.get(t);const s=ct(t);const r=s.reduce((function(t,e){return n.Y2.getMin(t,e.startx)}),a.x+a.width/2-1);const i=s.reduce((function(t,e){return n.Y2.getMax(t,e.stopx)}),a.x+a.width/2+1);return[r,i]}),"activationBounds");function dt(t,e,a,s,r){G.bumpVerticalPos(a);let o=s;if(e.id&&e.message&&t[e.id]){const a=t[e.id].width;const r=Q(J);e.message=i._K.wrapLabel(`[${e.message}]`,a-2*J.wrapPadding,r);e.width=a;e.wrap=true;const c=i._K.calculateTextDimensions(e.message,r);const l=n.Y2.getMax(c.height,J.labelBoxHeight);o=s+l;n.Rm.debug(`${l} - ${e.message}`)}r(e);G.bumpVerticalPos(o)}(0,n.K2)(dt,"adjustLoopHeightForWrap");function ht(t,e,a,s,r,i,o){function c(a,s){if(a.x<r.get(t.from).x){G.insert(e.stopx-s,e.starty,e.startx,e.stopy+a.height/2+J.noteMargin);e.stopx=e.stopx+s}else{G.insert(e.startx,e.starty,e.stopx+s,e.stopy+a.height/2+J.noteMargin);e.stopx=e.stopx-s}}(0,n.K2)(c,"receiverAdjustment");function l(a,s){if(a.x<r.get(t.to).x){G.insert(e.startx-s,e.starty,e.stopx,e.stopy+a.height/2+J.noteMargin);e.startx=e.startx+s}else{G.insert(e.stopx,e.starty,e.startx+s,e.stopy+a.height/2+J.noteMargin);e.startx=e.startx-s}}(0,n.K2)(l,"senderAdjustment");if(i.get(t.to)==s){const e=r.get(t.to);const s=e.type=="actor"?y/2+3:e.width/2+3;c(e,s);e.starty=a-e.height/2;G.bumpVerticalPos(e.height/2)}else if(o.get(t.from)==s){const e=r.get(t.from);if(J.mirrorActors){const t=e.type=="actor"?y/2:e.width/2;l(e,t)}e.stopy=a-e.height/2;G.bumpVerticalPos(e.height/2)}else if(o.get(t.to)==s){const e=r.get(t.to);if(J.mirrorActors){const t=e.type=="actor"?y/2+3:e.width/2+3;c(e,t)}e.stopy=a-e.height/2;G.bumpVerticalPos(e.height/2)}}(0,n.K2)(ht,"adjustCreatedDestroyedData");var pt=(0,n.K2)((async function(t,e,a,s){const{securityLevel:r,sequence:i}=(0,n.D7)();J=i;let c;if(r==="sandbox"){c=(0,o.Ltv)("#i"+e)}const l=r==="sandbox"?(0,o.Ltv)(c.nodes()[0].contentDocument.body):(0,o.Ltv)("body");const d=r==="sandbox"?c.nodes()[0].contentDocument:document;G.init();n.Rm.debug(s.db);const h=r==="sandbox"?l.select(`[id="${e}"]`):(0,o.Ltv)(`[id="${e}"]`);const p=s.db.getActors();const g=s.db.getCreatedActors();const u=s.db.getDestroyedActors();const f=s.db.getBoxes();let x=s.db.getActorKeys();const y=s.db.getMessages();const b=s.db.getDiagramTitle();const m=s.db.hasAtLeastOneBox();const T=s.db.hasAtLeastOneBoxWithTitle();const E=await gt(p,y,s);J.height=await ft(p,E,f);X.insertComputerIcon(h);X.insertDatabaseIcon(h);X.insertClockIcon(h);if(m){G.bumpVerticalPos(J.boxMargin);if(T){G.bumpVerticalPos(f[0].textMaxHeight)}}if(J.hideUnusedParticipants===true){const t=new Set;y.forEach((e=>{t.add(e.from);t.add(e.to)}));x=x.filter((e=>t.has(e)))}rt(h,p,g,x,0,y,false);const w=await bt(y,p,E,s);X.insertArrowHead(h);X.insertArrowCrossHead(h);X.insertArrowFilledHead(h);X.insertSequenceNumber(h);function v(t,e){const a=G.endActivation(t);if(a.starty+18>e){a.starty=e-6;e+=12}X.drawActivation(h,a,e,J,ct(t.from).length);G.insert(a.startx,e-10,a.stopx,e)}(0,n.K2)(v,"activeEnd");let k=1;let I=1;const L=[];const _=[];let P=0;for(const o of y){let t,e,a;switch(o.type){case s.db.LINETYPE.NOTE:G.resetVerticalPos();e=o.noteModel;await Z(h,e);break;case s.db.LINETYPE.ACTIVE_START:G.newActivation(o,h,p);break;case s.db.LINETYPE.ACTIVE_END:v(o,G.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:dt(w,o,J.boxMargin,J.boxMargin+J.boxTextMargin,(t=>G.newLoop(t)));break;case s.db.LINETYPE.LOOP_END:t=G.endLoop();await X.drawLoop(h,t,"loop",J);G.bumpVerticalPos(t.stopy-G.getVerticalPos());G.models.addLoop(t);break;case s.db.LINETYPE.RECT_START:dt(w,o,J.boxMargin,J.boxMargin,(t=>G.newLoop(void 0,t.message)));break;case s.db.LINETYPE.RECT_END:t=G.endLoop();_.push(t);G.models.addLoop(t);G.bumpVerticalPos(t.stopy-G.getVerticalPos());break;case s.db.LINETYPE.OPT_START:dt(w,o,J.boxMargin,J.boxMargin+J.boxTextMargin,(t=>G.newLoop(t)));break;case s.db.LINETYPE.OPT_END:t=G.endLoop();await X.drawLoop(h,t,"opt",J);G.bumpVerticalPos(t.stopy-G.getVerticalPos());G.models.addLoop(t);break;case s.db.LINETYPE.ALT_START:dt(w,o,J.boxMargin,J.boxMargin+J.boxTextMargin,(t=>G.newLoop(t)));break;case s.db.LINETYPE.ALT_ELSE:dt(w,o,J.boxMargin+J.boxTextMargin,J.boxMargin,(t=>G.addSectionToLoop(t)));break;case s.db.LINETYPE.ALT_END:t=G.endLoop();await X.drawLoop(h,t,"alt",J);G.bumpVerticalPos(t.stopy-G.getVerticalPos());G.models.addLoop(t);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:dt(w,o,J.boxMargin,J.boxMargin+J.boxTextMargin,(t=>G.newLoop(t)));G.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:dt(w,o,J.boxMargin+J.boxTextMargin,J.boxMargin,(t=>G.addSectionToLoop(t)));break;case s.db.LINETYPE.PAR_END:t=G.endLoop();await X.drawLoop(h,t,"par",J);G.bumpVerticalPos(t.stopy-G.getVerticalPos());G.models.addLoop(t);break;case s.db.LINETYPE.AUTONUMBER:k=o.message.start||k;I=o.message.step||I;if(o.message.visible){s.db.enableSequenceNumbers()}else{s.db.disableSequenceNumbers()}break;case s.db.LINETYPE.CRITICAL_START:dt(w,o,J.boxMargin,J.boxMargin+J.boxTextMargin,(t=>G.newLoop(t)));break;case s.db.LINETYPE.CRITICAL_OPTION:dt(w,o,J.boxMargin+J.boxTextMargin,J.boxMargin,(t=>G.addSectionToLoop(t)));break;case s.db.LINETYPE.CRITICAL_END:t=G.endLoop();await X.drawLoop(h,t,"critical",J);G.bumpVerticalPos(t.stopy-G.getVerticalPos());G.models.addLoop(t);break;case s.db.LINETYPE.BREAK_START:dt(w,o,J.boxMargin,J.boxMargin+J.boxTextMargin,(t=>G.newLoop(t)));break;case s.db.LINETYPE.BREAK_END:t=G.endLoop();await X.drawLoop(h,t,"break",J);G.bumpVerticalPos(t.stopy-G.getVerticalPos());G.models.addLoop(t);break;default:try{a=o.msgModel;a.starty=G.getVerticalPos();a.sequenceIndex=k;a.sequenceVisible=s.db.showSequenceNumbers();const t=await at(h,a);ht(o,a,t,P,p,g,u);L.push({messageModel:a,lineStartY:t});G.models.addMessage(a)}catch(K){n.Rm.error("error while drawing message",K)}}if([s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT,s.db.LINETYPE.BIDIRECTIONAL_SOLID,s.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(o.type)){k=k+I}P++}n.Rm.debug("createdActors",g);n.Rm.debug("destroyedActors",u);await it(h,p,x,false);for(const n of L){await st(h,n.messageModel,n.lineStartY,s)}if(J.mirrorActors){await it(h,p,x,true)}_.forEach((t=>X.drawBackgroundRect(h,t)));A(h,p,x,J);for(const n of G.models.boxes){n.height=G.getVerticalPos()-n.y;G.insert(n.x,n.y,n.x+n.width,n.height);n.startx=n.x;n.starty=n.y;n.stopx=n.startx+n.width;n.stopy=n.starty+n.height;n.stroke="rgb(0,0,0, 0.5)";X.drawBox(h,n,J)}if(m){G.bumpVerticalPos(J.boxMargin)}const N=nt(h,p,x,d);const{bounds:M}=G.getBounds();if(M.startx===void 0){M.startx=0}if(M.starty===void 0){M.starty=0}if(M.stopx===void 0){M.stopx=0}if(M.stopy===void 0){M.stopy=0}let D=M.stopy-M.starty;if(D<N.maxHeight){D=N.maxHeight}let S=D+2*J.diagramMarginY;if(J.mirrorActors){S=S-J.boxMargin+J.bottomMarginAdj}let O=M.stopx-M.startx;if(O<N.maxWidth){O=N.maxWidth}const R=O+2*J.diagramMarginX;if(b){h.append("text").text(b).attr("x",(M.stopx-M.startx)/2-2*J.diagramMarginX).attr("y",-25)}(0,n.a$)(h,S,R,J.useMaxWidth);const Y=b?40:0;h.attr("viewBox",M.startx-J.diagramMarginX+" -"+(J.diagramMarginY+Y)+" "+R+" "+(S+Y));n.Rm.debug(`models:`,G.models)}),"draw");async function gt(t,e,a){const s={};for(const r of e){if(t.get(r.to)&&t.get(r.from)){const e=t.get(r.to);if(r.placement===a.db.PLACEMENT.LEFTOF&&!e.prevActor){continue}if(r.placement===a.db.PLACEMENT.RIGHTOF&&!e.nextActor){continue}const o=r.placement!==void 0;const c=!o;const l=o?tt(J):Q(J);const d=r.wrap?i._K.wrapLabel(r.message,J.width-2*J.wrapPadding,l):r.message;const h=(0,n.Wi)(d)?await(0,n.Dl)(r.message,(0,n.D7)()):i._K.calculateTextDimensions(d,l);const p=h.width+2*J.wrapPadding;if(c&&r.from===e.nextActor){s[r.to]=n.Y2.getMax(s[r.to]||0,p)}else if(c&&r.from===e.prevActor){s[r.from]=n.Y2.getMax(s[r.from]||0,p)}else if(c&&r.from===r.to){s[r.from]=n.Y2.getMax(s[r.from]||0,p/2);s[r.to]=n.Y2.getMax(s[r.to]||0,p/2)}else if(r.placement===a.db.PLACEMENT.RIGHTOF){s[r.from]=n.Y2.getMax(s[r.from]||0,p)}else if(r.placement===a.db.PLACEMENT.LEFTOF){s[e.prevActor]=n.Y2.getMax(s[e.prevActor]||0,p)}else if(r.placement===a.db.PLACEMENT.OVER){if(e.prevActor){s[e.prevActor]=n.Y2.getMax(s[e.prevActor]||0,p/2)}if(e.nextActor){s[r.from]=n.Y2.getMax(s[r.from]||0,p/2)}}}}n.Rm.debug("maxMessageWidthPerActor:",s);return s}(0,n.K2)(gt,"getMaxMessageWidthPerActor");var ut=(0,n.K2)((function(t){let e=0;const a=et(J);for(const s in t.links){const t=i._K.calculateTextDimensions(s,a);const r=t.width+2*J.wrapPadding+2*J.boxMargin;if(e<r){e=r}}return e}),"getRequiredPopupWidth");async function ft(t,e,a){let s=0;for(const o of t.keys()){const e=t.get(o);if(e.wrap){e.description=i._K.wrapLabel(e.description,J.width-2*J.wrapPadding,et(J))}const a=(0,n.Wi)(e.description)?await(0,n.Dl)(e.description,(0,n.D7)()):i._K.calculateTextDimensions(e.description,et(J));e.width=e.wrap?J.width:n.Y2.getMax(J.width,a.width+2*J.wrapPadding);e.height=e.wrap?n.Y2.getMax(a.height,J.height):J.height;s=n.Y2.getMax(s,e.height)}for(const i in e){const a=t.get(i);if(!a){continue}const s=t.get(a.nextActor);if(!s){const t=e[i];const s=t+J.actorMargin-a.width/2;a.margin=n.Y2.getMax(s,J.actorMargin);continue}const r=e[i];const o=r+J.actorMargin-a.width/2-s.width/2;a.margin=n.Y2.getMax(o,J.actorMargin)}let r=0;a.forEach((e=>{const a=Q(J);let s=e.actorKeys.reduce(((e,a)=>e+=t.get(a).width+(t.get(a).margin||0)),0);s-=2*J.boxTextMargin;if(e.wrap){e.name=i._K.wrapLabel(e.name,s-2*J.wrapPadding,a)}const o=i._K.calculateTextDimensions(e.name,a);r=n.Y2.getMax(o.height,r);const c=n.Y2.getMax(s,o.width+2*J.wrapPadding);e.margin=J.boxTextMargin;if(s<c){const t=(c-s)/2;e.margin+=t}}));a.forEach((t=>t.textMaxHeight=r));return n.Y2.getMax(s,J.height)}(0,n.K2)(ft,"calculateActorMargins");var xt=(0,n.K2)((async function(t,e,a){const s=e.get(t.from);const r=e.get(t.to);const o=s.x;const c=r.x;const l=t.wrap&&t.message;let d=(0,n.Wi)(t.message)?await(0,n.Dl)(t.message,(0,n.D7)()):i._K.calculateTextDimensions(l?i._K.wrapLabel(t.message,J.width,tt(J)):t.message,tt(J));const h={width:l?J.width:n.Y2.getMax(J.width,d.width+2*J.noteMargin),height:0,startx:s.x,stopx:0,starty:0,stopy:0,message:t.message};if(t.placement===a.db.PLACEMENT.RIGHTOF){h.width=l?n.Y2.getMax(J.width,d.width):n.Y2.getMax(s.width/2+r.width/2,d.width+2*J.noteMargin);h.startx=o+(s.width+J.actorMargin)/2}else if(t.placement===a.db.PLACEMENT.LEFTOF){h.width=l?n.Y2.getMax(J.width,d.width+2*J.noteMargin):n.Y2.getMax(s.width/2+r.width/2,d.width+2*J.noteMargin);h.startx=o-h.width+(s.width-J.actorMargin)/2}else if(t.to===t.from){d=i._K.calculateTextDimensions(l?i._K.wrapLabel(t.message,n.Y2.getMax(J.width,s.width),tt(J)):t.message,tt(J));h.width=l?n.Y2.getMax(J.width,s.width):n.Y2.getMax(s.width,J.width,d.width+2*J.noteMargin);h.startx=o+(s.width-h.width)/2}else{h.width=Math.abs(o+s.width/2-(c+r.width/2))+J.actorMargin;h.startx=o<c?o+s.width/2-J.actorMargin/2:c+r.width/2-J.actorMargin/2}if(l){h.message=i._K.wrapLabel(t.message,h.width-2*J.wrapPadding,tt(J))}n.Rm.debug(`NM:[${h.startx},${h.stopx},${h.starty},${h.stopy}:${h.width},${h.height}=${t.message}]`);return h}),"buildNoteModel");var yt=(0,n.K2)((function(t,e,a){if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT,a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)){return{}}const[s,r]=lt(t.from,e);const[o,c]=lt(t.to,e);const l=s<=o;let d=l?r:s;let h=l?o:c;const p=Math.abs(o-c)>2;const g=(0,n.K2)((t=>l?-t:t),"adjustValue");if(t.from===t.to){h=d}else{if(t.activate&&!p){h+=g(J.activationWidth/2-1)}if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN].includes(t.type)){h+=g(3)}if([a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)){d-=g(3)}}const u=[s,r,o,c];const f=Math.abs(d-h);if(t.wrap&&t.message){t.message=i._K.wrapLabel(t.message,n.Y2.getMax(f+2*J.wrapPadding,J.width),Q(J))}const x=i._K.calculateTextDimensions(t.message,Q(J));return{width:n.Y2.getMax(t.wrap?0:x.width+2*J.wrapPadding,f+2*J.wrapPadding,J.width),height:0,startx:d,stopx:h,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,u),toBounds:Math.max.apply(null,u)}}),"buildMessageModel");var bt=(0,n.K2)((async function(t,e,a,s){const r={};const i=[];let o,c,l;for(const d of t){switch(d.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:i.push({id:d.id,msg:d.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:if(d.message){o=i.pop();r[o.id]=o;r[d.id]=o;i.push(o)}break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:o=i.pop();r[o.id]=o;break;case s.db.LINETYPE.ACTIVE_START:{const t=e.get(d.from?d.from:d.to.actor);const a=ct(d.from?d.from:d.to.actor).length;const s=t.x+t.width/2+(a-1)*J.activationWidth/2;const r={startx:s,stopx:s+J.activationWidth,actor:d.from,enabled:true};G.activations.push(r)}break;case s.db.LINETYPE.ACTIVE_END:{const t=G.activations.map((t=>t.actor)).lastIndexOf(d.from);G.activations.splice(t,1).splice(0,1)}break}const t=d.placement!==void 0;if(t){c=await xt(d,e,s);d.noteModel=c;i.forEach((t=>{o=t;o.from=n.Y2.getMin(o.from,c.startx);o.to=n.Y2.getMax(o.to,c.startx+c.width);o.width=n.Y2.getMax(o.width,Math.abs(o.from-o.to))-J.labelBoxWidth}))}else{l=yt(d,e,s);d.msgModel=l;if(l.startx&&l.stopx&&i.length>0){i.forEach((t=>{o=t;if(l.startx===l.stopx){const t=e.get(d.from);const a=e.get(d.to);o.from=n.Y2.getMin(t.x-l.width/2,t.x-t.width/2,o.from);o.to=n.Y2.getMax(a.x+l.width/2,a.x+t.width/2,o.to);o.width=n.Y2.getMax(o.width,Math.abs(o.to-o.from))-J.labelBoxWidth}else{o.from=n.Y2.getMin(l.startx,o.from);o.to=n.Y2.getMax(l.stopx,o.to);o.width=n.Y2.getMax(o.width,l.width)-J.labelBoxWidth}}))}}}G.activations=[];n.Rm.debug("Loop type widths:",r);return r}),"calculateLoopBounds");var mt={bounds:G,drawActors:it,drawActorsPopup:nt,setConf:ot,draw:pt};var Tt={parser:d,get db(){return new u},renderer:mt,styles:x,init:(0,n.K2)((t=>{if(!t.sequence){t.sequence={}}if(t.wrap){t.sequence.wrap=t.wrap;(0,n.XV)({sequence:{wrap:t.wrap}})}}),"init")}}}]);