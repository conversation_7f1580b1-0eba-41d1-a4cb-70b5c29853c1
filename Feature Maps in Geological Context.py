import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.layers import Conv2D

def visualize_feature_maps():
    """Visualize how CNN extracts geological features"""
    
    # Create synthetic geological section
    section = np.zeros((64, 64))
    
    # Add horizontal layers (sedimentary)
    for y in [15, 30, 45]:
        section[y:y+2, :] = 1.0
    
    # Add fault (vertical discontinuity)
    section[:, 30:32] = 0.5
    section[15:17, 30:] += 0.3  # Offset layers
    section[30:32, 30:] += 0.3
    section[45:47, 30:] += 0.3
    
    # Add anticline structure
    x = np.arange(64)
    anticline = 0.3 * np.exp(-((x - 48)**2) / 100)
    for i in range(64):
        shift = int(anticline[i] * 10)
        if shift > 0:
            section[:, i] = np.roll(section[:, i], shift)
    
    # Add noise
    section += np.random.normal(0, 0.05, section.shape)
    
    # Create simple CNN model for feature extraction
    input_layer = tf.keras.layers.Input(shape=(64, 64, 1))
    conv1 = Conv2D(4, (3, 3), activation='relu', padding='same')(input_layer)
    
    # Create model for feature extraction
    feature_model = tf.keras.Model(inputs=input_layer, outputs=conv1)
    
    # Initialize with specific filters
    weights = feature_model.get_weights()
    
    # Horizontal edge detector
    weights[0][:, :, 0, 0] = np.array([[-1, -1, -1], [0, 0, 0], [1, 1, 1]])
    # Vertical edge detector  
    weights[0][:, :, 0, 1] = np.array([[-1, 0, 1], [-1, 0, 1], [-1, 0, 1]])
    # Diagonal detector
    weights[0][:, :, 0, 2] = np.array([[-1, 0, 1], [0, 0, 0], [1, 0, -1]])
    # Blob detector
    weights[0][:, :, 0, 3] = np.array([[0, 1, 0], [1, -4, 1], [0, 1, 0]])
    
    feature_model.set_weights(weights)
    
    # Get feature maps
    features = feature_model.predict(section.reshape(1, 64, 64, 1))
    
    # Visualize
    _, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Original
    axes[0, 0].imshow(section, cmap='seismic')
    axes[0, 0].set_title('Original Geological Section')
    axes[0, 0].set_xlabel('Distance')
    axes[0, 0].set_ylabel('Depth')
    
    # Feature maps
    feature_names = ['Horizontal Layers', 'Vertical Faults', 'Diagonal Features', 'Local Anomalies']
    
    for i in range(4):
        row = (i + 1) // 3
        col = (i + 1) % 3
        if row < 2 and col < 3:
            axes[row, col].imshow(features[0, :, :, i], cmap='viridis')
            axes[row, col].set_title(f'Feature Map: {feature_names[i]}')
    
    # Remove empty subplot
    axes[1, 2].remove()
    
    plt.tight_layout()
    plt.show()

visualize_feature_maps()