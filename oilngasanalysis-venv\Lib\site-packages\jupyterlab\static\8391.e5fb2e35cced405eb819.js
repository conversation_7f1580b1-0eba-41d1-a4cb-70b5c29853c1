"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8391],{78391:(t,e,a)=>{a.d(e,{diagram:()=>j});var i=a(88855);var r=a(15051);var n=a(94065);var s=a(33416);var d=a(94746);var o=a(20778);var c=a(57590);var g=a(68232);var p=a(76261);var h=a(96049);var l=a(75905);var f=a(24982);var x=a(82211);var u=a(84416);var D={};var y=(0,l.K2)(((t,e)=>{D[t]=e}),"set");var w=(0,l.K2)((t=>D[t]),"get");var v=(0,l.K2)((()=>Object.keys(D)),"keys");var m=(0,l.K2)((()=>v().length),"size");var b={get:w,set:y,keys:v,size:m};var B=(0,l.K2)((t=>t.append("circle").attr("class","start-state").attr("r",(0,l.D7)().state.sizeUnit).attr("cx",(0,l.D7)().state.padding+(0,l.D7)().state.sizeUnit).attr("cy",(0,l.D7)().state.padding+(0,l.D7)().state.sizeUnit)),"drawStartState");var k=(0,l.K2)((t=>t.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",(0,l.D7)().state.textHeight).attr("class","divider").attr("x2",(0,l.D7)().state.textHeight*2).attr("y1",0).attr("y2",0)),"drawDivider");var S=(0,l.K2)(((t,e)=>{const a=t.append("text").attr("x",2*(0,l.D7)().state.padding).attr("y",(0,l.D7)().state.textHeight+2*(0,l.D7)().state.padding).attr("font-size",(0,l.D7)().state.fontSize).attr("class","state-title").text(e.id);const i=a.node().getBBox();t.insert("rect",":first-child").attr("x",(0,l.D7)().state.padding).attr("y",(0,l.D7)().state.padding).attr("width",i.width+2*(0,l.D7)().state.padding).attr("height",i.height+2*(0,l.D7)().state.padding).attr("rx",(0,l.D7)().state.radius);return a}),"drawSimpleState");var N=(0,l.K2)(((t,e)=>{const a=(0,l.K2)((function(t,e,a){const i=t.append("tspan").attr("x",2*(0,l.D7)().state.padding).text(e);if(!a){i.attr("dy",(0,l.D7)().state.textHeight)}}),"addTspan");const i=t.append("text").attr("x",2*(0,l.D7)().state.padding).attr("y",(0,l.D7)().state.textHeight+1.3*(0,l.D7)().state.padding).attr("font-size",(0,l.D7)().state.fontSize).attr("class","state-title").text(e.descriptions[0]);const r=i.node().getBBox();const n=r.height;const s=t.append("text").attr("x",(0,l.D7)().state.padding).attr("y",n+(0,l.D7)().state.padding*.4+(0,l.D7)().state.dividerMargin+(0,l.D7)().state.textHeight).attr("class","state-description");let d=true;let o=true;e.descriptions.forEach((function(t){if(!d){a(s,t,o);o=false}d=false}));const c=t.append("line").attr("x1",(0,l.D7)().state.padding).attr("y1",(0,l.D7)().state.padding+n+(0,l.D7)().state.dividerMargin/2).attr("y2",(0,l.D7)().state.padding+n+(0,l.D7)().state.dividerMargin/2).attr("class","descr-divider");const g=s.node().getBBox();const p=Math.max(g.width,r.width);c.attr("x2",p+3*(0,l.D7)().state.padding);t.insert("rect",":first-child").attr("x",(0,l.D7)().state.padding).attr("y",(0,l.D7)().state.padding).attr("width",p+2*(0,l.D7)().state.padding).attr("height",g.height+n+2*(0,l.D7)().state.padding).attr("rx",(0,l.D7)().state.radius);return t}),"drawDescrState");var E=(0,l.K2)(((t,e,a)=>{const i=(0,l.D7)().state.padding;const r=2*(0,l.D7)().state.padding;const n=t.node().getBBox();const s=n.width;const d=n.x;const o=t.append("text").attr("x",0).attr("y",(0,l.D7)().state.titleShift).attr("font-size",(0,l.D7)().state.fontSize).attr("class","state-title").text(e.id);const c=o.node().getBBox();const g=c.width+r;let p=Math.max(g,s);if(p===s){p=p+r}let h;const f=t.node().getBBox();if(e.doc){}h=d-i;if(g>s){h=(s-p)/2+i}if(Math.abs(d-f.x)<i&&g>s){h=d-(g-s)/2}const x=1-(0,l.D7)().state.textHeight;t.insert("rect",":first-child").attr("x",h).attr("y",x).attr("class",a?"alt-composit":"composit").attr("width",p).attr("height",f.height+(0,l.D7)().state.textHeight+(0,l.D7)().state.titleShift+1).attr("rx","0");o.attr("x",h+i);if(g<=s){o.attr("x",d+(p-r)/2-g/2+i)}t.insert("rect",":first-child").attr("x",h).attr("y",(0,l.D7)().state.titleShift-(0,l.D7)().state.textHeight-(0,l.D7)().state.padding).attr("width",p).attr("height",(0,l.D7)().state.textHeight*3).attr("rx",(0,l.D7)().state.radius);t.insert("rect",":first-child").attr("x",h).attr("y",(0,l.D7)().state.titleShift-(0,l.D7)().state.textHeight-(0,l.D7)().state.padding).attr("width",p).attr("height",f.height+3+2*(0,l.D7)().state.textHeight).attr("rx",(0,l.D7)().state.radius);return t}),"addTitleAndBox");var K=(0,l.K2)((t=>{t.append("circle").attr("class","end-state-outer").attr("r",(0,l.D7)().state.sizeUnit+(0,l.D7)().state.miniPadding).attr("cx",(0,l.D7)().state.padding+(0,l.D7)().state.sizeUnit+(0,l.D7)().state.miniPadding).attr("cy",(0,l.D7)().state.padding+(0,l.D7)().state.sizeUnit+(0,l.D7)().state.miniPadding);return t.append("circle").attr("class","end-state-inner").attr("r",(0,l.D7)().state.sizeUnit).attr("cx",(0,l.D7)().state.padding+(0,l.D7)().state.sizeUnit+2).attr("cy",(0,l.D7)().state.padding+(0,l.D7)().state.sizeUnit+2)}),"drawEndState");var M=(0,l.K2)(((t,e)=>{let a=(0,l.D7)().state.forkWidth;let i=(0,l.D7)().state.forkHeight;if(e.parentId){let t=a;a=i;i=t}return t.append("rect").style("stroke","black").style("fill","black").attr("width",a).attr("height",i).attr("x",(0,l.D7)().state.padding).attr("y",(0,l.D7)().state.padding)}),"drawForkJoinState");var R=(0,l.K2)(((t,e,a,i)=>{let r=0;const n=i.append("text");n.style("text-anchor","start");n.attr("class","noteText");let s=t.replace(/\r\n/g,"<br/>");s=s.replace(/\n/g,"<br/>");const d=s.split(l.Y2.lineBreakRegex);let o=1.25*(0,l.D7)().state.noteMargin;for(const c of d){const t=c.trim();if(t.length>0){const i=n.append("tspan");i.text(t);if(o===0){const t=i.node().getBBox();o+=t.height}r+=o;i.attr("x",e+(0,l.D7)().state.noteMargin);i.attr("y",a+r+1.25*(0,l.D7)().state.noteMargin)}}return{textWidth:n.node().getBBox().width,textHeight:r}}),"_drawLongText");var z=(0,l.K2)(((t,e)=>{e.attr("class","state-note");const a=e.append("rect").attr("x",0).attr("y",(0,l.D7)().state.padding);const i=e.append("g");const{textWidth:r,textHeight:n}=R(t,0,0,i);a.attr("height",n+2*(0,l.D7)().state.noteMargin);a.attr("width",r+(0,l.D7)().state.noteMargin*2);return a}),"drawNote");var H=(0,l.K2)((function(t,e){const a=e.id;const i={id:a,label:e.id,width:0,height:0};const r=t.append("g").attr("id",a).attr("class","stateGroup");if(e.type==="start"){B(r)}if(e.type==="end"){K(r)}if(e.type==="fork"||e.type==="join"){M(r,e)}if(e.type==="note"){z(e.note.text,r)}if(e.type==="divider"){k(r)}if(e.type==="default"&&e.descriptions.length===0){S(r,e)}if(e.type==="default"&&e.descriptions.length>0){N(r,e)}const n=r.node().getBBox();i.width=n.width+2*(0,l.D7)().state.padding;i.height=n.height+2*(0,l.D7)().state.padding;b.set(a,i);return i}),"drawState");var T=0;var L=(0,l.K2)((function(t,e,a){const r=(0,l.K2)((function(t){switch(t){case i.u4.relationType.AGGREGATION:return"aggregation";case i.u4.relationType.EXTENSION:return"extension";case i.u4.relationType.COMPOSITION:return"composition";case i.u4.relationType.DEPENDENCY:return"dependency"}}),"getRelationType");e.points=e.points.filter((t=>!Number.isNaN(t.y)));const n=e.points;const s=(0,f.n8j)().x((function(t){return t.x})).y((function(t){return t.y})).curve(f.qrM);const d=t.append("path").attr("d",s(n)).attr("id","edge"+T).attr("class","transition");let o="";if((0,l.D7)().state.arrowMarkerAbsolute){o=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search;o=o.replace(/\(/g,"\\(");o=o.replace(/\)/g,"\\)")}d.attr("marker-end","url("+o+"#"+r(i.u4.relationType.DEPENDENCY)+"End)");if(a.title!==void 0){const i=t.append("g").attr("class","stateLabel");const{x:r,y:n}=h._K.calcLabelPosition(e.points);const s=l.Y2.getRows(a.title);let d=0;const o=[];let c=0;let g=0;for(let t=0;t<=s.length;t++){const e=i.append("text").attr("text-anchor","middle").text(s[t]).attr("x",r).attr("y",n+d);const a=e.node().getBBox();c=Math.max(c,a.width);g=Math.min(g,a.x);l.Rm.info(a.x,r,n+d);if(d===0){const t=e.node().getBBox();d=t.height;l.Rm.info("Title height",d,n)}o.push(e)}let p=d*s.length;if(s.length>1){const t=(s.length-1)*d*.5;o.forEach(((e,a)=>e.attr("y",n+a*d-t)));p=d*s.length}const f=i.node().getBBox();i.insert("rect",":first-child").attr("class","box").attr("x",r-c/2-(0,l.D7)().state.padding/2).attr("y",n-p/2-(0,l.D7)().state.padding/2-3.5).attr("width",c+(0,l.D7)().state.padding).attr("height",p+(0,l.D7)().state.padding);l.Rm.info(f)}T++}),"drawEdge");var A;var G={};var O=(0,l.K2)((function(){}),"setConf");var C=(0,l.K2)((function(t){t.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")}),"insertMarkers");var P=(0,l.K2)((function(t,e,a,i){A=(0,l.D7)().state;const r=(0,l.D7)().securityLevel;let n;if(r==="sandbox"){n=(0,f.Ltv)("#i"+e)}const s=r==="sandbox"?(0,f.Ltv)(n.nodes()[0].contentDocument.body):(0,f.Ltv)("body");const d=r==="sandbox"?n.nodes()[0].contentDocument:document;l.Rm.debug("Rendering diagram "+t);const o=s.select(`[id='${e}']`);C(o);const c=i.db.getRootDoc();_(c,o,void 0,false,s,d,i);const g=A.padding;const p=o.node().getBBox();const h=p.width+g*2;const x=p.height+g*2;const u=h*1.75;(0,l.a$)(o,x,u,A.useMaxWidth);o.attr("viewBox",`${p.x-A.padding}  ${p.y-A.padding} `+h+" "+x)}),"draw");var U=(0,l.K2)((t=>t?t.length*A.fontSizeFactor:1),"getLabelWidth");var _=(0,l.K2)(((t,e,a,i,r,n,s)=>{const d=new u.T({compound:true,multigraph:true});let o;let c=true;for(o=0;o<t.length;o++){if(t[o].stmt==="relation"){c=false;break}}if(a){d.setGraph({rankdir:"LR",multigraph:true,compound:true,ranker:"tight-tree",ranksep:c?1:A.edgeLengthFactor,nodeSep:c?1:50,isMultiGraph:true})}else{d.setGraph({rankdir:"TB",multigraph:true,compound:true,ranksep:c?1:A.edgeLengthFactor,nodeSep:c?1:50,ranker:"tight-tree",isMultiGraph:true})}d.setDefaultEdgeLabel((function(){return{}}));const g=s.db.getStates();const p=s.db.getRelations();const h=Object.keys(g);let f=true;for(const l of h){const t=g[l];if(a){t.parentId=a}let o;if(t.doc){let a=e.append("g").attr("id",t.id).attr("class","stateGroup");o=_(t.doc,a,t.id,!i,r,n,s);if(f){a=E(a,t,i);let e=a.node().getBBox();o.width=e.width;o.height=e.height+A.padding/2;G[t.id]={y:A.compositTitleSize}}else{let t=a.node().getBBox();o.width=t.width;o.height=t.height}}else{o=H(e,t,d)}if(t.note){const a={descriptions:[],id:t.id+"-note",note:t.note,type:"note"};const i=H(e,a,d);if(t.note.position==="left of"){d.setNode(o.id+"-note",i);d.setNode(o.id,o)}else{d.setNode(o.id,o);d.setNode(o.id+"-note",i)}d.setParent(o.id,o.id+"-group");d.setParent(o.id+"-note",o.id+"-group")}else{d.setNode(o.id,o)}}l.Rm.debug("Count=",d.nodeCount(),d);let D=0;p.forEach((function(t){D++;l.Rm.debug("Setting edge",t);d.setEdge(t.id1,t.id2,{relation:t,width:U(t.title),height:A.labelHeight*l.Y2.getRows(t.title).length,labelpos:"c"},"id"+D)}));(0,x.Zp)(d);l.Rm.debug("Graph after layout",d.nodes());const y=e.node();d.nodes().forEach((function(t){if(t!==void 0&&d.node(t)!==void 0){l.Rm.warn("Node "+t+": "+JSON.stringify(d.node(t)));r.select("#"+y.id+" #"+t).attr("transform","translate("+(d.node(t).x-d.node(t).width/2)+","+(d.node(t).y+(G[t]?G[t].y:0)-d.node(t).height/2)+" )");r.select("#"+y.id+" #"+t).attr("data-x-shift",d.node(t).x-d.node(t).width/2);const e=n.querySelectorAll("#"+y.id+" #"+t+" .divider");e.forEach((t=>{const e=t.parentElement;let a=0;let i=0;if(e){if(e.parentElement){a=e.parentElement.getBBox().width}i=parseInt(e.getAttribute("data-x-shift"),10);if(Number.isNaN(i)){i=0}}t.setAttribute("x1",0-i+8);t.setAttribute("x2",a-i-8)}))}else{l.Rm.debug("No Node "+t+": "+JSON.stringify(d.node(t)))}}));let w=y.getBBox();d.edges().forEach((function(t){if(t!==void 0&&d.edge(t)!==void 0){l.Rm.debug("Edge "+t.v+" -> "+t.w+": "+JSON.stringify(d.edge(t)));L(e,d.edge(t),d.edge(t).relation)}}));w=y.getBBox();const v={id:a?a:"root",label:a?a:"root",width:0,height:0};v.width=w.width+2*A.padding;v.height=w.height+2*A.padding;l.Rm.debug("Doc rendered",v,d);return v}),"renderDoc");var I={setConf:O,draw:P};var j={parser:i.Zk,get db(){return new i.u4(1)},renderer:I,styles:i.tM,init:(0,l.K2)((t=>{if(!t.state){t.state={}}t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute}),"init")}}}]);