{"version": 3, "file": "403.270ca5cf44874182bd4d.js?v=270ca5cf44874182bd4d", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA,MAAM,2BAA2B;AACjC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,wCAAwC,WAAW;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,sBAAsB;AACtB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA,GAAG;;AAEH;AACA,oBAAoB,QAAQ,WAAW,QAAQ,gBAAgB;AAC/D;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/asterisk.js"], "sourcesContent": ["var atoms    = [\"exten\", \"same\", \"include\",\"ignorepat\",\"switch\"],\n    dpcmd    = [\"#include\",\"#exec\"],\n    apps     = [\n      \"addqueuemember\",\"adsiprog\",\"aelsub\",\"agentlogin\",\"agentmonitoroutgoing\",\"agi\",\n      \"alarmreceiver\",\"amd\",\"answer\",\"authenticate\",\"background\",\"backgrounddetect\",\n      \"bridge\",\"busy\",\"callcompletioncancel\",\"callcompletionrequest\",\"celgenuserevent\",\n      \"changemonitor\",\"chanisavail\",\"channelredirect\",\"chanspy\",\"clearhash\",\"confbridge\",\n      \"congestion\",\"continuewhile\",\"controlplayback\",\"dahdiacceptr2call\",\"dahdibarge\",\n      \"dahdiras\",\"dahdiscan\",\"dahdisendcallreroutingfacility\",\"dahdisendkeypadfacility\",\n      \"datetime\",\"dbdel\",\"dbdeltree\",\"deadagi\",\"dial\",\"dictate\",\"directory\",\"disa\",\n      \"dumpchan\",\"eagi\",\"echo\",\"endwhile\",\"exec\",\"execif\",\"execiftime\",\"exitwhile\",\"extenspy\",\n      \"externalivr\",\"festival\",\"flash\",\"followme\",\"forkcdr\",\"getcpeid\",\"gosub\",\"gosubif\",\n      \"goto\",\"gotoif\",\"gotoiftime\",\"hangup\",\"iax2provision\",\"ices\",\"importvar\",\"incomplete\",\n      \"ivrdemo\",\"jabberjoin\",\"jabberleave\",\"jabbersend\",\"jabbersendgroup\",\"jabberstatus\",\n      \"jack\",\"log\",\"macro\",\"macroexclusive\",\"macroexit\",\"macroif\",\"mailboxexists\",\"meetme\",\n      \"meetmeadmin\",\"meetmechanneladmin\",\"meetmecount\",\"milliwatt\",\"minivmaccmess\",\"minivmdelete\",\n      \"minivmgreet\",\"minivmmwi\",\"minivmnotify\",\"minivmrecord\",\"mixmonitor\",\"monitor\",\"morsecode\",\n      \"mp3player\",\"mset\",\"musiconhold\",\"nbscat\",\"nocdr\",\"noop\",\"odbc\",\"odbc\",\"odbcfinish\",\n      \"originate\",\"ospauth\",\"ospfinish\",\"osplookup\",\"ospnext\",\"page\",\"park\",\"parkandannounce\",\n      \"parkedcall\",\"pausemonitor\",\"pausequeuemember\",\"pickup\",\"pickupchan\",\"playback\",\"playtones\",\n      \"privacymanager\",\"proceeding\",\"progress\",\"queue\",\"queuelog\",\"raiseexception\",\"read\",\"readexten\",\n      \"readfile\",\"receivefax\",\"receivefax\",\"receivefax\",\"record\",\"removequeuemember\",\n      \"resetcdr\",\"retrydial\",\"return\",\"ringing\",\"sayalpha\",\"saycountedadj\",\"saycountednoun\",\n      \"saycountpl\",\"saydigits\",\"saynumber\",\"sayphonetic\",\"sayunixtime\",\"senddtmf\",\"sendfax\",\n      \"sendfax\",\"sendfax\",\"sendimage\",\"sendtext\",\"sendurl\",\"set\",\"setamaflags\",\n      \"setcallerpres\",\"setmusiconhold\",\"sipaddheader\",\"sipdtmfmode\",\"sipremoveheader\",\"skel\",\n      \"slastation\",\"slatrunk\",\"sms\",\"softhangup\",\"speechactivategrammar\",\"speechbackground\",\n      \"speechcreate\",\"speechdeactivategrammar\",\"speechdestroy\",\"speechloadgrammar\",\"speechprocessingsound\",\n      \"speechstart\",\"speechunloadgrammar\",\"stackpop\",\"startmusiconhold\",\"stopmixmonitor\",\"stopmonitor\",\n      \"stopmusiconhold\",\"stopplaytones\",\"system\",\"testclient\",\"testserver\",\"transfer\",\"tryexec\",\n      \"trysystem\",\"unpausemonitor\",\"unpausequeuemember\",\"userevent\",\"verbose\",\"vmauthenticate\",\n      \"vmsayname\",\"voicemail\",\"voicemailmain\",\"wait\",\"waitexten\",\"waitfornoise\",\"waitforring\",\n      \"waitforsilence\",\"waitmusiconhold\",\"waituntil\",\"while\",\"zapateller\"\n    ];\n\nfunction basicToken(stream,state){\n  var cur = '';\n  var ch = stream.next();\n  // comment\n  if (state.blockComment) {\n    if (ch == \"-\" && stream.match(\"-;\", true)) {\n      state.blockComment = false;\n    } else if (stream.skipTo(\"--;\")) {\n      stream.next();\n      stream.next();\n      stream.next();\n      state.blockComment = false;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"comment\";\n  }\n  if(ch == \";\") {\n    if (stream.match(\"--\", true)) {\n      if (!stream.match(\"-\", false)) {  // Except ;--- is not a block comment\n        state.blockComment = true;\n        return \"comment\";\n      }\n    }\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // context\n  if(ch == '[') {\n    stream.skipTo(']');\n    stream.eat(']');\n    return \"header\";\n  }\n  // string\n  if(ch == '\"') {\n    stream.skipTo('\"');\n    return \"string\";\n  }\n  if(ch == \"'\") {\n    stream.skipTo(\"'\");\n    return \"string.special\";\n  }\n  // dialplan commands\n  if(ch == '#') {\n    stream.eatWhile(/\\w/);\n    cur = stream.current();\n    if(dpcmd.indexOf(cur) !== -1) {\n      stream.skipToEnd();\n      return \"strong\";\n    }\n  }\n  // application args\n  if(ch == '$'){\n    var ch1 = stream.peek();\n    if(ch1 == '{'){\n      stream.skipTo('}');\n      stream.eat('}');\n      return \"variableName.special\";\n    }\n  }\n  // extension\n  stream.eatWhile(/\\w/);\n  cur = stream.current();\n  if(atoms.indexOf(cur) !== -1) {\n    state.extenStart = true;\n    switch(cur) {\n    case 'same': state.extenSame = true; break;\n    case 'include':\n    case 'switch':\n    case 'ignorepat':\n      state.extenInclude = true;break;\n    default:break;\n    }\n    return \"atom\";\n  }\n}\n\nexport const asterisk = {\n  name: \"asterisk\",\n  startState: function() {\n    return {\n      blockComment: false,\n      extenStart: false,\n      extenSame:  false,\n      extenInclude: false,\n      extenExten: false,\n      extenPriority: false,\n      extenApplication: false\n    };\n  },\n  token: function(stream, state) {\n\n    var cur = '';\n    if(stream.eatSpace()) return null;\n    // extension started\n    if(state.extenStart){\n      stream.eatWhile(/[^\\s]/);\n      cur = stream.current();\n      if(/^=>?$/.test(cur)){\n        state.extenExten = true;\n        state.extenStart = false;\n        return \"strong\";\n      } else {\n        state.extenStart = false;\n        stream.skipToEnd();\n        return \"error\";\n      }\n    } else if(state.extenExten) {\n      // set exten and priority\n      state.extenExten = false;\n      state.extenPriority = true;\n      stream.eatWhile(/[^,]/);\n      if(state.extenInclude) {\n        stream.skipToEnd();\n        state.extenPriority = false;\n        state.extenInclude = false;\n      }\n      if(state.extenSame) {\n        state.extenPriority = false;\n        state.extenSame = false;\n        state.extenApplication = true;\n      }\n      return \"tag\";\n    } else if(state.extenPriority) {\n      state.extenPriority = false;\n      state.extenApplication = true;\n      stream.next(); // get comma\n      if(state.extenSame) return null;\n      stream.eatWhile(/[^,]/);\n      return \"number\";\n    } else if(state.extenApplication) {\n      stream.eatWhile(/,/);\n      cur = stream.current();\n      if(cur === ',') return null;\n      stream.eatWhile(/\\w/);\n      cur = stream.current().toLowerCase();\n      state.extenApplication = false;\n      if(apps.indexOf(cur) !== -1){\n        return \"def\";\n      }\n    } else{\n      return basicToken(stream,state);\n    }\n\n    return null;\n  },\n\n  languageData: {\n    commentTokens: {line: \";\", block: {open: \";--\", close: \"--;\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}