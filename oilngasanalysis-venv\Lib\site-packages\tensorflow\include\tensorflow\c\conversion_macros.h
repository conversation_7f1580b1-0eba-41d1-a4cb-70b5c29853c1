/* Copyright 2020 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_C_CONVERSION_MACROS_H_
#define TENSORFLOW_C_CONVERSION_MACROS_H_

#define DEFINE_CONVERSION_FUNCTIONS(cpp_impl, wrapper)                         \
  inline cpp_impl *unwrap(wrapper *w) {                                        \
    return reinterpret_cast<cpp_impl *>(w);                                    \
  }                                                                            \
                                                                               \
  inline const cpp_impl *unwrap(const wrapper *w) {                            \
    return reinterpret_cast<const cpp_impl *>(w);                              \
  }                                                                            \
                                                                               \
  inline wrapper *wrap(cpp_impl *i) { return reinterpret_cast<wrapper *>(i); } \
  inline const wrapper *wrap(const cpp_impl *i) {                              \
    return reinterpret_cast<const wrapper *>(i);                               \
  }

#endif  // TENSORFLOW_C_CONVERSION_MACROS_H_
