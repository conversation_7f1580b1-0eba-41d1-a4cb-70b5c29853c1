{"version": 3, "file": "4843.7eed3c5267c10f3eb786.js?v=7eed3c5267c10f3eb786", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,mBAAmB;AACzC;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA,CAAC;;AAEM;AACP;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA,CAAC;;AAEM;AACP;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/mscgen.js"], "sourcesContent": ["function mkParser(lang) {\n  return {\n    name: \"mscgen\",\n    startState: startStateFn,\n    copyState: copyStateFn,\n    token: produceTokenFunction(lang),\n    languageData: {\n      commentTokens: {line: \"#\", block: {open: \"/*\", close: \"*/\"}}\n    }\n  }\n}\n\nexport const mscgen = mkParser({\n  \"keywords\" : [\"msc\"],\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\"],\n  \"attributes\" : [\"label\", \"idurl\", \"id\", \"url\", \"linecolor\", \"linecolour\", \"textcolor\", \"textcolour\", \"textbgcolor\", \"textbgcolour\", \"arclinecolor\", \"arclinecolour\", \"arctextcolor\", \"arctextcolour\", \"arctextbgcolor\", \"arctextbgcolour\", \"arcskip\"],\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"], // [ and  ] are brackets too, but these get handled in with lists\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nexport const msgenny = mkParser({\n  \"keywords\" : null,\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\", \"wordwrapentities\", \"watermark\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\", \"auto\"],\n  \"attributes\" : null,\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"],\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\", \"alt\", \"else\", \"opt\", \"break\", \"par\", \"seq\", \"strict\", \"neg\", \"critical\", \"ignore\", \"consider\", \"assert\", \"loop\", \"ref\", \"exc\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nexport const xu = mkParser({\n  \"keywords\" : [\"msc\", \"xu\"],\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\", \"wordwrapentities\", \"watermark\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\", \"auto\"],\n  \"attributes\" : [\"label\", \"idurl\", \"id\", \"url\", \"linecolor\", \"linecolour\", \"textcolor\", \"textcolour\", \"textbgcolor\", \"textbgcolour\", \"arclinecolor\", \"arclinecolour\", \"arctextcolor\", \"arctextcolour\", \"arctextbgcolor\", \"arctextbgcolour\", \"arcskip\", \"title\", \"deactivate\", \"activate\", \"activation\"],\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"],  // [ and  ] are brackets too, but these get handled in with lists\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\", \"alt\", \"else\", \"opt\", \"break\", \"par\", \"seq\", \"strict\", \"neg\", \"critical\", \"ignore\", \"consider\", \"assert\", \"loop\", \"ref\", \"exc\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nfunction wordRegexpBoundary(pWords) {\n  return new RegExp(\"^\\\\b(\" + pWords.join(\"|\") + \")\\\\b\", \"i\");\n}\n\nfunction wordRegexp(pWords) {\n  return new RegExp(\"^(?:\" + pWords.join(\"|\") + \")\", \"i\");\n}\n\nfunction startStateFn() {\n  return {\n    inComment : false,\n    inString : false,\n    inAttributeList : false,\n    inScript : false\n  };\n}\n\nfunction copyStateFn(pState) {\n  return {\n    inComment : pState.inComment,\n    inString : pState.inString,\n    inAttributeList : pState.inAttributeList,\n    inScript : pState.inScript\n  };\n}\n\nfunction produceTokenFunction(pConfig) {\n  return function(pStream, pState) {\n    if (pStream.match(wordRegexp(pConfig.brackets), true, true)) {\n      return \"bracket\";\n    }\n    /* comments */\n    if (!pState.inComment) {\n      if (pStream.match(/\\/\\*[^\\*\\/]*/, true, true)) {\n        pState.inComment = true;\n        return \"comment\";\n      }\n      if (pStream.match(wordRegexp(pConfig.singlecomment), true, true)) {\n        pStream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (pState.inComment) {\n      if (pStream.match(/[^\\*\\/]*\\*\\//, true, true))\n        pState.inComment = false;\n      else\n        pStream.skipToEnd();\n      return \"comment\";\n    }\n    /* strings */\n    if (!pState.inString && pStream.match(/\\\"(\\\\\\\"|[^\\\"])*/, true, true)) {\n      pState.inString = true;\n      return \"string\";\n    }\n    if (pState.inString) {\n      if (pStream.match(/[^\\\"]*\\\"/, true, true))\n        pState.inString = false;\n      else\n        pStream.skipToEnd();\n      return \"string\";\n    }\n    /* keywords & operators */\n    if (!!pConfig.keywords && pStream.match(wordRegexpBoundary(pConfig.keywords), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexpBoundary(pConfig.options), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexpBoundary(pConfig.arcsWords), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexp(pConfig.arcsOthers), true, true))\n      return \"keyword\";\n\n    if (!!pConfig.operators && pStream.match(wordRegexp(pConfig.operators), true, true))\n      return \"operator\";\n\n    if (!!pConfig.constants && pStream.match(wordRegexp(pConfig.constants), true, true))\n      return \"variable\";\n\n    /* attribute lists */\n    if (!pConfig.inAttributeList && !!pConfig.attributes && pStream.match('[', true, true)) {\n      pConfig.inAttributeList = true;\n      return \"bracket\";\n    }\n    if (pConfig.inAttributeList) {\n      if (pConfig.attributes !== null && pStream.match(wordRegexpBoundary(pConfig.attributes), true, true)) {\n        return \"attribute\";\n      }\n      if (pStream.match(']', true, true)) {\n        pConfig.inAttributeList = false;\n        return \"bracket\";\n      }\n    }\n\n    pStream.next();\n    return null\n  };\n}\n"], "names": [], "sourceRoot": ""}