{"version": 3, "file": "4276.aa39300c806a420e8c6e.js?v=aa39300c806a420e8c6e", "mappings": ";;;;;;;;;;;;;;;;;;;AAAqC;;AAErC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qCAAQ;AACjB;;AAEA,yDAAe,QAAQ,EAAC;;;;;;;;;AC3BxB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iDAAe,SAAS,EAAC;;;;;;;AC3Be;AACU;AACb;;AAErC;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,aAAa,UAAU;AACvB;AACA;AACA;AACA,2CAA2C,kCAAc;AACzD;AACA;AACA;AACA,YAAY,2BAAQ;AACpB;AACA;AACA;AACA,MAAM;AACN,YAAY,2BAAQ;AACpB;AACA,yDAAyD,2BAAQ;AACjE,WAAW,UAAS;AACpB;AACA;;AAEA,mDAAe,WAAW,EAAC;;;AC7BiB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,YAAW;;AAEvB,sDAAe,KAAK,EAAC;;;;;AC7CrB;AACA;AACA;AACA;;AAEgB;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;ACtD+B;AACc;AACP;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACqB;;AAErB,wBAAwB,uBAAU;;AAElC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS,sBAAS;AAClB,IAAI,kBAAK;AACT;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,EAAE,sBAAS;AACX;AACA;;AAEA;AACA,qBAAqB,sBAAsB;AAC3C;;AAEA;AACA;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;AACA;;AAEA;AACA,qBAAqB,qBAAK;AAC1B;AACA;;AAEA,EAAE,sBAAS;AACX,0BAA0B,qBAAqB;AAC/C,GAAG;;AAEH;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,gBAAgB,eAAO;AACvB,eAAe,IAAI;AACnB,GAAG;AACH;;AAEA,EAAE,sBAAS;AACX;AACA,GAAG;;AAEH,WAAW;AACX;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;;AC7H+B;AACa;;AAEvB;;AAErB;AACA,+CAA+C,SAAS;AACxD,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA,+BAA+B,kBAAU;AACzC,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAS;AACb;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;;AAEA,EAAE,sBAAS;AACX;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;AC3D0C;AACX;;AAE/B;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA,SAAS,8BAAU;AACnB,WAAW,wBAAK;AAChB,GAAG;AACH;;AAEA,gDAAe,QAAQ,EAAC;;;;;;;AClBW;AACG;AACM;;AAE5C;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA,SAAS,+BAAW,CAAC,4BAAQ,kBAAkB,sBAAO;AACtD;;AAEA,gDAAe,QAAQ,EAAC;;;ACfc;AACA;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,sBAAsB;AACjC,aAAa,QAAQ;AACrB;AACA;AACA,kBAAkB;AAClB;AACA;AACA,WAAW;AACX;AACA,WAAW,SAAQ;AACnB,6BAA6B,EAAE,SAAQ;AACvC,CAAC;;AAED,qDAAe,IAAI,EAAC;;;;;;;ACxBpB;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA,8CAAe,MAAM,EAAC;;;;;ACbwB;AACZ;AACG;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gCAAY,QAAQ,uBAAQ,EAAE,OAAM;AAC1C;AACA;;AAEA,oDAAe,GAAG,EAAC;;;;;;;;;;;AC5BiC;AACV;AACI;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,kBAAkB,8BAA8B;AAChD,kBAAkB;AAClB;AACA;AACA,oCAAoC,eAAe;AACnD,WAAW,2BAA2B;AACtC;AACA;AACA;AACA,WAAW,2BAA2B;AACtC;AACA;AACA;AACA,aAAa,gCAAY;;AAEzB,EAAE,8BAAU;AACZ,IAAI,mCAAe;AACnB,GAAG;AACH;AACA;;AAEA,0DAAe,SAAS,EAAC;;;;;;;;;;;AC1CK;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,SAAS,oBAAI;AACb;;AAEA,oDAAe,GAAG,EAAC;;;ACtBY;AACc;;AAiB3C;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,kBAAU;AAClB,IAAI;;AAEJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uBAAuB,qBAAK;AAC5B,EAAE,sBAAS;AACX;AACA,GAAG;AACH,EAAE,sBAAS;AACX,qDAAqD;AACrD;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;;AAEA;AACA,uBAAuB,qBAAK,GAAG,8BAA8B;AAC7D,EAAE,sBAAS;AACX;AACA;AACA;AACA,GAAG;AACH,EAAE,sBAAS;AACX;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;;AAEA;AACA,0CAA0C,KAAK;AAC/C,KAAK,oBAAoB;AACzB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,kBAAK,CAAC,eAAO,CAAC,YAAO;AACtC;AACA,GAAG;AACH,EAAE,sBAAS;AACX;AACA;AACA,SAAS,0BAAa;AACtB;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY,4BAAK;AACjB,IAAI,kBAAK;AACT;AACA,KAAK;AACL;AACA,EAAE,sBAAS;AACX;AACA,QAAQ,kBAAK;AACb;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,eAAe,4BAAK;AACpB,IAAI,kBAAK;AACT;AACA,KAAK;AACL;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,EAAE,sBAAS;AACX,QAAQ,0BAAa;AACrB;AACA,MAAM;AACN,MAAM,sBAAS;AACf;AACA,OAAO;AACP;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,YAAO;AAChB,SAAS,aAAK;AACd,IAAI,kBAAK;AACT;AACA,WAAW,0BAAa;AACxB;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,EAAE,sBAAS;AACX;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS,SAAI;AACb,cAAc,aAAK;AACnB;AACA;AACA,IAAI;AACJ,oCAAoC,aAAK;AACzC;AACA;;AAEA;AACA;AACA;;;ACzP+B;AACG;;AAEL;;AAE7B;AACA;AACA;AACA;AACA;AACA,MAAM,sBAAS;AACf;;AAEA;AACA;AACA;AACA,gEAAgE,gBAAgB;AAChF,QAAQ,iCAAa;AACrB,QAAQ,iCAAa;AACrB;AACA;AACA;;AAEA,EAAE,sBAAS;AACX;;AAEA,SAAS,iCAAa;AACtB,gBAAgB;AAChB;AACA,aAAa,YAAiB;AAC9B;AACA;AACA;AACA,4BAA4B,WAAW;AACvC;AACA;;;ACnC+B;;AAEP;;AAExB;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,sBAAI;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;AACH,EAAE,sBAAS;AACX;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA,IAAI,sBAAS;AACb;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA,IAAI,sBAAS;AACb;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;;AC1EA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AAC+B;AACG;;AAEb;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAG;AACZ;AACA,EAAE,sBAAS;AACX;AACA,GAAG;AACH;;AAEA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA,eAAe,QAAQ;AACvB,gBAAgB,QAAQ;AACxB,gBAAgB,QAAQ;AACxB,gBAAgB,2BAA2B;AAC3C,gBAAgB,KAAK;AACrB,gBAAgB,mCAAmC;AACnD,gBAAgB,QAAQ;AACxB,gBAAgB,uCAAuC;AACvD;;AAEA,aAAa,mBAAmB;AAChC;AACA;AACA,uBAAuB,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,YAAiB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0BAA0B;AACpD;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,0BAA0B;AAC9C;;AAEA,SAAS,cAAI;AACb,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,sBAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;AC7G8C;AACA;AACZ;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB,aAAa,GAAG;AAChB;AACA;AACA,oBAAoB,QAAQ,IAAI,QAAQ;AACxC;AACA,kCAAkC,aAAa;AAC/C,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,MAAM,gCAAY,QAAQ,gCAAY,eAAe,sBAAM;AAC3D;AACA;;AAEA,sDAAe,KAAK,EAAC;;;ACjCU;;AAED;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,4BAAK;AACpB,MAAM,kBAAK;AACX;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,EAAE,sBAAS;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC9D+B;AACiB;AACd;;AAEV;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,qBAAK,GAAG,iBAAiB;;AAEvC;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA,gCAAgC,KAAK,aAAa,KAAK;AACvD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAS;AACb;AACA;AACA,4BAA4B,KAAK;AACjC,uBAAuB;AACvB,0BAA0B;AAC1B;AACA;AACA,KAAK;AACL;;AAEA,EAAE,sBAAS;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAO;AAChB;AACA,aAAa,KAAK;AAClB;AACA,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;AACH;;;;;;;ACtF+B;AAC2B;;AAEtC;;AAEpB,0BAA0B,uBAAU;;AAEpC,SAAS,iBAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AChE+B;AACU;;AAElB;;AAEvB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,MAAM;AACN;AACA;;;ACb+B;;AAEN;;AAEzB,IAAI,kCAAmB,GAAG,uBAAU;;AAEpC;AACA;AACA;AACA,gBAAgB,kCAAmB;AACnC;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,0BAA0B;AAC1B;AACA,KAAK;AACL;AACA;AACA;AACA,wBAAwB;AACxB,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL,GAAG;;AAEH;AACA;;;;;;;;;;;;;ACtD8C;;AAE9C;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA,gBAAgB,gCAAY;;AAE5B,iDAAe,SAAS,EAAC;;;ACXzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA,kDAAe,UAAU,EAAC;;;ACzB1B;AACA,IAAI,0BAAa;AACjB,IAAI,8BAAiB;AACrB,IAAI,kCAAqB;AACzB,IAAI,gCAAmB;AACvB,IAAI,yBAAY,GAAG,8BAAiB,GAAG,kCAAqB,GAAG,gCAAmB;AAClF,IAAI,uBAAU;;AAEd;AACA,qBAAqB,0BAAa;AAClC,oBAAoB,yBAAY;AAChC;AACA;AACA,yBAAyB,0BAAa;AACtC,+CAA+C,EAAE;AACjD;AACA,IAAI,kBAAK;;AAET;AACA;AACA,qBAAqB,uBAAU;AAC/B,wBAAwB,kBAAK;AAC7B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mDAAe,WAAW,EAAC;;;AC3Ca;AACE;AACE;;AAE5C;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA,SAAS,WAAU;AACnB,MAAM,YAAW;AACjB,MAAM,UAAS;AACf;;AAEA,kDAAe,UAAU,EAAC;;;ACjBY;AACJ;AACS;AACN;AACK;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,qBAAqB;AAChC,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA,YAAY,gBAAgB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8BAAW;AACjB,WAAW,2BAAQ,eAAe,WAAU;AAC5C;AACA,YAAY,0BAAM;AAClB;AACA;AACA;AACA,SAAS,4BAAQ;AACjB;;AAEA,qDAAe,IAAI,EAAC;;;AC7CW;;AAEI;;AAEnC,eAAO,kBAAkB,sBAAc;;AAEvC,SAAS,eAAO;AAChB;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,sBAAc;AAC9B;;AAEA;AACA;AACA;AACA,MAAM,sBAAM;AACZ;AACA;AACA;AACA;;AAEA,EAAE,sBAAM;;AAER,MAAM,cAAM;AACZ,cAAc,sBAAc;AAC5B;;AAEA;AACA;;AAEA,SAAS,sBAAc;AACvB,sBAAc,0BAA0B;;;ACnCe;;AAElC;;AAErB;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACd+B;;AAEhB;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,sBAAS;AAChB;AACA;;AAEA;;AAEA;AACA;AACA,EAAE,sBAAM;AACR;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,sBAAM;AACV;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;;AC7C+B;;AAEV;;AAErB;AACA,SAAS,GAAG;AACZ;;;ACN+B;;AAEX;;AAEpB;AACA,SAAS,GAAG;AACZ;;;;;ACN+B;AAC2B;AACtB;;AAEpB;;AAEhB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;;;ACnD6C;AACJ;AACO;AACF;AACM;AACR;AACD;AACF;AACR;AACI;AACE;;AAcrC;;;ACxB6B;AACoB;AACb;AACY;AACH;;AAErB;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ;AACd,EAAE,WAAW;AACb,UAAU,YAAY;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,SAAa;AACxB;AACA,EAAE,sBAAS;AACX;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,EAAE,sBAAS;AACX;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;;AAEA;AACA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;;AAEA;AACA,SAAS,mBAAM;AACf;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,qBAAQ;AAC3B;AACA;AACA;AACA;AACA,GAAG;;AAEH,SAAS,eAAO;AAChB,WAAW,KAAK;AAChB,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;;AAEA;AACA,aAAa,mBAAM;AACnB;AACA,GAAG;AACH,WAAW,QAAY;AACvB;AACA,EAAE,sBAAS;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC3OkD;AACI;AACd;;AAExB;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,WAAW;;AAEnC;AACA,EAAE,WAAW;AACb,EAAE,YAAY;AACd;;AAEA;AACA,EAAE,cAAc;AAChB;;;;;;;ACnD+B;AACG;;AAEV;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAG;AACZ,aAAa,YAAiB,cAAc;AAC5C;AACA,eAAe,aAAK,CAAC,qBAAQ,eAAe;AAC5C;;AAEA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,EAAE,sBAAS;AACX,IAAI,iBAAG;AACP,GAAG;;AAEH;AACA;AACA;AACA;;AAEA,SAAS,iBAAG;AACZ;AACA;AACA;AACA,2BAA2B,4BAA4B;AACvD;AACA;AACA;;AAEA,YAAY,aAAkB;AAC9B,eAAe,aAAkB;AACjC;;AAEA;AACA;AACA;AACA;;AAEA,EAAE,sBAAS;AACX,IAAI,iBAAG;;AAEP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA,2BAA2B,uCAAuC;AAClE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM,sBAAS;AACf;AACA,OAAO;AACP;AACA;AACA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;AACH;AACA;;AAEA;AACA,SAAS,qBAAQ;AACjB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;ACxIwC;;AAExC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,GAAG;AAChB;AACA;AACA;AACA,oBAAoB,QAAQ,IAAI,QAAQ;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,6BAAS;AAClB;;AAEA,0DAAe,SAAS,EAAC;;;AC5BM;;AAEG;;AAElC;AACA,eAAe;AACf;;AAEA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAA2B;AACtD;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;;ACpD+B;AACiB;;AAErB;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,qBAAK,GAAG,gBAAgB;AACzC,kBAAkB,YAAY;AAC9B;AACA;AACA,OAAO;;AAEP,EAAE,sBAAS;AACX;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM,sBAAS;AACf;AACA;AACA,oBAAoB,0BAAa;AACjC,+BAA+B,mCAAmC;AAClE,OAAO;;AAEP;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA,wBAAwB,kBAAU;AAClC;AACA;;;;;AC3EA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,qDAAe,aAAa,EAAC;;;ACtBe;AACI;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa,QAAQ;AACrB;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,SAAS,cAAa,4BAA4B,2BAAW;AAC7D;;AAEA,0DAAe,SAAS,EAAC;;;;;;;;;;;ACvBzB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,kDAAe,UAAU,EAAC;;;;;;;ACpBW;;AAErC;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,2BAAQ;;AAE9B;AACA;AACA;AACA,sBAAsB,2BAAQ;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wDAAe,gBAAgB,EAAC;;;ACxCsB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,oBAAoB;AAC/B,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,iBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,uDAAe,eAAe,EAAC;;;AC3CO;AACF;AACU;AACV;AACM;AACF;AACY;AACf;AACF;;AAEnC;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,8BAA8B;AACzC,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;AACA,gBAAgB,4BAAQ;AACxB,UAAU,0BAAO;AACjB;AACA,iBAAiB,2BAAO;AACxB;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ,iBAAiB,uBAAQ;AACzB;;AAEA;AACA,cAAc,4BAAQ,YAAY,6BAAS,CAAC,4BAAY;;AAExD,eAAe,2BAAO;AACtB,mBAAmB,4BAAQ;AAC3B;AACA,KAAK;AACL,aAAa;AACb,GAAG;;AAEH,SAAS,WAAU;AACnB,WAAW,gBAAe;AAC1B,GAAG;AACH;;AAEA,mDAAe,WAAW,EAAC;;;;;AChDiB;AACA;AACN;AACY;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,0BAA0B;AACrC;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA,OAAO,6BAA6B;AACpC,OAAO,6BAA6B;AACpC,OAAO,6BAA6B;AACpC,OAAO;AACP;AACA;AACA,kCAAkC,gBAAgB;AAClD;AACA;AACA;AACA;AACA;AACA,aAAa,4BAAQ;AACrB;AACA;AACA;AACA;AACA,oBAAoB,kCAAc;AAClC;AACA,IAAI,uBAAuB,kCAAc;AACzC;AACA;AACA,SAAS,YAAW,aAAa,+BAAW;AAC5C,CAAC;;AAED,uDAAe,MAAM,EAAC;;;AC/CS;;AAET;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,mBAAW;AAC5B;AACA,IAAI,kBAAK;AACT;AACA,KAAK;AACL;AACA,qBAAqB,sBAAS;AAC9B,IAAI,kBAAK;AACT,aAAa,gBAAQ;AACrB,QAAQ,kBAAK;AACb,mBAAmB;AACnB,SAAS;AACT;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa,kBAAK;AAClB;AACA,GAAG;;AAEH;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;;ACjF+B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,oBAAoB,qBAAQ;AAC5B;AACA,GAAG;AACH,gBAAgB,aAAK;AACrB,IAAI,kBAAK;AACT;AACA,KAAK;AACL;AACA,eAAe,kBAAK,CAAC,eAAO;AAC5B;AACA,GAAG;;AAEH;AACA,QAAQ,kBAAK;AACb;AACA;AACA;AACA,IAAI,sBAAS;AACb;;AAEA,kBAAkB,gBAAQ;AAC1B;AACA,GAAG;AACH,EAAE,sBAAS;;AAEX;AACA;;;ACzC+B;;AAET;;AAEtB;AACA,SAAS,kBAAK;AACd;AACA;AACA,eAAe;AACf,MAAM;AACN,mBAAmB,qBAAQ;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,UAAU,mBAAmB;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;AC9B+B;;AAEH;;AAE5B;AACA,wCAAwC,uBAAuB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,sBAAsB;AACxD,8BAA8B,EAAE;AAChC;AACA;AACA;AACA,yCAAyC,0BAA0B;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,SAAS,0BAAa;AACtB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA;AACA,SAAS,0BAAa,aAAa,0BAAa;AAChD;AACA;AACA;AACA,GAAG;;AAEH,kBAAkB,qBAAQ;AAC1B;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAAa;AACrB,QAAQ,0BAAa;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,sBAAS;AACb,IAAI,sBAAS;AACb;;AAEA,SAAS,kBAAK;AACd,IAAI,qBAAQ;AACZ;AACA,KAAK;AACL;AACA,aAAa,cAAM;AACnB,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AC/H+B;AACI;;AAEnB;;AAEhB;AACA,cAAc,SAAc;AAC5B;AACA,GAAG;AACH;AACA,iBAAiB,gBAAQ;AACzB;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,iBAAiB,IAAI,sBAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,sCAAsC,6BAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;;;AC1D+B;AACc;AACa;AACzB;;AAET;;AAExB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,qBAAQ;AACtB;AACA,KAAK;AACL;;AAEA,oBAAoB,UAAU;AAC9B,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,gBAAgB,gBAAgB;AAChC;;AAEA,eAAe,IAAI;;AAEnB;AACA,gBAAgB,sBAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX,eAAe,sBAAS;AACxB;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;;AAEA;AACA,OAAO,0BAAa;AACpB;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;;AC7E+B;AACiB;AACb;AACoC;AACd;AACX;AACF;AACM;;AAEjC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,YAAY;AAC5B,0CAA0C,eAAO;AACjD,wCAAwC,eAAO;;AAE/C,iBAAiB,SAAS;AAC1B;;AAEA;AACA;;AAEA,gCAAgC,cAAc;AAC9C;;AAEA,eAAe,gBAAqB;AACpC,aAAa,UAAU;AACvB;AACA;AACA,aAAa,mBAAW;AACxB;AACA;AACA;;AAEA;AACA;;AAEA;AACA,SAAS,kBAAK;AACd,WAAW,eAAe;AAC1B,GAAG;AACH;;AAEA;AACA,eAAe,qBAAK;AACpB,EAAE,sBAAS;AACX;AACA,iBAAiB,YAAY;AAC7B,IAAI,sBAAS;AACb;AACA,KAAK;AACL,IAAI,sBAAsB;AAC1B,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX,IAAI,sBAAS;AACb;AACA,KAAK;AACL,GAAG;AACH;;;AC5E+B;;AAEF;;AAE7B;AACA,sBAAsB,6BAAS;;AAE/B,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;AACA;;AAEA,WAAW;AACX;;AAEA,SAAS,6BAAS;AAClB;AACA;;AAEA;AACA;AACA,IAAI,sBAAS;AACb,kBAAkB;AAClB;AACA,EAAE,sBAAS;;AAEX;AACA;;;;;ACrF0C;AACI;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,mBAAmB,8BAAU,SAAS,gCAAY;AAClD;;AAEA,uDAAe,MAAM,EAAC;;;;;;;ACnCc;AACU;AACb;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM,2BAAO,SAAS,gCAAY,YAAY,qBAAM;AACpD;;AAEA,sDAAe,KAAK,EAAC;;;ACtCU;AACiB;AACb;;AAEnC;AACA;AACA;AACA;;AAaE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,6BAAM;;AAEvB,IAAI,sBAAS;AACb;AACA;;AAEA;AACA,QAAQ,sBAAS;AACjB,UAAU,sBAAS;AACnB;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA,EAAE,qBAAQ;AACV;AACA;;AAEA;AACA;;AAEA;AACA;AACA,IAAI,sBAAS,CAAC,eAAO;AACrB;AACA;AACA,QAAQ,sBAAS;AACjB;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA,IAAI,sBAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA,EAAE,qBAAQ;AACV;AACA;;AAEA;AACA;AACA,WAAW,mBAAM;AACjB;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,cAAc;AACd;;AAEA;AACA;AACA;AACA,EAAE,sBAAS;AACX,IAAI,sBAAS;AACb;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA,IAAI,sBAAS;AACb;AACA;AACA,aAAa,gBAAQ;AACrB;AACA,SAAS;AACT;AACA,yDAAyD,SAAS;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,uBAAuB,qBAAK;AAC5B;AACA;;AAEA,EAAE,sBAAS;AACX;AACA,IAAI,sBAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS,eAAO,CAAC,qBAAQ;AACzB;AACA;;AAEA,IAAI,eAAO;AACX;;AAEA;AACA;AACA,KAAK;;AAEL;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAQ;AAC5B,iBAAiB,4BAAK;AACtB,iBAAiB,aAAK;;AAEtB,EAAE,sBAAS;AACX,IAAI,sBAAS;AACb;AACA;AACA;AACA;;AAEA,mBAAmB,qBAAQ;AAC3B,2CAA2C,4BAAK,wBAAwB,aAAK;;AAE7E;AACA,yBAAyB,mBAAW;AACpC;AACA,SAAS;AACT;AACA,KAAK;AACL,GAAG;AACH;;AAEA;AACA,SAAS,mBAAW;AACpB;AACA;AACA,MAAM;AACN,eAAe,gBAAQ,CAAC,kBAAK;AAC7B;AACA;AACA,GAAG;AACH;;AAEA;AACA,iBAAiB,gBAAqB;AACtC,kBAAkB,oBAAO;;AAEzB;AACA;AACA,EAAE,sBAAS;AACX,iDAAiD,qBAAQ;AACzD,IAAI,sBAAS;AACb;AACA,2BAA2B,kBAAK;AAChC,iBAAiB,qBAAQ;AACzB,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA,aAAa,mBAAW;AACxB;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;ACta+B;AACI;AACC;;AAEhB;;AAEpB;AACA,MAAM,kBAAuB;;AAE7B;AACA,EAAE,gBAAQ,CAAC,SAAS;AACpB;AACA,GAAG;AACH;;AAEA;AACA,iBAAiB,gBAAqB;AACtC;AACA;AACA,EAAE,sBAAS;AACX,oBAAoB,aAAK;AACzB,MAAM,kBAAK;AACX;AACA,OAAO;AACP;AACA,IAAI,sBAAS;AACb;AACA,KAAK;AACL;AACA,GAAG;AACH;;;AC9B+B;AACc;AACgB;AACF;AACnB;AACI;AACL;AACY;AACV;AACoB;AACd;AACb;;AAEhB;;AAElB;AACA,wCAAwC,SAAS,GAAG,MAAW;AAC/D;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,4BAA4B,GAAW;AACvC,qCAAqC,iBAAgB;AACrD,yBAAyB,IAAI,CAAC,kBAAuB;AACrD;AACA,qCAAqC,gBAAqB;AAC1D,yCAAyC,OAAoB;AAC7D,mCAAmC,cAAmB;AACtD;AACA;AACA,kCAAkC,aAAa;AAC/C,sCAAsC,iBAAiB;AACvD,sCAAsC,iBAAiB;AACvD,0BAA0B,KAAK;AAC/B;AACA,2CAA2C,MAAuB;AAClE,6BAA6B,QAAQ;AACrC;AACA;AACA,mCAAmC,cAAc;AACjD;AACA,yCAAyC,sBAAqB;AAC9D;AACA;AACA;AACA,iCAAiC,IAAY;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,sBAAS;AACX;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,sBAAsB;AACtB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,qBAAK,GAAG,kCAAkC;AACxD;;AAEA;AACA,IAAI,oBAAO,GAAG,0DAA0D,cAAM;AAC9E;;AAEA,EAAE,sBAAS;AACX;AACA,iBAAiB,uBAAU;AAC3B;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA;AACA;AACA,MAAM,oBAAO,GAAG,uDAAuD,cAAM;AAC7E;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA,oBAAoB;AACpB,MAAM,YAAiB;AACvB;AACA,GAAG;AACH;;AAEA;AACA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAK;AACrB;AACA,GAAG;AACH;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,EAAE,sBAAS;AACX;AACA,GAAG;AACH,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA,EAAE,sBAAS;AACX;AACA;AACA;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA,IAAI,sBAAS;AACb;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,wBAAwB,aAAkB;AAC1C,qBAAqB,aAAkB;AACvC,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA,qBAAqB,6BAAM;AAC3B,qBAAqB,6BAAM;;AAE3B;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,EAAE,sBAAS;AACX;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA,4BAA4B,wBAAwB;AACpD;AACA;AACA,GAAG;AACH;;AAEA;AACA,eAAe,gBAAqB;AACpC,EAAE,sBAAS;AACX;AACA,IAAI,sBAAS;AACb;AACA;AACA,MAAM,sBAAS;AACf,QAAQ,YAAiB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL,GAAG;AACH;;AAEA;AACA,EAAE,sBAAS;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,gCAAgC;AAC1C,UAAU,gCAAgC;AAC1C,UAAU,iBAAiB;AAC3B,UAAU,gCAAgC;AAC1C,UAAU,gCAAgC;AAC1C;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,SAAS,mBAAW,CAAC,cAAM;AAC3B;;AAEA;AACA;AACA,EAAE,sBAAS;AACX;AACA,GAAG;AACH;AACA;;;ACrZwC;AACH;AACO;AACL;;AAEK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLA;AACN;AACA;AACiB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA,YAAY,4BAAQ;AACpB,SAAS,4BAAQ,CAAC,+BAAW,YAAY,gCAAiB;AAC1D,CAAC;;AAED,sDAAe,KAAK,EAAC;;;;;;;ACzBU;;AAE/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oEAAoE,WAAW;AAC/E;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oEAAoE,WAAW;AAC/E;AACA;AACA;AACA;AACO;AACP,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,+BAA+B,uBAAU;;AAEzC;AACA,+BAA+B,uBAAU;;AAEzC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAY;AACrB,mBAAmB,uBAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAM;AACjB;AACA;AACA;AACA,WAAW,qBAAQ;AACnB,aAAa,sBAAS;AACtB,KAAK;AACL;AACA;AACA;AACA,WAAW,qBAAQ;AACnB,aAAa,sBAAS;AACtB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,sBAAM;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,sBAAM;AACd;AACA,SAAS;AACT;AACA;AACA,MAAM,sBAAM,CAAC,mBAAM;AACnB;AACA;AACA,MAAM,sBAAM,CAAC,mBAAM;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,0BAAa;AACrB;AACA,MAAM;AACN;AACA;AACA,kCAAkC,CAAC,0BAAa,YAAY;AAC5D;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAAa;AACrB;AACA;;AAEA;AACA;AACA;AACA,eAAe,mBAAM;AACrB;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,mBAAM;AACnB;AACA;AACA;AACA;AACA;AACA,aAAa,mBAAM;AACnB;AACA;AACA;AACA;AACA;AACA,aAAa,eAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA,IAAI,sBAAM;AACV;AACA;AACA;AACA,KAAK;;AAEL,IAAI,sBAAM;AACV;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA,MAAM,sBAAM;AACZ;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA,SAAS,yBAAY;AACrB,mBAAmB,uBAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,qBAAQ;AACnB;AACA;AACA;AACA;AACA,IAAI,qBAAQ;AACZ;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,eAAe,cAAc;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS,0BAAa;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,0BAAa;AACtB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAQ;AAC1B;AACA;AACA;AACA,aAAa,qBAAQ;AACrB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAQ;AAC1B;AACA;AACA;AACA,aAAa,qBAAQ;AACrB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,0BAAa;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;;AChgBA;;AAEmC;;AAEnC;;AAE0B;;;;;;;;;;;;ACNW;;AAErC;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,GAAG;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qCAAqC,iEAAQ;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iEAAe,YAAY,EAAC;;;;;;;;;;;AC/B5B;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA,iEAAe,MAAM,EAAC;;;;;;;;;;;;;ACbgB;AACK;;AAE3C;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;AACA,eAAe,oEAAW;;AAE1B,EAAE,iEAAQ;AACV;AACA,GAAG;AACH;AACA;;AAEA,iEAAe,OAAO,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBqB;AACN;AACF;AACC;AACL;;AAEhC;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,cAAc;AACzB,WAAW,GAAG;AACd,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA,OAAO,2BAAQ;AACf;AACA;AACA,SAAS,4BAAQ;;AAEjB;AACA;AACA;AACA;;AAEA;AACA,cAAc,yBAAK;AACnB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,2BAAQ;AAC3B;AACA,aAAa,2BAAO,2BAA2B;AAC/C;AACA;AACA,IAAI,+BAAW;AACf;AACA;AACA;AACA;;AAEA,+CAAe,OAAO,EAAC;;;AClDa;AACA;AACE;;AAEtC;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,2BAAO;;AAEvB;AACA,MAAM,QAAO,SAAS,4BAAQ;AAC9B;AACA;AACA;AACA;;AAEA,kDAAe,UAAU,EAAC;;;;;;;;;;;;;;;AC7BY;AACb;AACyB;AACjB;;AAEjC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,WAAW;AACtB,aAAa,QAAQ;AACrB;AACA;AACA;AACA,gBAAgB,QAAQ,IAAI,QAAQ,IAAI,QAAQ;AAChD,WAAW;AACX;AACA,eAAe,iEAAQ;AACvB;;AAEA;AACA;AACA;;AAEA,eAAe,uEAAc;AAC7B;AACA;;AAEA;AACA;AACA,gBAAgB,+DAAM;AACtB;AACA;;AAEA;AACA;AACA;;AAEA;AACA,WAAW,2DAAE;AACb;AACA;AACA;AACA;;AAEA;AACA,CAAC;;AAED,iEAAe,QAAQ,EAAC;;;;;;;;;;;;;;;;;;;;;AC/DsB;AACH;AACd;;AAE7B;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA,SAAS,8BAAW;AACpB,qBAAqB,gCAAY;AACjC,mBAAmB,uBAAI;AACvB,kCAAkC;AAClC;AACA;AACA;AACA;AACA;;AAEA,kDAAe,UAAU,EAAC;;;;;;;ACxBsB;AACF;AACP;;AAEvC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA,OAAO,oCAAoC;AAC3C,OAAO,oCAAoC;AAC3C,OAAO;AACP;AACA;AACA,oCAAoC,4BAA4B;AAChE;AACA;AACA;AACA,wBAAwB,iCAAiC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,4BAAS;AAC/C;AACA;AACA;AACA,SAAS,iCAAa,QAAQ,gCAAY;AAC1C;;AAEA,0DAAe,SAAS,EAAC;;;ACtDiB;AACH;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA,OAAO,8CAA8C;AACrD,OAAO,+CAA+C;AACtD,OAAO;AACP;AACA;AACA,+BAA+B,oBAAoB;AACnD;AACA;AACA;AACA,mBAAmB,0BAA0B;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAU,CAAC,mBAAS;;AAE/B,qDAAe,IAAI,EAAC;;;;;;;;;;;;ACzCwB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,oEAAW;AAC7B;;AAEA,iEAAe,OAAO,EAAC;;;;;;;;;;;;;;;ACrBvB;AACA;;AAEA;AACA,IAAI,uBAAc;;AAElB;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,cAAc;AACzB,aAAa,SAAS;AACtB;AACA;AACA,2BAA2B,uBAAc;AACzC;;AAEA,+CAAe,OAAO,EAAC;;;;;AClBa;AACA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,cAAc;AACzB,aAAa,SAAS;AACtB;AACA;AACA,kBAAkB,OAAO;AACzB,0BAA0B,gBAAgB,QAAQ,GAAG;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAAO,eAAe,QAAO;AACxD;;AAEA,oDAAe,GAAG,EAAC;;;;;;;;;;;;;;AClCuB;AACP;AACU;;AAE7C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gEAAO,WAAW,qEAAY,WAAW,mEAAU;AACzD;;AAEA,iEAAe,QAAQ,EAAC;;;;;;;;;;;AC7BxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iEAAe,IAAI,EAAC;;;;;;;;;;;;;;;ACnBkB;AACQ;AACV;AACD;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,gBAAgB;AAC3B;AACA;AACA;AACA,OAAO,kBAAkB;AACzB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,gEAAO,eAAe,6DAAQ,GAAG,4DAAO;AACrD,0BAA0B,qEAAY;AACtC;;AAEA,iEAAe,GAAG,EAAC;;;;;;;;;;;;;;ACpD2B;AACZ;AACG;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAY,QAAQ,6DAAQ,EAAE,2DAAM;AAC1C;AACA;;AAEA,iEAAe,GAAG,EAAC;;;;;;;;;;;;;;;AC5BnB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uDAAe,eAAe,EAAC;;;AClBqB;;AAEpD;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA,sBAAsB,gBAAe;AACrC;AACA;;AAEA,gDAAe,QAAQ,EAAC;;;;;;;AClBc;AACD;AACA;;AAErC;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,2BAAQ;AACd;AACA;AACA,MAAM,2BAAQ;AACd;AACA,YAAY,2BAAQ;AACpB;AACA;AACA;AACA;AACA,UAAU,SAAQ;AAClB;AACA;AACA;AACA;AACA;;AAEA,yDAAe,QAAQ,EAAC;;;AC/Da;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,kBAAQ;AAClB;AACA;AACA;AACA;AACA;AACA;;AAEA,yDAAe,QAAQ,EAAC;;;;;;;;;;;;ACzCa;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,iEAAQ;AACvB;;AAEA;AACA;;AAEA,iEAAe,SAAS,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/uniqueId.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseRange.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_createRange.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/range.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/data/list.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/greedy-fas.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/acyclic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_basePick.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_flatRest.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/pick.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseGt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/max.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/mapValues.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/now.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/util.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/add-border-segments.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/coordinate-system.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/normalize.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/minBy.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/rank/util.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/dijkstra.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/dijkstra-all.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/floyd-warshall.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_asciiSize.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_hasUnicode.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_unicodeSize.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_stringSize.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/size.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/topsort.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/is-acyclic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/dfs.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/postorder.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/preorder.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/prim.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/alg/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/rank/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/nesting-graph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/cloneDeep.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseZipObject.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/zipObject.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseSortBy.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_compareAscending.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_compareMultiple.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseOrderBy.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/sortBy.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/cross-count.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/init-order.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/barycenter.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/sort.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/order/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/forOwn.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/forIn.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/position/bk.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/position/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/layout.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/dagre/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/union.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/graph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseExtremum.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseLt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseMap.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseSet.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_basePickBy.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/defaults.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_createFind.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/findIndex.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/find.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/flatten.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseHas.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/has.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/isString.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/last.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/map.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/min.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_trimmedEndIndex.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/_baseTrim.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/toNumber.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/toFinite.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/toInteger.js"], "sourcesContent": ["import toString from './toString.js';\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nexport default uniqueId;\n", "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * The base implementation of `_.range` and `_.rangeRight` which doesn't\n * coerce arguments.\n *\n * @private\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @param {number} step The value to increment or decrement by.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the range of numbers.\n */\nfunction baseRange(start, end, step, fromRight) {\n  var index = -1,\n      length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),\n      result = Array(length);\n\n  while (length--) {\n    result[fromRight ? length : ++index] = start;\n    start += step;\n  }\n  return result;\n}\n\nexport default baseRange;\n", "import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function(start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nexport default createRange;\n", "import createRange from './_createRange.js';\n\n/**\n * Creates an array of numbers (positive and/or negative) progressing from\n * `start` up to, but not including, `end`. A step of `-1` is used if a negative\n * `start` is specified without an `end` or `step`. If `end` is not specified,\n * it's set to `start` with `start` then set to `0`.\n *\n * **Note:** JavaScript follows the IEEE-754 standard for resolving\n * floating-point values which can produce unexpected results.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @param {number} [step=1] The value to increment or decrement by.\n * @returns {Array} Returns the range of numbers.\n * @see _.inRange, _.rangeRight\n * @example\n *\n * _.range(4);\n * // => [0, 1, 2, 3]\n *\n * _.range(-4);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 5);\n * // => [1, 2, 3, 4]\n *\n * _.range(0, 20, 5);\n * // => [0, 5, 10, 15]\n *\n * _.range(0, -4, -1);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 4, 0);\n * // => [1, 1, 1]\n *\n * _.range(0);\n * // => []\n */\nvar range = createRange();\n\nexport default range;\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    }),\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (Object.prototype.hasOwnProperty.call(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nexport default basePick;\n", "import flatten from './flatten.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\n\nexport default flatRest;\n", "import basePick from './_basePick.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nexport default pick;\n", "/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\n\nexport default baseGt;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseGt from './_baseGt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the maximum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * _.max([4, 2, 8, 6]);\n * // => 8\n *\n * _.max([]);\n * // => undefined\n */\nfunction max(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseGt)\n    : undefined;\n}\n\nexport default max;\n", "import baseAssignValue from './_baseAssignValue.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * Creates an object with the same keys as `object` and values generated\n * by running each own enumerable string keyed property of `object` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapKeys\n * @example\n *\n * var users = {\n *   'fred':    { 'user': 'fred',    'age': 40 },\n *   'pebbles': { 'user': 'pebbles', 'age': 1 }\n * };\n *\n * _.mapValues(users, function(o) { return o.age; });\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n *\n * // The `_.property` iteratee shorthand.\n * _.mapValues(users, 'age');\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n */\nfunction mapValues(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, key, iteratee(value, key, object));\n  });\n  return result;\n}\n\nexport default mapValues;\n", "import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nexport default now;\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    }),\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "/**\n * TypeScript type imports:\n *\n * @import { Graph } from '../graphlib/graph.js';\n */\nimport * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\n/**\n * @param {Graph} g\n */\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  /**\n   * @typedef {Object} Attrs\n   * @property {number} width\n   * @property {number} height\n   * @property {ReturnType<Graph[\"node\"]>} edgeLabel\n   * @property {any} edgeObj\n   * @property {ReturnType<Graph[\"node\"]>[\"rank\"]} rank\n   * @property {string} [dummy]\n   * @property {ReturnType<Graph[\"node\"]>[\"labelpos\"]} [labelpos]\n   */\n\n  /** @type {Attrs | undefined} */\n  var attrs = undefined;\n  var dummy, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = 'edge-label';\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "import baseExtremum from './_baseExtremum.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseLt from './_baseLt.js';\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt)\n    : undefined;\n}\n\nexport default minBy;\n", "import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      }),\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\n\nexport { dijkstra };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(\n    g,\n    String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\n        'dijkstra does not allow negative edge weights. ' +\n          'Bad edge: ' +\n          edge +\n          ' Weight: ' +\n          weight,\n      );\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n", "import * as _ from 'lodash-es';\nimport { dijkstra } from './dijkstra.js';\n\nexport { dijkstraAll };\n\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(\n    g.nodes(),\n    function (acc, v) {\n      acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n    },\n    {},\n  );\n}\n", "import * as _ from 'lodash-es';\n\nexport { floyd<PERSON>ars<PERSON> };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(\n    g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function (v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function (w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function (edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function (k) {\n    var rowK = results[k];\n    nodes.forEach(function (i) {\n      var rowI = results[i];\n      nodes.forEach(function (j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n", "import baseProperty from './_baseProperty.js';\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\n\nexport default asciiSize;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsZWJ = '\\\\u200d';\n\n/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */\nvar reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboRange + rsVarRange + ']');\n\n/**\n * Checks if `string` contains Unicode symbols.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {boolean} Returns `true` if a symbol is found, else `false`.\n */\nfunction hasUnicode(string) {\n  return reHasUnicode.test(string);\n}\n\nexport default hasUnicode;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsAstral = '[' + rsAstralRange + ']',\n    rsCombo = '[' + rsComboRange + ']',\n    rsFitz = '\\\\ud83c[\\\\udffb-\\\\udfff]',\n    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',\n    rsNonAstral = '[^' + rsAstralRange + ']',\n    rsRegional = '(?:\\\\ud83c[\\\\udde6-\\\\uddff]){2}',\n    rsSurrPair = '[\\\\ud800-\\\\udbff][\\\\udc00-\\\\udfff]',\n    rsZWJ = '\\\\u200d';\n\n/** Used to compose unicode regexes. */\nvar reOptMod = rsModifier + '?',\n    rsOptVar = '[' + rsVarRange + ']?',\n    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',\n    rsSeq = rsOptVar + reOptMod + rsOptJoin,\n    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';\n\n/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */\nvar reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');\n\n/**\n * Gets the size of a Unicode `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nfunction unicodeSize(string) {\n  var result = reUnicode.lastIndex = 0;\n  while (reUnicode.test(string)) {\n    ++result;\n  }\n  return result;\n}\n\nexport default unicodeSize;\n", "import asciiSize from './_asciiSize.js';\nimport hasUnicode from './_hasUnicode.js';\nimport unicodeSize from './_unicodeSize.js';\n\n/**\n * Gets the number of symbols in `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the string size.\n */\nfunction stringSize(string) {\n  return hasUnicode(string)\n    ? unicodeSize(string)\n    : asciiSize(string);\n}\n\nexport default stringSize;\n", "import baseKeys from './_baseKeys.js';\nimport getTag from './_getTag.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport stringSize from './_stringSize.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/**\n * Gets the size of `collection` by returning its length for array-like\n * values or the number of own enumerable string keyed properties for objects.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @returns {number} Returns the collection size.\n * @example\n *\n * _.size([1, 2, 3]);\n * // => 3\n *\n * _.size({ 'a': 1, 'b': 2 });\n * // => 2\n *\n * _.size('pebbles');\n * // => 7\n */\nfunction size(collection) {\n  if (collection == null) {\n    return 0;\n  }\n  if (isArrayLike(collection)) {\n    return isString(collection) ? stringSize(collection) : collection.length;\n  }\n  var tag = getTag(collection);\n  if (tag == mapTag || tag == setTag) {\n    return collection.size;\n  }\n  return baseKeys(collection).length;\n}\n\nexport default size;\n", "import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.prototype.hasOwnProperty.call(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.prototype.hasOwnProperty.call(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n", "import { topsort, CycleException } from './topsort.js';\n\nexport { isAcyclic };\n\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}\n", "import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!Object.prototype.hasOwnProperty.call(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n", "import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n", "import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n", "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\nimport { Graph } from '../graph.js';\n\nexport { prim };\n\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n\n  _.each(g.nodes(), function (v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (Object.prototype.hasOwnProperty.call(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error('Input graph is not connected: ' + g);\n    } else {\n      init = true;\n    }\n\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n\n  return result;\n}\n", "import { components } from './components.js';\nimport { dijkstra } from './dijkstra.js';\nimport { dijkstraAll } from './dijkstra-all.js';\nimport { findCycles } from './find-cycles.js';\nimport { floydWarshall } from './floyd-warshall.js';\nimport { isAcyclic } from './is-acyclic.js';\nimport { postorder } from './postorder.js';\nimport { preorder } from './preorder.js';\nimport { prim } from './prim.js';\nimport { tarjan } from './tarjan.js';\nimport { topsort } from './topsort.js';\n\nexport {\n  components,\n  dijkstra,\n  dijkstraAll,\n  findCycles,\n  floydWarshall,\n  isAcyclic,\n  postorder,\n  preorder,\n  prim,\n  tarjan,\n  topsort,\n};\n", "import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!Object.prototype.hasOwnProperty.call(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0,\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);\n}\n\nexport default cloneDeep;\n", "import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n", "/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n      length = props.length,\n      valsLength = values.length,\n      result = {};\n\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\n\nexport default baseZipObject;\n", "import assignValue from './_assignValue.js';\nimport baseZipObject from './_baseZipObject.js';\n\n/**\n * This method is like `_.fromPairs` except that it accepts two arrays,\n * one of property identifiers and one of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 0.4.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObject(['a', 'b'], [1, 2]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction zipObject(props, values) {\n  return baseZipObject(props || [], values || [], assignValue);\n}\n\nexport default zipObject;\n", "/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\n\nexport default baseSortBy;\n", "import isSymbol from './isSymbol.js';\n\n/**\n * Compares values to sort them in ascending order.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {number} Returns the sort order indicator for `value`.\n */\nfunction compareAscending(value, other) {\n  if (value !== other) {\n    var valIsDefined = value !== undefined,\n        valIsNull = value === null,\n        valIsReflexive = value === value,\n        valIsSymbol = isSymbol(value);\n\n    var othIsDefined = other !== undefined,\n        othIsNull = other === null,\n        othIsReflexive = other === other,\n        othIsSymbol = isSymbol(other);\n\n    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||\n        (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||\n        (valIsNull && othIsDefined && othIsReflexive) ||\n        (!valIsDefined && othIsReflexive) ||\n        !valIsReflexive) {\n      return 1;\n    }\n    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||\n        (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||\n        (othIsNull && valIsDefined && valIsReflexive) ||\n        (!othIsDefined && valIsReflexive) ||\n        !othIsReflexive) {\n      return -1;\n    }\n  }\n  return 0;\n}\n\nexport default compareAscending;\n", "import compareAscending from './_compareAscending.js';\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n      objCriteria = object.criteria,\n      othCriteria = other.criteria,\n      length = objCriteria.length,\n      ordersLength = orders.length;\n\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\n\nexport default compareMultiple;\n", "import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nexport default baseOrderBy;\n", "import baseFlatten from './_baseFlatten.js';\nimport baseOrderBy from './_baseOrderBy.js';\nimport baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates an array of elements, sorted in ascending order by the results of\n * running each element in a collection thru each iteratee. This method\n * performs a stable sort, that is, it preserves the original sort order of\n * equal elements. The iteratees are invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {...(Function|Function[])} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 36 },\n *   { 'user': 'fred',   'age': 30 },\n *   { 'user': 'barney', 'age': 34 }\n * ];\n *\n * _.sortBy(users, [function(o) { return o.user; }]);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 30]]\n *\n * _.sortBy(users, ['user', 'age']);\n * // => objects for [['barney', 34], ['barney', 36], ['fred', 30], ['fred', 48]]\n */\nvar sortBy = baseRest(function(collection, iteratees) {\n  if (collection == null) {\n    return [];\n  }\n  var length = iteratees.length;\n  if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {\n    iteratees = [];\n  } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {\n    iteratees = [iteratees[0]];\n  }\n  return baseOrderBy(collection, baseFlatten(iteratees, 1), []);\n});\n\nexport default sortBy;\n", "import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n", "import * as _ from 'lodash-es';\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport function initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 },\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return Object.prototype.hasOwnProperty.call(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "import baseForOwn from './_baseForOwn.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * Iterates over own enumerable string keyed properties of an object and\n * invokes `iteratee` for each property. The iteratee is invoked with three\n * arguments: (value, key, object). Iteratee functions may exit iteration\n * early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwnRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a' then 'b' (iteration order is not guaranteed).\n */\nfunction forOwn(object, iteratee) {\n  return object && baseForOwn(object, castFunction(iteratee));\n}\n\nexport default forOwn;\n", "import baseFor from './_baseFor.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null\n    ? object\n    : baseFor(object, castFunction(iteratee), keysIn);\n}\n\nexport default forIn;\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.prototype.hasOwnProperty.call(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      }),\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', () => {\n    var layoutGraph = time('  buildLayoutGraph', () => buildLayoutGraph(g));\n    time('  runLayout', () => runLayout(layoutGraph, time));\n    time('  updateInputGraph', () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', () => makeSpaceForEdgeLabels(g));\n  time('    removeSelfEdges', () => removeSelfEdges(g));\n  time('    acyclic', () => acyclic.run(g));\n  time('    nestingGraph.run', () => nestingGraph.run(g));\n  time('    rank', () => rank(util.asNonCompoundGraph(g)));\n  time('    injectEdgeLabelProxies', () => injectEdgeLabelProxies(g));\n  time('    removeEmptyRanks', () => util.removeEmptyRanks(g));\n  time('    nestingGraph.cleanup', () => nestingGraph.cleanup(g));\n  time('    normalizeRanks', () => util.normalizeRanks(g));\n  time('    assignRankMinMax', () => assignRankMinMax(g));\n  time('    removeEdgeLabelProxies', () => removeEdgeLabelProxies(g));\n  time('    normalize.run', () => normalize.run(g));\n  time('    parentDummyChains', () => parentDummyChains(g));\n  time('    addBorderSegments', () => addBorderSegments(g));\n  time('    order', () => order(g));\n  time('    insertSelfEdges', () => insertSelfEdges(g));\n  time('    adjustCoordinateSystem', () => coordinateSystem.adjust(g));\n  time('    position', () => position(g));\n  time('    positionSelfEdges', () => positionSelfEdges(g));\n  time('    removeBorderNodes', () => removeBorderNodes(g));\n  time('    normalize.undo', () => normalize.undo(g));\n  time('    fixupEdgeLabelCoords', () => fixupEdgeLabelCoords(g));\n  time('    undoCoordinateSystem', () => coordinateSystem.undo(g));\n  time('    translateGraph', () => translateGraph(g));\n  time('    assignNodeIntersects', () => assignNodeIntersects(g));\n  time('    reversePoints', () => reversePointsForReversedEdges(g));\n  time('    acyclic.undo', () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.prototype.hasOwnProperty.call(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)),\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)),\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se',\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n", "import * as acyclic from './acyclic.js';\nimport { layout } from './layout.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\n\nexport { acyclic, normalize, rank, layout };\n", "import baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array of unique values, in order, from all given arrays using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * _.union([2], [1, 2]);\n * // => [2, 1]\n */\nvar union = baseRest(function(arrays) {\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));\n});\n\nexport default union;\n", "import * as _ from 'lodash-es';\n\nvar DEFAULT_EDGE_NAME = '\\x00';\nvar GRAPH_NODE = '\\x00';\nvar EDGE_KEY_DELIM = '\\x01';\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\nexport class Graph {\n  constructor(opts = {}) {\n    this._isDirected = Object.prototype.hasOwnProperty.call(opts, 'directed')\n      ? opts.directed\n      : true;\n    this._isMultigraph = Object.prototype.hasOwnProperty.call(opts, 'multigraph')\n      ? opts.multigraph\n      : false;\n    this._isCompound = Object.prototype.hasOwnProperty.call(opts, 'compound')\n      ? opts.compound\n      : false;\n\n    // Label for the graph itself\n    this._label = undefined;\n\n    // Defaults to be set when creating a new node\n    this._defaultNodeLabelFn = _.constant(undefined);\n\n    // Defaults to be set when creating a new edge\n    this._defaultEdgeLabelFn = _.constant(undefined);\n\n    // v -> label\n    this._nodes = {};\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n\n    // v -> edgeObj\n    this._in = {};\n\n    // u -> v -> Number\n    this._preds = {};\n\n    // v -> edgeObj\n    this._out = {};\n\n    // v -> w -> Number\n    this._sucs = {};\n\n    // e -> edgeObj\n    this._edgeObjs = {};\n\n    // e -> label\n    this._edgeLabels = {};\n  }\n  /* === Graph functions ========= */\n  isDirected() {\n    return this._isDirected;\n  }\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n  isCompound() {\n    return this._isCompound;\n  }\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n  graph() {\n    return this._label;\n  }\n  /* === Node functions ========== */\n  setDefaultNodeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultNodeLabelFn = newDefault;\n    return this;\n  }\n  nodeCount() {\n    return this._nodeCount;\n  }\n  nodes() {\n    return _.keys(this._nodes);\n  }\n  sources() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._in[v]);\n    });\n  }\n  sinks() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._out[v]);\n    });\n  }\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    _.each(vs, function (v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n  setNode(v, value) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    // @ts-expect-error\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n  node(v) {\n    return this._nodes[v];\n  }\n  hasNode(v) {\n    return Object.prototype.hasOwnProperty.call(this._nodes, v);\n  }\n  removeNode(v) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      var removeEdge = (e) => this.removeEdge(this._edgeObjs[e]);\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        _.each(this.children(v), (child) => {\n          this.setParent(child);\n        });\n        delete this._children[v];\n      }\n      _.each(_.keys(this._in[v]), removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      _.each(_.keys(this._out[v]), removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error('Cannot set parent in a non-compound graph');\n    }\n\n    if (_.isUndefined(parent)) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += '';\n      for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error('Setting ' + parent + ' as parent of ' + v + ' would create a cycle');\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n  children(v) {\n    if (_.isUndefined(v)) {\n      v = GRAPH_NODE;\n    }\n\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return _.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return _.keys(predsV);\n    }\n  }\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return _.keys(sucsV);\n    }\n  }\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      return _.union(preds, this.successors(v));\n    }\n  }\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n  filterNodes(filter) {\n    // @ts-expect-error\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound,\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    _.each(this._nodes, function (value, v) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    _.each(this._edgeObjs, function (e) {\n      // @ts-expect-error\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      _.each(copy.nodes(), function (v) {\n        copy.setParent(v, findParent(v));\n      });\n    }\n\n    return copy;\n  }\n  /* === Edge functions ========== */\n  setDefaultEdgeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultEdgeLabelFn = newDefault;\n    return this;\n  }\n  edgeCount() {\n    return this._edgeCount;\n  }\n  edges() {\n    return _.values(this._edgeObjs);\n  }\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    _.reduce(vs, function (v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n  /*\n   * setEdge(v, w, [value, [name]])\n   * setEdge({ v, w, [name] }, [value])\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === 'object' && arg0 !== null && 'v' in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = '' + v;\n    w = '' + w;\n    if (!_.isUndefined(name)) {\n      name = '' + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (Object.prototype.hasOwnProperty.call(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (!_.isUndefined(name) && !this._isMultigraph) {\n      throw new Error('Cannot set a named edge when isMultigraph = false');\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    // @ts-expect-error\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n  edge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return this._edgeLabels[e];\n  }\n  hasEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return Object.prototype.hasOwnProperty.call(this._edgeLabels, e);\n  }\n  removeEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = _.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.v === u;\n      });\n    }\n  }\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = _.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.w === w;\n      });\n    }\n  }\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) {\n    delete map[k];\n  }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n", "// Includes only the \"core\" of graphlib\n\nimport { Graph } from './graph.js';\n\nconst version = '2.1.9-pre';\n\nexport { Graph, version };\n", "import isSymbol from './isSymbol.js';\n\n/**\n * The base implementation of methods like `_.max` and `_.min` which accepts a\n * `comparator` to determine the extremum value.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The iteratee invoked per iteration.\n * @param {Function} comparator The comparator used to compare values.\n * @returns {*} Returns the extremum value.\n */\nfunction baseExtremum(array, iteratee, comparator) {\n  var index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var value = array[index],\n        current = iteratee(value);\n\n    if (current != null && (computed === undefined\n          ? (current === current && !isSymbol(current))\n          : comparator(current, computed)\n        )) {\n      var computed = current,\n          result = value;\n    }\n  }\n  return result;\n}\n\nexport default baseExtremum;\n", "/**\n * The base implementation of `_.lt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n */\nfunction baseLt(value, other) {\n  return value < other;\n}\n\nexport default baseLt;\n", "import baseEach from './_baseEach.js';\nimport isArrayLike from './isArrayLike.js';\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n      result = isArrayLike(collection) ? Array(collection.length) : [];\n\n  baseEach(collection, function(value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\n\nexport default baseMap;\n", "import assignValue from './_assignValue.js';\nimport castPath from './_castPath.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nexport default baseSet;\n", "import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\nimport castPath from './_castPath.js';\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nexport default basePickBy;\n", "import baseRest from './_baseRest.js';\nimport eq from './eq.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport keysIn from './keysIn.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns own and inherited enumerable string keyed properties of source\n * objects to the destination object for all destination properties that\n * resolve to `undefined`. Source objects are applied from left to right.\n * Once a property is set, additional values of the same property are ignored.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaultsDeep\n * @example\n *\n * _.defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar defaults = baseRest(function(object, sources) {\n  object = Object(object);\n\n  var index = -1;\n  var length = sources.length;\n  var guard = length > 2 ? sources[2] : undefined;\n\n  if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n    length = 1;\n  }\n\n  while (++index < length) {\n    var source = sources[index];\n    var props = keysIn(source);\n    var propsIndex = -1;\n    var propsLength = props.length;\n\n    while (++propsIndex < propsLength) {\n      var key = props[propsIndex];\n      var value = object[key];\n\n      if (value === undefined ||\n          (eq(value, objectProto[key]) && !hasOwnProperty.call(object, key))) {\n        object[key] = source[key];\n      }\n    }\n  }\n\n  return object;\n});\n\nexport default defaults;\n", "import baseIteratee from './_baseIteratee.js';\nimport isArrayLike from './isArrayLike.js';\nimport keys from './keys.js';\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function(collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function(key) { return iteratee(iterable[key], key, iterable); };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\n\nexport default createFind;\n", "import baseFindIndex from './_baseFindIndex.js';\nimport baseIteratee from './_baseIteratee.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.find` except that it returns the index of the first\n * element `predicate` returns truthy for instead of the element itself.\n *\n * @static\n * @memberOf _\n * @since 1.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': false },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': true }\n * ];\n *\n * _.findIndex(users, function(o) { return o.user == 'barney'; });\n * // => 0\n *\n * // The `_.matches` iteratee shorthand.\n * _.findIndex(users, { 'user': 'fred', 'active': false });\n * // => 1\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findIndex(users, ['active', false]);\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.findIndex(users, 'active');\n * // => 2\n */\nfunction findIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = fromIndex == null ? 0 : toInteger(fromIndex);\n  if (index < 0) {\n    index = nativeMax(length + index, 0);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index);\n}\n\nexport default findIndex;\n", "import createFind from './_createFind.js';\nimport findIndex from './findIndex.js';\n\n/**\n * Iterates over elements of `collection`, returning the first element\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {*} Returns the matched element, else `undefined`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36, 'active': true },\n *   { 'user': 'fred',    'age': 40, 'active': false },\n *   { 'user': 'pebbles', 'age': 1,  'active': true }\n * ];\n *\n * _.find(users, function(o) { return o.age < 40; });\n * // => object for 'barney'\n *\n * // The `_.matches` iteratee shorthand.\n * _.find(users, { 'age': 1, 'active': true });\n * // => object for 'pebbles'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.find(users, ['active', false]);\n * // => object for 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.find(users, 'active');\n * // => object for 'barney'\n */\nvar find = createFind(findIndex);\n\nexport default find;\n", "import baseFlatten from './_baseFlatten.js';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\n\nexport default flatten;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.has` without support for deep paths.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {Array|string} key The key to check.\n * @returns {boolean} Returns `true` if `key` exists, else `false`.\n */\nfunction baseHas(object, key) {\n  return object != null && hasOwnProperty.call(object, key);\n}\n\nexport default baseHas;\n", "import baseHas from './_baseHas.js';\nimport hasPath from './_hasPath.js';\n\n/**\n * Checks if `path` is a direct property of `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n * @example\n *\n * var object = { 'a': { 'b': 2 } };\n * var other = _.create({ 'a': _.create({ 'b': 2 }) });\n *\n * _.has(object, 'a');\n * // => true\n *\n * _.has(object, 'a.b');\n * // => true\n *\n * _.has(object, ['a', 'b']);\n * // => true\n *\n * _.has(other, 'a');\n * // => false\n */\nfunction has(object, path) {\n  return object != null && hasPath(object, path, baseHas);\n}\n\nexport default has;\n", "import baseGetTag from './_baseGetTag.js';\nimport isArray from './isArray.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);\n}\n\nexport default isString;\n", "/**\n * Gets the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the last element of `array`.\n * @example\n *\n * _.last([1, 2, 3]);\n * // => 3\n */\nfunction last(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? array[length - 1] : undefined;\n}\n\nexport default last;\n", "import arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates an array of values by running each element in `collection` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, index|key, collection).\n *\n * Many lodash methods are guarded to work as iteratees for methods like\n * `_.every`, `_.filter`, `_.map`, `_.mapValues`, `_.reject`, and `_.some`.\n *\n * The guarded methods are:\n * `ary`, `chunk`, `curry`, `curryRight`, `drop`, `dropRight`, `every`,\n * `fill`, `invert`, `parseInt`, `random`, `range`, `rangeRight`, `repeat`,\n * `sampleSize`, `slice`, `some`, `sortBy`, `split`, `take`, `takeRight`,\n * `template`, `trim`, `trimEnd`, `trimStart`, and `words`\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * _.map([4, 8], square);\n * // => [16, 64]\n *\n * _.map({ 'a': 4, 'b': 8 }, square);\n * // => [16, 64] (iteration order is not guaranteed)\n *\n * var users = [\n *   { 'user': 'barney' },\n *   { 'user': 'fred' }\n * ];\n *\n * // The `_.property` iteratee shorthand.\n * _.map(users, 'user');\n * // => ['barney', 'fred']\n */\nfunction map(collection, iteratee) {\n  var func = isArray(collection) ? arrayMap : baseMap;\n  return func(collection, baseIteratee(iteratee, 3));\n}\n\nexport default map;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseLt from './_baseLt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the minimum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * _.min([4, 2, 8, 6]);\n * // => 2\n *\n * _.min([]);\n * // => undefined\n */\nfunction min(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseLt)\n    : undefined;\n}\n\nexport default min;\n", "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nexport default trimmedEndIndex;\n", "import trimmedEndIndex from './_trimmedEndIndex.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nexport default baseTrim;\n", "import baseTrim from './_baseTrim.js';\nimport isObject from './isObject.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nexport default toNumber;\n", "import toNumber from './toNumber.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308;\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\nexport default toFinite;\n", "import toFinite from './toFinite.js';\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\nexport default toInteger;\n"], "names": [], "sourceRoot": ""}