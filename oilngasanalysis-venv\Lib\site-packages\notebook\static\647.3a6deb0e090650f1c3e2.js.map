{"version": 3, "file": "647.3a6deb0e090650f1c3e2.js?v=3a6deb0e090650f1c3e2", "mappings": ";;;;;;;;;;AAAA,mBAAmB;AACnB,iBAAiB;;AAEV;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,QAAQ,+BAA+B;AACvC;AACA;AACA,QAAQ,+BAA+B;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,+BAA+B;AAC/B,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,gDAAgD;;AAEhD;AACA;AACA;AACA,+BAA+B;AAC/B;AACA,UAAU;AACV,+BAA+B;AAC/B;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ,4BAA4B;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/ebnf.js"], "sourcesContent": ["var commentType = {slash: 0, parenthesis: 1};\nvar stateType = {comment: 0, _string: 1, characterClass: 2};\n\nexport const ebnf = {\n  name: \"ebnf\",\n  startState: function () {\n    return {\n      stringType: null,\n      commentType: null,\n      braced: 0,\n      lhs: true,\n      localState: null,\n      stack: [],\n      inDefinition: false\n    };\n  },\n  token: function (stream, state) {\n    if (!stream) return;\n\n    //check for state changes\n    if (state.stack.length === 0) {\n      //strings\n      if ((stream.peek() == '\"') || (stream.peek() == \"'\")) {\n        state.stringType = stream.peek();\n        stream.next(); // Skip quote\n        state.stack.unshift(stateType._string);\n      } else if (stream.match('/*')) { //comments starting with /*\n        state.stack.unshift(stateType.comment);\n        state.commentType = commentType.slash;\n      } else if (stream.match('(*')) { //comments starting with (*\n        state.stack.unshift(stateType.comment);\n        state.commentType = commentType.parenthesis;\n      }\n    }\n\n    //return state\n    //stack has\n    switch (state.stack[0]) {\n    case stateType._string:\n      while (state.stack[0] === stateType._string && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.stack.shift(); // Clear flag\n        } else if (stream.peek() === \"\\\\\") {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return state.lhs ? \"property\" : \"string\"; // Token style\n\n    case stateType.comment:\n      while (state.stack[0] === stateType.comment && !stream.eol()) {\n        if (state.commentType === commentType.slash && stream.match('*/')) {\n          state.stack.shift(); // Clear flag\n          state.commentType = null;\n        } else if (state.commentType === commentType.parenthesis && stream.match('*)')) {\n          state.stack.shift(); // Clear flag\n          state.commentType = null;\n        } else {\n          stream.match(/^.[^\\*]*/);\n        }\n      }\n      return \"comment\";\n\n    case stateType.characterClass:\n      while (state.stack[0] === stateType.characterClass && !stream.eol()) {\n        if (!(stream.match(/^[^\\]\\\\]+/) || stream.match('.'))) {\n          state.stack.shift();\n        }\n      }\n      return \"operator\";\n    }\n\n    var peek = stream.peek();\n\n    //no stack\n    switch (peek) {\n    case \"[\":\n      stream.next();\n      state.stack.unshift(stateType.characterClass);\n      return \"bracket\";\n    case \":\":\n    case \"|\":\n    case \";\":\n      stream.next();\n      return \"operator\";\n    case \"%\":\n      if (stream.match(\"%%\")) {\n        return \"header\";\n      } else if (stream.match(/[%][A-Za-z]+/)) {\n        return \"keyword\";\n      } else if (stream.match(/[%][}]/)) {\n        return \"bracket\";\n      }\n      break;\n    case \"/\":\n      if (stream.match(/[\\/][A-Za-z]+/)) {\n        return \"keyword\";\n      }\n    case \"\\\\\":\n      if (stream.match(/[\\][a-z]+/)) {\n        return \"string.special\";\n      }\n    case \".\":\n      if (stream.match(\".\")) {\n        return \"atom\";\n      }\n    case \"*\":\n    case \"-\":\n    case \"+\":\n    case \"^\":\n      if (stream.match(peek)) {\n        return \"atom\";\n      }\n    case \"$\":\n      if (stream.match(\"$$\")) {\n        return \"builtin\";\n      } else if (stream.match(/[$][0-9]+/)) {\n        return \"variableName.special\";\n      }\n    case \"<\":\n      if (stream.match(/<<[a-zA-Z_]+>>/)) {\n        return \"builtin\";\n      }\n    }\n\n    if (stream.match('//')) {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (stream.match('return')) {\n      return \"operator\";\n    } else if (stream.match(/^[a-zA-Z_][a-zA-Z0-9_]*/)) {\n      if (stream.match(/(?=[\\(.])/)) {\n        return \"variable\";\n      } else if (stream.match(/(?=[\\s\\n]*[:=])/)) {\n        return \"def\";\n      }\n      return \"variableName.special\";\n    } else if ([\"[\", \"]\", \"(\", \")\"].indexOf(stream.peek()) != -1) {\n      stream.next();\n      return \"bracket\";\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  }\n};\n"], "names": [], "sourceRoot": ""}