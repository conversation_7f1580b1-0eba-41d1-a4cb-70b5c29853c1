{"version": 3, "file": "7022.ada0a27a1f0d61d90ee8.js?v=ada0a27a1f0d61d90ee8", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;;AAEzB;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,4BAA4B,QAAQ,0DAA0D;AAC9F;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAEM;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAEM;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/mllike.js"], "sourcesContent": ["function mlLike(parserConfig) {\n  var words = {\n    'as': 'keyword',\n    'do': 'keyword',\n    'else': 'keyword',\n    'end': 'keyword',\n    'exception': 'keyword',\n    'fun': 'keyword',\n    'functor': 'keyword',\n    'if': 'keyword',\n    'in': 'keyword',\n    'include': 'keyword',\n    'let': 'keyword',\n    'of': 'keyword',\n    'open': 'keyword',\n    'rec': 'keyword',\n    'struct': 'keyword',\n    'then': 'keyword',\n    'type': 'keyword',\n    'val': 'keyword',\n    'while': 'keyword',\n    'with': 'keyword'\n  };\n\n  var extraWords = parserConfig.extraWords || {};\n  for (var prop in extraWords) {\n    if (extraWords.hasOwnProperty(prop)) {\n      words[prop] = parserConfig.extraWords[prop];\n    }\n  }\n  var hintWords = [];\n  for (var k in words) { hintWords.push(k); }\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    if (ch === '\"') {\n      state.tokenize = tokenString;\n      return state.tokenize(stream, state);\n    }\n    if (ch === '{') {\n      if (stream.eat('|')) {\n        state.longString = true;\n        state.tokenize = tokenLongString;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '(') {\n      if (stream.match(/^\\*(?!\\))/)) {\n        state.commentLevel++;\n        state.tokenize = tokenComment;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '~' || ch === '?') {\n      stream.eatWhile(/\\w/);\n      return 'variableName.special';\n    }\n    if (ch === '`') {\n      stream.eatWhile(/\\w/);\n      return 'quote';\n    }\n    if (ch === '/' && parserConfig.slashComments && stream.eat('/')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n    if (/\\d/.test(ch)) {\n      if (ch === '0' && stream.eat(/[bB]/)) {\n        stream.eatWhile(/[01]/);\n      } if (ch === '0' && stream.eat(/[xX]/)) {\n        stream.eatWhile(/[0-9a-fA-F]/)\n      } if (ch === '0' && stream.eat(/[oO]/)) {\n        stream.eatWhile(/[0-7]/);\n      } else {\n        stream.eatWhile(/[\\d_]/);\n        if (stream.eat('.')) {\n          stream.eatWhile(/[\\d]/);\n        }\n        if (stream.eat(/[eE]/)) {\n          stream.eatWhile(/[\\d\\-+]/);\n        }\n      }\n      return 'number';\n    }\n    if ( /[+\\-*&%=<>!?|@\\.~:]/.test(ch)) {\n      return 'operator';\n    }\n    if (/[\\w\\xa1-\\uffff]/.test(ch)) {\n      stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n      var cur = stream.current();\n      return words.hasOwnProperty(cur) ? words[cur] : 'variable';\n    }\n    return null\n  }\n\n  function tokenString(stream, state) {\n    var next, end = false, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === '\"' && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    if (end && !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  };\n\n  function tokenComment(stream, state) {\n    var prev, next;\n    while(state.commentLevel > 0 && (next = stream.next()) != null) {\n      if (prev === '(' && next === '*') state.commentLevel++;\n      if (prev === '*' && next === ')') state.commentLevel--;\n      prev = next;\n    }\n    if (state.commentLevel <= 0) {\n      state.tokenize = tokenBase;\n    }\n    return 'comment';\n  }\n\n  function tokenLongString(stream, state) {\n    var prev, next;\n    while (state.longString && (next = stream.next()) != null) {\n      if (prev === '|' && next === '}') state.longString = false;\n      prev = next;\n    }\n    if (!state.longString) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  }\n\n  return {\n    startState: function() {return {tokenize: tokenBase, commentLevel: 0, longString: false};},\n    token: function(stream, state) {\n      if (stream.eatSpace()) return null;\n      return state.tokenize(stream, state);\n    },\n\n    languageData: {\n      autocomplete: hintWords,\n      commentTokens: {\n        line: parserConfig.slashComments ? \"//\" : undefined,\n        block: {open: \"(*\", close: \"*)\"}\n      }\n    }\n  };\n};\n\nexport const oCaml = mlLike({\n  name: \"ocaml\",\n  extraWords: {\n    'and': 'keyword',\n    'assert': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'constraint': 'keyword',\n    'done': 'keyword',\n    'downto': 'keyword',\n    'external': 'keyword',\n    'function': 'keyword',\n    'initializer': 'keyword',\n    'lazy': 'keyword',\n    'match': 'keyword',\n    'method': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'new': 'keyword',\n    'nonrec': 'keyword',\n    'object': 'keyword',\n    'private': 'keyword',\n    'sig': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'value': 'keyword',\n    'virtual': 'keyword',\n    'when': 'keyword',\n\n    // builtins\n    'raise': 'builtin',\n    'failwith': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    // Pervasives builtins\n    'asr': 'builtin',\n    'land': 'builtin',\n    'lor': 'builtin',\n    'lsl': 'builtin',\n    'lsr': 'builtin',\n    'lxor': 'builtin',\n    'mod': 'builtin',\n    'or': 'builtin',\n\n    // More Pervasives\n    'raise_notrace': 'builtin',\n    'trace': 'builtin',\n    'exit': 'builtin',\n    'print_string': 'builtin',\n    'print_endline': 'builtin',\n\n     'int': 'type',\n     'float': 'type',\n     'bool': 'type',\n     'char': 'type',\n     'string': 'type',\n     'unit': 'type',\n\n     // Modules\n     'List': 'builtin'\n  }\n});\n\nexport const fSharp = mlLike({\n  name: \"fsharp\",\n  extraWords: {\n    'abstract': 'keyword',\n    'assert': 'keyword',\n    'base': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'default': 'keyword',\n    'delegate': 'keyword',\n    'do!': 'keyword',\n    'done': 'keyword',\n    'downcast': 'keyword',\n    'downto': 'keyword',\n    'elif': 'keyword',\n    'extern': 'keyword',\n    'finally': 'keyword',\n    'for': 'keyword',\n    'function': 'keyword',\n    'global': 'keyword',\n    'inherit': 'keyword',\n    'inline': 'keyword',\n    'interface': 'keyword',\n    'internal': 'keyword',\n    'lazy': 'keyword',\n    'let!': 'keyword',\n    'match': 'keyword',\n    'member': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'namespace': 'keyword',\n    'new': 'keyword',\n    'null': 'keyword',\n    'override': 'keyword',\n    'private': 'keyword',\n    'public': 'keyword',\n    'return!': 'keyword',\n    'return': 'keyword',\n    'select': 'keyword',\n    'static': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'upcast': 'keyword',\n    'use!': 'keyword',\n    'use': 'keyword',\n    'void': 'keyword',\n    'when': 'keyword',\n    'yield!': 'keyword',\n    'yield': 'keyword',\n\n    // Reserved words\n    'atomic': 'keyword',\n    'break': 'keyword',\n    'checked': 'keyword',\n    'component': 'keyword',\n    'const': 'keyword',\n    'constraint': 'keyword',\n    'constructor': 'keyword',\n    'continue': 'keyword',\n    'eager': 'keyword',\n    'event': 'keyword',\n    'external': 'keyword',\n    'fixed': 'keyword',\n    'method': 'keyword',\n    'mixin': 'keyword',\n    'object': 'keyword',\n    'parallel': 'keyword',\n    'process': 'keyword',\n    'protected': 'keyword',\n    'pure': 'keyword',\n    'sealed': 'keyword',\n    'tailcall': 'keyword',\n    'trait': 'keyword',\n    'virtual': 'keyword',\n    'volatile': 'keyword',\n\n    // builtins\n    'List': 'builtin',\n    'Seq': 'builtin',\n    'Map': 'builtin',\n    'Set': 'builtin',\n    'Option': 'builtin',\n    'int': 'builtin',\n    'string': 'builtin',\n    'not': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    'raise': 'builtin',\n    'failwith': 'builtin'\n  },\n  slashComments: true\n});\n\nexport const sml = mlLike({\n  name: \"sml\",\n  extraWords: {\n    'abstype': 'keyword',\n    'and': 'keyword',\n    'andalso': 'keyword',\n    'case': 'keyword',\n    'datatype': 'keyword',\n    'fn': 'keyword',\n    'handle': 'keyword',\n    'infix': 'keyword',\n    'infixr': 'keyword',\n    'local': 'keyword',\n    'nonfix': 'keyword',\n    'op': 'keyword',\n    'orelse': 'keyword',\n    'raise': 'keyword',\n    'withtype': 'keyword',\n    'eqtype': 'keyword',\n    'sharing': 'keyword',\n    'sig': 'keyword',\n    'signature': 'keyword',\n    'structure': 'keyword',\n    'where': 'keyword',\n    'true': 'keyword',\n    'false': 'keyword',\n\n    // types\n    'int': 'builtin',\n    'real': 'builtin',\n    'string': 'builtin',\n    'char': 'builtin',\n    'bool': 'builtin'\n  },\n  slashComments: true\n});\n"], "names": [], "sourceRoot": ""}