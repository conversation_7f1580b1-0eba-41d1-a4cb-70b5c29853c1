"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[9890],{19163:(t,e,r)=>{r.d(e,{S:()=>n});var a=r(75905);function n(t,e){if(t.accDescr){e.setAccDescription?.(t.accDescr)}if(t.accTitle){e.setAccTitle?.(t.accTitle)}if(t.title){e.setDiagramTitle?.(t.title)}}(0,a.K2)(n,"populateCommonDb")},13249:(t,e,r)=>{r.d(e,{m:()=>n});var a=r(75905);var n=class{constructor(t){this.init=t;this.records=this.init()}static{(0,a.K2)(this,"ImperativeState")}reset(){this.records=this.init()}}},99890:(t,e,r)=>{r.d(e,{diagram:()=>Mt});var a=r(19163);var n=r(13249);var o=r(96049);var s=r(75905);var c=r(24010);var i=r(24982);var m={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4};var d=s.UI.gitGraph;var h=(0,s.K2)((()=>{const t=(0,o.$t)({...d,...(0,s.zj)().gitGraph});return t}),"getConfig");var l=new n.m((()=>{const t=h();const e=t.mainBranchName;const r=t.mainBranchOrder;return{mainBranchName:e,commits:new Map,head:null,branchConfig:new Map([[e,{name:e,order:r}]]),branches:new Map([[e,null]]),currBranch:e,direction:"LR",seq:0,options:{}}}));function g(){return(0,o.yT)({length:7})}(0,s.K2)(g,"getID");function p(t,e){const r=Object.create(null);return t.reduce(((t,a)=>{const n=e(a);if(!r[n]){r[n]=true;t.push(a)}return t}),[])}(0,s.K2)(p,"uniqBy");var f=(0,s.K2)((function(t){l.records.direction=t}),"setDirection");var $=(0,s.K2)((function(t){s.Rm.debug("options str",t);t=t?.trim();t=t||"{}";try{l.records.options=JSON.parse(t)}catch(e){s.Rm.error("error while parsing gitGraph options",e.message)}}),"setOptions");var y=(0,s.K2)((function(){return l.records.options}),"getOptions");var x=(0,s.K2)((function(t){let e=t.msg;let r=t.id;const a=t.type;let n=t.tags;s.Rm.info("commit",e,r,a,n);s.Rm.debug("Entering commit:",e,r,a,n);const o=h();r=s.Y2.sanitizeText(r,o);e=s.Y2.sanitizeText(e,o);n=n?.map((t=>s.Y2.sanitizeText(t,o)));const c={id:r?r:l.records.seq+"-"+g(),message:e,seq:l.records.seq++,type:a??m.NORMAL,tags:n??[],parents:l.records.head==null?[]:[l.records.head.id],branch:l.records.currBranch};l.records.head=c;s.Rm.info("main branch",o.mainBranchName);l.records.commits.set(c.id,c);l.records.branches.set(l.records.currBranch,c.id);s.Rm.debug("in pushCommit "+c.id)}),"commit");var u=(0,s.K2)((function(t){let e=t.name;const r=t.order;e=s.Y2.sanitizeText(e,h());if(l.records.branches.has(e)){throw new Error(`Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout ${e}")`)}l.records.branches.set(e,l.records.head!=null?l.records.head.id:null);l.records.branchConfig.set(e,{name:e,order:r});v(e);s.Rm.debug("in createBranch")}),"branch");var b=(0,s.K2)((t=>{let e=t.branch;let r=t.id;const a=t.type;const n=t.tags;const o=h();e=s.Y2.sanitizeText(e,o);if(r){r=s.Y2.sanitizeText(r,o)}const c=l.records.branches.get(l.records.currBranch);const i=l.records.branches.get(e);const d=c?l.records.commits.get(c):void 0;const p=i?l.records.commits.get(i):void 0;if(d&&p&&d.branch===e){throw new Error(`Cannot merge branch '${e}' into itself.`)}if(l.records.currBranch===e){const t=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');t.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]};throw t}if(d===void 0||!d){const t=new Error(`Incorrect usage of "merge". Current branch (${l.records.currBranch})has no commits`);t.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["commit"]};throw t}if(!l.records.branches.has(e)){const t=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") does not exist");t.hash={text:`merge ${e}`,token:`merge ${e}`,expected:[`branch ${e}`]};throw t}if(p===void 0||!p){const t=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") has no commits");t.hash={text:`merge ${e}`,token:`merge ${e}`,expected:['"commit"']};throw t}if(d===p){const t=new Error('Incorrect usage of "merge". Both branches have same head');t.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]};throw t}if(r&&l.records.commits.has(r)){const t=new Error('Incorrect usage of "merge". Commit with id:'+r+" already exists, use different custom Id");t.hash={text:`merge ${e} ${r} ${a} ${n?.join(" ")}`,token:`merge ${e} ${r} ${a} ${n?.join(" ")}`,expected:[`merge ${e} ${r}_UNIQUE ${a} ${n?.join(" ")}`]};throw t}const f=i?i:"";const $={id:r||`${l.records.seq}-${g()}`,message:`merged branch ${e} into ${l.records.currBranch}`,seq:l.records.seq++,parents:l.records.head==null?[]:[l.records.head.id,f],branch:l.records.currBranch,type:m.MERGE,customType:a,customId:r?true:false,tags:n??[]};l.records.head=$;l.records.commits.set($.id,$);l.records.branches.set(l.records.currBranch,$.id);s.Rm.debug(l.records.branches);s.Rm.debug("in mergeBranch")}),"merge");var w=(0,s.K2)((function(t){let e=t.id;let r=t.targetId;let a=t.tags;let n=t.parent;s.Rm.debug("Entering cherryPick:",e,r,a);const o=h();e=s.Y2.sanitizeText(e,o);r=s.Y2.sanitizeText(r,o);a=a?.map((t=>s.Y2.sanitizeText(t,o)));n=s.Y2.sanitizeText(n,o);if(!e||!l.records.commits.has(e)){const t=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');t.hash={text:`cherryPick ${e} ${r}`,token:`cherryPick ${e} ${r}`,expected:["cherry-pick abc"]};throw t}const c=l.records.commits.get(e);if(c===void 0||!c){throw new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided')}if(n&&!(Array.isArray(c.parents)&&c.parents.includes(n))){const t=new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");throw t}const i=c.branch;if(c.type===m.MERGE&&!n){const t=new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");throw t}if(!r||!l.records.commits.has(r)){if(i===l.records.currBranch){const t=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');t.hash={text:`cherryPick ${e} ${r}`,token:`cherryPick ${e} ${r}`,expected:["cherry-pick abc"]};throw t}const t=l.records.branches.get(l.records.currBranch);if(t===void 0||!t){const t=new Error(`Incorrect usage of "cherry-pick". Current branch (${l.records.currBranch})has no commits`);t.hash={text:`cherryPick ${e} ${r}`,token:`cherryPick ${e} ${r}`,expected:["cherry-pick abc"]};throw t}const o=l.records.commits.get(t);if(o===void 0||!o){const t=new Error(`Incorrect usage of "cherry-pick". Current branch (${l.records.currBranch})has no commits`);t.hash={text:`cherryPick ${e} ${r}`,token:`cherryPick ${e} ${r}`,expected:["cherry-pick abc"]};throw t}const d={id:l.records.seq+"-"+g(),message:`cherry-picked ${c?.message} into ${l.records.currBranch}`,seq:l.records.seq++,parents:l.records.head==null?[]:[l.records.head.id,c.id],branch:l.records.currBranch,type:m.CHERRY_PICK,tags:a?a.filter(Boolean):[`cherry-pick:${c.id}${c.type===m.MERGE?`|parent:${n}`:""}`]};l.records.head=d;l.records.commits.set(d.id,d);l.records.branches.set(l.records.currBranch,d.id);s.Rm.debug(l.records.branches);s.Rm.debug("in cherryPick")}}),"cherryPick");var v=(0,s.K2)((function(t){t=s.Y2.sanitizeText(t,h());if(!l.records.branches.has(t)){const e=new Error(`Trying to checkout branch which is not yet created. (Help try using "branch ${t}")`);e.hash={text:`checkout ${t}`,token:`checkout ${t}`,expected:[`branch ${t}`]};throw e}else{l.records.currBranch=t;const e=l.records.branches.get(l.records.currBranch);if(e===void 0||!e){l.records.head=null}else{l.records.head=l.records.commits.get(e)??null}}}),"checkout");function B(t,e,r){const a=t.indexOf(e);if(a===-1){t.push(r)}else{t.splice(a,1,r)}}(0,s.K2)(B,"upsert");function E(t){const e=t.reduce(((t,e)=>{if(t.seq>e.seq){return t}return e}),t[0]);let r="";t.forEach((function(t){if(t===e){r+="\t*"}else{r+="\t|"}}));const a=[r,e.id,e.seq];for(const n in l.records.branches){if(l.records.branches.get(n)===e.id){a.push(n)}}s.Rm.debug(a.join(" "));if(e.parents&&e.parents.length==2&&e.parents[0]&&e.parents[1]){const r=l.records.commits.get(e.parents[0]);B(t,e,r);if(e.parents[1]){t.push(l.records.commits.get(e.parents[1]))}}else if(e.parents.length==0){return}else{if(e.parents[0]){const r=l.records.commits.get(e.parents[0]);B(t,e,r)}}t=p(t,(t=>t.id));E(t)}(0,s.K2)(E,"prettyPrintCommitHistory");var C=(0,s.K2)((function(){s.Rm.debug(l.records.commits);const t=R()[0];E([t])}),"prettyPrint");var k=(0,s.K2)((function(){l.reset();(0,s.IU)()}),"clear");var L=(0,s.K2)((function(){const t=[...l.records.branchConfig.values()].map(((t,e)=>{if(t.order!==null&&t.order!==void 0){return t}return{...t,order:parseFloat(`0.${e}`)}})).sort(((t,e)=>(t.order??0)-(e.order??0))).map((({name:t})=>({name:t})));return t}),"getBranchesAsObjArray");var T=(0,s.K2)((function(){return l.records.branches}),"getBranches");var M=(0,s.K2)((function(){return l.records.commits}),"getCommits");var R=(0,s.K2)((function(){const t=[...l.records.commits.values()];t.forEach((function(t){s.Rm.debug(t.id)}));t.sort(((t,e)=>t.seq-e.seq));return t}),"getCommitsArray");var K=(0,s.K2)((function(){return l.records.currBranch}),"getCurrentBranch");var O=(0,s.K2)((function(){return l.records.direction}),"getDirection");var P=(0,s.K2)((function(){return l.records.head}),"getHead");var A={commitType:m,getConfig:h,setDirection:f,setOptions:$,getOptions:y,commit:x,branch:u,merge:b,cherryPick:w,checkout:v,prettyPrint:C,clear:k,getBranchesAsObjArray:L,getBranches:T,getCommits:M,getCommitsArray:R,getCurrentBranch:K,getDirection:O,getHead:P,setAccTitle:s.SV,getAccTitle:s.iN,getAccDescription:s.m7,setAccDescription:s.EI,setDiagramTitle:s.ke,getDiagramTitle:s.ab};var q=(0,s.K2)(((t,e)=>{(0,a.S)(t,e);if(t.dir){e.setDirection(t.dir)}for(const r of t.statements){I(r,e)}}),"populate");var I=(0,s.K2)(((t,e)=>{const r={Commit:(0,s.K2)((t=>e.commit(G(t))),"Commit"),Branch:(0,s.K2)((t=>e.branch(W(t))),"Branch"),Merge:(0,s.K2)((t=>e.merge(H(t))),"Merge"),Checkout:(0,s.K2)((t=>e.checkout(D(t))),"Checkout"),CherryPicking:(0,s.K2)((t=>e.cherryPick(N(t))),"CherryPicking")};const a=r[t.$type];if(a){a(t)}else{s.Rm.error(`Unknown statement type: ${t.$type}`)}}),"parseStatement");var G=(0,s.K2)((t=>{const e={id:t.id,msg:t.message??"",type:t.type!==void 0?m[t.type]:m.NORMAL,tags:t.tags??void 0};return e}),"parseCommit");var W=(0,s.K2)((t=>{const e={name:t.name,order:t.order??0};return e}),"parseBranch");var H=(0,s.K2)((t=>{const e={branch:t.branch,id:t.id??"",type:t.type!==void 0?m[t.type]:void 0,tags:t.tags??void 0};return e}),"parseMerge");var D=(0,s.K2)((t=>{const e=t.branch;return e}),"parseCheckout");var N=(0,s.K2)((t=>{const e={id:t.id,targetId:"",tags:t.tags?.length===0?void 0:t.tags,parent:t.parent};return e}),"parseCherryPicking");var _={parse:(0,s.K2)((async t=>{const e=await(0,c.qg)("gitGraph",t);s.Rm.debug(e);q(e,A)}),"parse")};if(void 0){const{it:t,expect:e,describe:r}=void 0;const a={commitType:m,setDirection:vi.fn(),commit:vi.fn(),branch:vi.fn(),merge:vi.fn(),cherryPick:vi.fn(),checkout:vi.fn()};r("GitGraph Parser",(()=>{t("should parse a commit statement",(()=>{const t={$type:"Commit",id:"1",message:"test",tags:["tag1","tag2"],type:"NORMAL"};I(t,a);e(a.commit).toHaveBeenCalledWith({id:"1",msg:"test",tags:["tag1","tag2"],type:0})}));t("should parse a branch statement",(()=>{const t={$type:"Branch",name:"newBranch",order:1};I(t,a);e(a.branch).toHaveBeenCalledWith({name:"newBranch",order:1})}));t("should parse a checkout statement",(()=>{const t={$type:"Checkout",branch:"newBranch"};I(t,a);e(a.checkout).toHaveBeenCalledWith("newBranch")}));t("should parse a merge statement",(()=>{const t={$type:"Merge",branch:"newBranch",id:"1",tags:["tag1","tag2"],type:"NORMAL"};I(t,a);e(a.merge).toHaveBeenCalledWith({branch:"newBranch",id:"1",tags:["tag1","tag2"],type:0})}));t("should parse a cherry picking statement",(()=>{const t={$type:"CherryPicking",id:"1",tags:["tag1","tag2"],parent:"2"};I(t,a);e(a.cherryPick).toHaveBeenCalledWith({id:"1",targetId:"",parent:"2",tags:["tag1","tag2"]})}));t("should parse a langium generated gitGraph ast",(()=>{const t={$type:"GitGraph",statements:[]};const r={$type:"GitGraph",statements:[{$container:t,$type:"Commit",id:"1",message:"test",tags:["tag1","tag2"],type:"NORMAL"},{$container:t,$type:"Branch",name:"newBranch",order:1},{$container:t,$type:"Merge",branch:"newBranch",id:"1",tags:["tag1","tag2"],type:"NORMAL"},{$container:t,$type:"Checkout",branch:"newBranch"},{$container:t,$type:"CherryPicking",id:"1",tags:["tag1","tag2"],parent:"2"}]};q(r,a);e(a.commit).toHaveBeenCalledWith({id:"1",msg:"test",tags:["tag1","tag2"],type:0});e(a.branch).toHaveBeenCalledWith({name:"newBranch",order:1});e(a.merge).toHaveBeenCalledWith({branch:"newBranch",id:"1",tags:["tag1","tag2"],type:0});e(a.checkout).toHaveBeenCalledWith("newBranch")}))}))}var S=(0,s.D7)();var z=S?.gitGraph;var Y=10;var j=40;var Z=4;var F=2;var U=8;var V=new Map;var J=new Map;var Q=30;var X=new Map;var tt=[];var et=0;var rt="LR";var at=(0,s.K2)((()=>{V.clear();J.clear();X.clear();et=0;tt=[];rt="LR"}),"clear");var nt=(0,s.K2)((t=>{const e=document.createElementNS("http://www.w3.org/2000/svg","text");const r=typeof t==="string"?t.split(/\\n|\n|<br\s*\/?>/gi):t;r.forEach((t=>{const r=document.createElementNS("http://www.w3.org/2000/svg","tspan");r.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve");r.setAttribute("dy","1em");r.setAttribute("x","0");r.setAttribute("class","row");r.textContent=t.trim();e.appendChild(r)}));return e}),"drawText");var ot=(0,s.K2)((t=>{let e;let r;let a;if(rt==="BT"){r=(0,s.K2)(((t,e)=>t<=e),"comparisonFunc");a=Infinity}else{r=(0,s.K2)(((t,e)=>t>=e),"comparisonFunc");a=0}t.forEach((t=>{const n=rt==="TB"||rt=="BT"?J.get(t)?.y:J.get(t)?.x;if(n!==void 0&&r(n,a)){e=t;a=n}}));return e}),"findClosestParent");var st=(0,s.K2)((t=>{let e="";let r=Infinity;t.forEach((t=>{const a=J.get(t).y;if(a<=r){e=t;r=a}}));return e||void 0}),"findClosestParentBT");var ct=(0,s.K2)(((t,e,r)=>{let a=r;let n=r;const o=[];t.forEach((t=>{const r=e.get(t);if(!r){throw new Error(`Commit not found for key ${t}`)}if(r.parents.length){a=mt(r);n=Math.max(a,n)}else{o.push(r)}dt(r,a)}));a=n;o.forEach((t=>{ht(t,a,r)}));t.forEach((t=>{const r=e.get(t);if(r?.parents.length){const t=st(r.parents);a=J.get(t).y-j;if(a<=n){n=a}const e=V.get(r.branch).pos;const o=a-Y;J.set(r.id,{x:e,y:o})}}))}),"setParallelBTPos");var it=(0,s.K2)((t=>{const e=ot(t.parents.filter((t=>t!==null)));if(!e){throw new Error(`Closest parent not found for commit ${t.id}`)}const r=J.get(e)?.y;if(r===void 0){throw new Error(`Closest parent position not found for commit ${t.id}`)}return r}),"findClosestParentPos");var mt=(0,s.K2)((t=>{const e=it(t);return e+j}),"calculateCommitPosition");var dt=(0,s.K2)(((t,e)=>{const r=V.get(t.branch);if(!r){throw new Error(`Branch not found for commit ${t.id}`)}const a=r.pos;const n=e+Y;J.set(t.id,{x:a,y:n});return{x:a,y:n}}),"setCommitPosition");var ht=(0,s.K2)(((t,e,r)=>{const a=V.get(t.branch);if(!a){throw new Error(`Branch not found for commit ${t.id}`)}const n=e+r;const o=a.pos;J.set(t.id,{x:o,y:n})}),"setRootPosition");var lt=(0,s.K2)(((t,e,r,a,n,o)=>{if(o===m.HIGHLIGHT){t.append("rect").attr("x",r.x-10).attr("y",r.y-10).attr("width",20).attr("height",20).attr("class",`commit ${e.id} commit-highlight${n%U} ${a}-outer`);t.append("rect").attr("x",r.x-6).attr("y",r.y-6).attr("width",12).attr("height",12).attr("class",`commit ${e.id} commit${n%U} ${a}-inner`)}else if(o===m.CHERRY_PICK){t.append("circle").attr("cx",r.x).attr("cy",r.y).attr("r",10).attr("class",`commit ${e.id} ${a}`);t.append("circle").attr("cx",r.x-3).attr("cy",r.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${a}`);t.append("circle").attr("cx",r.x+3).attr("cy",r.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${a}`);t.append("line").attr("x1",r.x+3).attr("y1",r.y+1).attr("x2",r.x).attr("y2",r.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${a}`);t.append("line").attr("x1",r.x-3).attr("y1",r.y+1).attr("x2",r.x).attr("y2",r.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${a}`)}else{const s=t.append("circle");s.attr("cx",r.x);s.attr("cy",r.y);s.attr("r",e.type===m.MERGE?9:10);s.attr("class",`commit ${e.id} commit${n%U}`);if(o===m.MERGE){const o=t.append("circle");o.attr("cx",r.x);o.attr("cy",r.y);o.attr("r",6);o.attr("class",`commit ${a} ${e.id} commit${n%U}`)}if(o===m.REVERSE){const o=t.append("path");o.attr("d",`M ${r.x-5},${r.y-5}L${r.x+5},${r.y+5}M${r.x-5},${r.y+5}L${r.x+5},${r.y-5}`).attr("class",`commit ${a} ${e.id} commit${n%U}`)}}}),"drawCommitBullet");var gt=(0,s.K2)(((t,e,r,a)=>{if(e.type!==m.CHERRY_PICK&&(e.customId&&e.type===m.MERGE||e.type!==m.MERGE)&&z?.showCommitLabel){const n=t.append("g");const o=n.insert("rect").attr("class","commit-label-bkg");const s=n.append("text").attr("x",a).attr("y",r.y+25).attr("class","commit-label").text(e.id);const c=s.node()?.getBBox();if(c){o.attr("x",r.posWithOffset-c.width/2-F).attr("y",r.y+13.5).attr("width",c.width+2*F).attr("height",c.height+2*F);if(rt==="TB"||rt==="BT"){o.attr("x",r.x-(c.width+4*Z+5)).attr("y",r.y-12);s.attr("x",r.x-(c.width+4*Z)).attr("y",r.y+c.height-12)}else{s.attr("x",r.posWithOffset-c.width/2)}if(z.rotateCommitLabel){if(rt==="TB"||rt==="BT"){s.attr("transform","rotate(-45, "+r.x+", "+r.y+")");o.attr("transform","rotate(-45, "+r.x+", "+r.y+")")}else{const t=-7.5-(c.width+10)/25*9.5;const e=10+c.width/25*8.5;n.attr("transform","translate("+t+", "+e+") rotate(-45, "+a+", "+r.y+")")}}}}}),"drawCommitLabel");var pt=(0,s.K2)(((t,e,r,a)=>{if(e.tags.length>0){let n=0;let o=0;let s=0;const c=[];for(const a of e.tags.reverse()){const e=t.insert("polygon");const i=t.append("circle");const m=t.append("text").attr("y",r.y-16-n).attr("class","tag-label").text(a);const d=m.node()?.getBBox();if(!d){throw new Error("Tag bbox not found")}o=Math.max(o,d.width);s=Math.max(s,d.height);m.attr("x",r.posWithOffset-d.width/2);c.push({tag:m,hole:i,rect:e,yOffset:n});n+=20}for(const{tag:t,hole:e,rect:i,yOffset:m}of c){const n=s/2;const c=r.y-19.2-m;i.attr("class","tag-label-bkg").attr("points",`\n      ${a-o/2-Z/2},${c+F}  \n      ${a-o/2-Z/2},${c-F}\n      ${r.posWithOffset-o/2-Z},${c-n-F}\n      ${r.posWithOffset+o/2+Z},${c-n-F}\n      ${r.posWithOffset+o/2+Z},${c+n+F}\n      ${r.posWithOffset-o/2-Z},${c+n+F}`);e.attr("cy",c).attr("cx",a-o/2+Z/2).attr("r",1.5).attr("class","tag-hole");if(rt==="TB"||rt==="BT"){const s=a+m;i.attr("class","tag-label-bkg").attr("points",`\n        ${r.x},${s+2}\n        ${r.x},${s-2}\n        ${r.x+Y},${s-n-2}\n        ${r.x+Y+o+4},${s-n-2}\n        ${r.x+Y+o+4},${s+n+2}\n        ${r.x+Y},${s+n+2}`).attr("transform","translate(12,12) rotate(45, "+r.x+","+a+")");e.attr("cx",r.x+Z/2).attr("cy",s).attr("transform","translate(12,12) rotate(45, "+r.x+","+a+")");t.attr("x",r.x+5).attr("y",s+3).attr("transform","translate(14,14) rotate(45, "+r.x+","+a+")")}}}}),"drawCommitTags");var ft=(0,s.K2)((t=>{const e=t.customType??t.type;switch(e){case m.NORMAL:return"commit-normal";case m.REVERSE:return"commit-reverse";case m.HIGHLIGHT:return"commit-highlight";case m.MERGE:return"commit-merge";case m.CHERRY_PICK:return"commit-cherry-pick";default:return"commit-normal"}}),"getCommitClassType");var $t=(0,s.K2)(((t,e,r,a)=>{const n={x:0,y:0};if(t.parents.length>0){const r=ot(t.parents);if(r){const o=a.get(r)??n;if(e==="TB"){return o.y+j}else if(e==="BT"){const e=a.get(t.id)??n;return e.y-j}else{return o.x+j}}}else{if(e==="TB"){return Q}else if(e==="BT"){const e=a.get(t.id)??n;return e.y-j}else{return 0}}return 0}),"calculatePosition");var yt=(0,s.K2)(((t,e,r)=>{const a=rt==="BT"&&r?e:e+Y;const n=rt==="TB"||rt==="BT"?a:V.get(t.branch)?.pos;const o=rt==="TB"||rt==="BT"?V.get(t.branch)?.pos:a;if(o===void 0||n===void 0){throw new Error(`Position were undefined for commit ${t.id}`)}return{x:o,y:n,posWithOffset:a}}),"getCommitPosition");var xt=(0,s.K2)(((t,e,r)=>{if(!z){throw new Error("GitGraph config not found")}const a=t.append("g").attr("class","commit-bullets");const n=t.append("g").attr("class","commit-labels");let o=rt==="TB"||rt==="BT"?Q:0;const c=[...e.keys()];const i=z?.parallelCommits??false;const m=(0,s.K2)(((t,r)=>{const a=e.get(t)?.seq;const n=e.get(r)?.seq;return a!==void 0&&n!==void 0?a-n:0}),"sortKeys");let d=c.sort(m);if(rt==="BT"){if(i){ct(d,e,o)}d=d.reverse()}d.forEach((t=>{const s=e.get(t);if(!s){throw new Error(`Commit not found for key ${t}`)}if(i){o=$t(s,rt,o,J)}const c=yt(s,o,i);if(r){const t=ft(s);const e=s.customType??s.type;const r=V.get(s.branch)?.index??0;lt(a,s,c,t,r,e);gt(n,s,c,o);pt(n,s,c,o)}if(rt==="TB"||rt==="BT"){J.set(s.id,{x:c.x,y:c.posWithOffset})}else{J.set(s.id,{x:c.posWithOffset,y:c.y})}o=rt==="BT"&&i?o+j:o+j+Y;if(o>et){et=o}}))}),"drawCommits");var ut=(0,s.K2)(((t,e,r,a,n)=>{const o=rt==="TB"||rt==="BT"?r.x<a.x:r.y<a.y;const c=o?e.branch:t.branch;const i=(0,s.K2)((t=>t.branch===c),"isOnBranchToGetCurve");const m=(0,s.K2)((r=>r.seq>t.seq&&r.seq<e.seq),"isBetweenCommits");return[...n.values()].some((t=>m(t)&&i(t)))}),"shouldRerouteArrow");var bt=(0,s.K2)(((t,e,r=0)=>{const a=t+Math.abs(t-e)/2;if(r>5){return a}const n=tt.every((t=>Math.abs(t-a)>=10));if(n){tt.push(a);return a}const o=Math.abs(t-e);return bt(t,e-o/5,r+1)}),"findLane");var wt=(0,s.K2)(((t,e,r,a)=>{const n=J.get(e.id);const o=J.get(r.id);if(n===void 0||o===void 0){throw new Error(`Commit positions not found for commits ${e.id} and ${r.id}`)}const s=ut(e,r,n,o,a);let c="";let i="";let d=0;let h=0;let l=V.get(r.branch)?.index;if(r.type===m.MERGE&&e.id!==r.parents[0]){l=V.get(e.branch)?.index}let g;if(s){c="A 10 10, 0, 0, 0,";i="A 10 10, 0, 0, 1,";d=10;h=10;const t=n.y<o.y?bt(n.y,o.y):bt(o.y,n.y);const r=n.x<o.x?bt(n.x,o.x):bt(o.x,n.x);if(rt==="TB"){if(n.x<o.x){g=`M ${n.x} ${n.y} L ${r-d} ${n.y} ${i} ${r} ${n.y+h} L ${r} ${o.y-d} ${c} ${r+h} ${o.y} L ${o.x} ${o.y}`}else{l=V.get(e.branch)?.index;g=`M ${n.x} ${n.y} L ${r+d} ${n.y} ${c} ${r} ${n.y+h} L ${r} ${o.y-d} ${i} ${r-h} ${o.y} L ${o.x} ${o.y}`}}else if(rt==="BT"){if(n.x<o.x){g=`M ${n.x} ${n.y} L ${r-d} ${n.y} ${c} ${r} ${n.y-h} L ${r} ${o.y+d} ${i} ${r+h} ${o.y} L ${o.x} ${o.y}`}else{l=V.get(e.branch)?.index;g=`M ${n.x} ${n.y} L ${r+d} ${n.y} ${i} ${r} ${n.y-h} L ${r} ${o.y+d} ${c} ${r-h} ${o.y} L ${o.x} ${o.y}`}}else{if(n.y<o.y){g=`M ${n.x} ${n.y} L ${n.x} ${t-d} ${c} ${n.x+h} ${t} L ${o.x-d} ${t} ${i} ${o.x} ${t+h} L ${o.x} ${o.y}`}else{l=V.get(e.branch)?.index;g=`M ${n.x} ${n.y} L ${n.x} ${t+d} ${i} ${n.x+h} ${t} L ${o.x-d} ${t} ${c} ${o.x} ${t-h} L ${o.x} ${o.y}`}}}else{c="A 20 20, 0, 0, 0,";i="A 20 20, 0, 0, 1,";d=20;h=20;if(rt==="TB"){if(n.x<o.x){if(r.type===m.MERGE&&e.id!==r.parents[0]){g=`M ${n.x} ${n.y} L ${n.x} ${o.y-d} ${c} ${n.x+h} ${o.y} L ${o.x} ${o.y}`}else{g=`M ${n.x} ${n.y} L ${o.x-d} ${n.y} ${i} ${o.x} ${n.y+h} L ${o.x} ${o.y}`}}if(n.x>o.x){c="A 20 20, 0, 0, 0,";i="A 20 20, 0, 0, 1,";d=20;h=20;if(r.type===m.MERGE&&e.id!==r.parents[0]){g=`M ${n.x} ${n.y} L ${n.x} ${o.y-d} ${i} ${n.x-h} ${o.y} L ${o.x} ${o.y}`}else{g=`M ${n.x} ${n.y} L ${o.x+d} ${n.y} ${c} ${o.x} ${n.y+h} L ${o.x} ${o.y}`}}if(n.x===o.x){g=`M ${n.x} ${n.y} L ${o.x} ${o.y}`}}else if(rt==="BT"){if(n.x<o.x){if(r.type===m.MERGE&&e.id!==r.parents[0]){g=`M ${n.x} ${n.y} L ${n.x} ${o.y+d} ${i} ${n.x+h} ${o.y} L ${o.x} ${o.y}`}else{g=`M ${n.x} ${n.y} L ${o.x-d} ${n.y} ${c} ${o.x} ${n.y-h} L ${o.x} ${o.y}`}}if(n.x>o.x){c="A 20 20, 0, 0, 0,";i="A 20 20, 0, 0, 1,";d=20;h=20;if(r.type===m.MERGE&&e.id!==r.parents[0]){g=`M ${n.x} ${n.y} L ${n.x} ${o.y+d} ${c} ${n.x-h} ${o.y} L ${o.x} ${o.y}`}else{g=`M ${n.x} ${n.y} L ${o.x-d} ${n.y} ${c} ${o.x} ${n.y-h} L ${o.x} ${o.y}`}}if(n.x===o.x){g=`M ${n.x} ${n.y} L ${o.x} ${o.y}`}}else{if(n.y<o.y){if(r.type===m.MERGE&&e.id!==r.parents[0]){g=`M ${n.x} ${n.y} L ${o.x-d} ${n.y} ${i} ${o.x} ${n.y+h} L ${o.x} ${o.y}`}else{g=`M ${n.x} ${n.y} L ${n.x} ${o.y-d} ${c} ${n.x+h} ${o.y} L ${o.x} ${o.y}`}}if(n.y>o.y){if(r.type===m.MERGE&&e.id!==r.parents[0]){g=`M ${n.x} ${n.y} L ${o.x-d} ${n.y} ${c} ${o.x} ${n.y-h} L ${o.x} ${o.y}`}else{g=`M ${n.x} ${n.y} L ${n.x} ${o.y+d} ${i} ${n.x+h} ${o.y} L ${o.x} ${o.y}`}}if(n.y===o.y){g=`M ${n.x} ${n.y} L ${o.x} ${o.y}`}}}if(g===void 0){throw new Error("Line definition not found")}t.append("path").attr("d",g).attr("class","arrow arrow"+l%U)}),"drawArrow");var vt=(0,s.K2)(((t,e)=>{const r=t.append("g").attr("class","commit-arrows");[...e.keys()].forEach((t=>{const a=e.get(t);if(a.parents&&a.parents.length>0){a.parents.forEach((t=>{wt(r,e.get(t),a,e)}))}}))}),"drawArrows");var Bt=(0,s.K2)(((t,e)=>{const r=t.append("g");e.forEach(((t,e)=>{const a=e%U;const n=V.get(t.name)?.pos;if(n===void 0){throw new Error(`Position not found for branch ${t.name}`)}const o=r.append("line");o.attr("x1",0);o.attr("y1",n);o.attr("x2",et);o.attr("y2",n);o.attr("class","branch branch"+a);if(rt==="TB"){o.attr("y1",Q);o.attr("x1",n);o.attr("y2",et);o.attr("x2",n)}else if(rt==="BT"){o.attr("y1",et);o.attr("x1",n);o.attr("y2",Q);o.attr("x2",n)}tt.push(n);const s=t.name;const c=nt(s);const i=r.insert("rect");const m=r.insert("g").attr("class","branchLabel");const d=m.insert("g").attr("class","label branch-label"+a);d.node().appendChild(c);const h=c.getBBox();i.attr("class","branchLabelBkg label"+a).attr("rx",4).attr("ry",4).attr("x",-h.width-4-(z?.rotateCommitLabel===true?30:0)).attr("y",-h.height/2+8).attr("width",h.width+18).attr("height",h.height+4);d.attr("transform","translate("+(-h.width-14-(z?.rotateCommitLabel===true?30:0))+", "+(n-h.height/2-1)+")");if(rt==="TB"){i.attr("x",n-h.width/2-10).attr("y",0);d.attr("transform","translate("+(n-h.width/2-5)+", 0)")}else if(rt==="BT"){i.attr("x",n-h.width/2-10).attr("y",et);d.attr("transform","translate("+(n-h.width/2-5)+", "+et+")")}else{i.attr("transform","translate(-19, "+(n-h.height/2)+")")}}))}),"drawBranches");var Et=(0,s.K2)((function(t,e,r,a,n){V.set(t,{pos:e,index:r});e+=50+(n?40:0)+(rt==="TB"||rt==="BT"?a.width/2:0);return e}),"setBranchPosition");var Ct=(0,s.K2)((function(t,e,r,a){at();s.Rm.debug("in gitgraph renderer",t+"\n","id:",e,r);if(!z){throw new Error("GitGraph config not found")}const n=z.rotateCommitLabel??false;const c=a.db;X=c.getCommits();const m=c.getBranchesAsObjArray();rt=c.getDirection();const d=(0,i.Ltv)(`[id="${e}"]`);let h=0;m.forEach(((t,e)=>{const r=nt(t.name);const a=d.append("g");const o=a.insert("g").attr("class","branchLabel");const s=o.insert("g").attr("class","label branch-label");s.node()?.appendChild(r);const c=r.getBBox();h=Et(t.name,h,e,c,n);s.remove();o.remove();a.remove()}));xt(d,X,false);if(z.showBranches){Bt(d,m)}vt(d,X);xt(d,X,true);o._K.insertTitle(d,"gitTitleText",z.titleTopMargin??0,c.getDiagramTitle());(0,s.mj)(void 0,d,z.diagramPadding,z.useMaxWidth)}),"draw");var kt={draw:Ct};if(void 0){const{it:t,expect:e,describe:r}=void 0;r("drawText",(()=>{t("should drawText",(()=>{const t=nt("main");e(t).toBeDefined();e(t.children[0].innerHTML).toBe("main")}))}));r("branchPosition",(()=>{const r={x:0,y:0,width:10,height:10,top:0,right:0,bottom:0,left:0,toJSON:(0,s.K2)((()=>""),"toJSON")};t("should setBranchPositions LR with two branches",(()=>{rt="LR";const t=Et("main",0,0,r,true);e(t).toBe(90);e(V.get("main")).toEqual({pos:0,index:0});const a=Et("develop",t,1,r,true);e(a).toBe(180);e(V.get("develop")).toEqual({pos:t,index:1})}));t("should setBranchPositions TB with two branches",(()=>{rt="TB";r.width=34.9921875;const t=Et("main",0,0,r,true);e(t).toBe(107.49609375);e(V.get("main")).toEqual({pos:0,index:0});r.width=56.421875;const a=Et("develop",t,1,r,true);e(a).toBe(225.70703125);e(V.get("develop")).toEqual({pos:t,index:1})}))}));r("commitPosition",(()=>{const a=new Map([["commitZero",{id:"ZERO",message:"",seq:0,type:m.NORMAL,tags:[],parents:[],branch:"main"}],["commitA",{id:"A",message:"",seq:1,type:m.NORMAL,tags:[],parents:["ZERO"],branch:"feature"}],["commitB",{id:"B",message:"",seq:2,type:m.NORMAL,tags:[],parents:["A"],branch:"feature"}],["commitM",{id:"M",message:"merged branch feature into main",seq:3,type:m.MERGE,tags:[],parents:["ZERO","B"],branch:"main",customId:true}],["commitC",{id:"C",message:"",seq:4,type:m.NORMAL,tags:[],parents:["ZERO"],branch:"release"}],["commit5_8928ea0",{id:"5-8928ea0",message:"cherry-picked [object Object] into release",seq:5,type:m.CHERRY_PICK,tags:[],parents:["C","M"],branch:"release"}],["commitD",{id:"D",message:"",seq:6,type:m.NORMAL,tags:[],parents:["5-8928ea0"],branch:"release"}],["commit7_ed848ba",{id:"7-ed848ba",message:"cherry-picked [object Object] into release",seq:7,type:m.CHERRY_PICK,tags:[],parents:["D","M"],branch:"release"}]]);let n=0;V.set("main",{pos:0,index:0});V.set("feature",{pos:107.49609375,index:1});V.set("release",{pos:224.03515625,index:2});r("TB",(()=>{n=30;rt="TB";const r=new Map([["commitZero",{x:0,y:40,posWithOffset:40}],["commitA",{x:107.49609375,y:90,posWithOffset:90}],["commitB",{x:107.49609375,y:140,posWithOffset:140}],["commitM",{x:0,y:190,posWithOffset:190}],["commitC",{x:224.03515625,y:240,posWithOffset:240}],["commit5_8928ea0",{x:224.03515625,y:290,posWithOffset:290}],["commitD",{x:224.03515625,y:340,posWithOffset:340}],["commit7_ed848ba",{x:224.03515625,y:390,posWithOffset:390}]]);a.forEach(((a,o)=>{t(`should give the correct position for commit ${o}`,(()=>{const t=yt(a,n,false);e(t).toEqual(r.get(o));n+=50}))}))}));r("LR",(()=>{let r=30;rt="LR";const n=new Map([["commitZero",{x:0,y:40,posWithOffset:40}],["commitA",{x:107.49609375,y:90,posWithOffset:90}],["commitB",{x:107.49609375,y:140,posWithOffset:140}],["commitM",{x:0,y:190,posWithOffset:190}],["commitC",{x:224.03515625,y:240,posWithOffset:240}],["commit5_8928ea0",{x:224.03515625,y:290,posWithOffset:290}],["commitD",{x:224.03515625,y:340,posWithOffset:340}],["commit7_ed848ba",{x:224.03515625,y:390,posWithOffset:390}]]);a.forEach(((a,o)=>{t(`should give the correct position for commit ${o}`,(()=>{const t=yt(a,r,false);e(t).toEqual(n.get(o));r+=50}))}))}));r("getCommitClassType",(()=>{const r=new Map([["commitZero","commit-normal"],["commitA","commit-normal"],["commitB","commit-normal"],["commitM","commit-merge"],["commitC","commit-normal"],["commit5_8928ea0","commit-cherry-pick"],["commitD","commit-normal"],["commit7_ed848ba","commit-cherry-pick"]]);a.forEach(((a,n)=>{t(`should give the correct class type for commit ${n}`,(()=>{const t=ft(a);e(t).toBe(r.get(n))}))}))}))}));r("building BT parallel commit diagram",(()=>{const r=new Map([["1-abcdefg",{id:"1-abcdefg",message:"",seq:0,type:0,tags:[],parents:[],branch:"main"}],["2-abcdefg",{id:"2-abcdefg",message:"",seq:1,type:0,tags:[],parents:["1-abcdefg"],branch:"main"}],["3-abcdefg",{id:"3-abcdefg",message:"",seq:2,type:0,tags:[],parents:["2-abcdefg"],branch:"develop"}],["4-abcdefg",{id:"4-abcdefg",message:"",seq:3,type:0,tags:[],parents:["3-abcdefg"],branch:"develop"}],["5-abcdefg",{id:"5-abcdefg",message:"",seq:4,type:0,tags:[],parents:["2-abcdefg"],branch:"feature"}],["6-abcdefg",{id:"6-abcdefg",message:"",seq:5,type:0,tags:[],parents:["5-abcdefg"],branch:"feature"}],["7-abcdefg",{id:"7-abcdefg",message:"",seq:6,type:0,tags:[],parents:["2-abcdefg"],branch:"main"}],["8-abcdefg",{id:"8-abcdefg",message:"",seq:7,type:0,tags:[],parents:["7-abcdefg"],branch:"main"}]]);const a=new Map([["1-abcdefg",{x:0,y:40}],["2-abcdefg",{x:0,y:90}],["3-abcdefg",{x:107.49609375,y:140}],["4-abcdefg",{x:107.49609375,y:190}],["5-abcdefg",{x:225.70703125,y:140}],["6-abcdefg",{x:225.70703125,y:190}],["7-abcdefg",{x:0,y:140}],["8-abcdefg",{x:0,y:190}]]);const n=new Map([["1-abcdefg",{x:0,y:210}],["2-abcdefg",{x:0,y:160}],["3-abcdefg",{x:107.49609375,y:110}],["4-abcdefg",{x:107.49609375,y:60}],["5-abcdefg",{x:225.70703125,y:110}],["6-abcdefg",{x:225.70703125,y:60}],["7-abcdefg",{x:0,y:110}],["8-abcdefg",{x:0,y:60}]]);const o=new Map([["1-abcdefg",30],["2-abcdefg",80],["3-abcdefg",130],["4-abcdefg",180],["5-abcdefg",130],["6-abcdefg",180],["7-abcdefg",130],["8-abcdefg",180]]);const s=[...a.keys()];t("should get the correct commit position and current position",(()=>{rt="BT";let t=30;J.clear();V.clear();V.set("main",{pos:0,index:0});V.set("develop",{pos:107.49609375,index:1});V.set("feature",{pos:225.70703125,index:2});z.parallelCommits=true;r.forEach(((r,n)=>{if(r.parents.length>0){t=mt(r)}const s=dt(r,t);e(s).toEqual(a.get(n));e(t).toEqual(o.get(n))}))}));t("should get the correct commit position after parallel commits",(()=>{J.clear();V.clear();rt="BT";const t=30;J.clear();V.clear();V.set("main",{pos:0,index:0});V.set("develop",{pos:107.49609375,index:1});V.set("feature",{pos:225.70703125,index:2});ct(s,r,t);s.forEach((t=>{const r=J.get(t);e(r).toEqual(n.get(t))}))}))}));z.parallelCommits=false;t("add",(()=>{J.set("parent1",{x:1,y:1});J.set("parent2",{x:2,y:2});J.set("parent3",{x:3,y:3});rt="LR";const t=["parent1","parent2","parent3"];const r=ot(t);e(r).toBe("parent3");J.clear()}))}var Lt=(0,s.K2)((t=>`\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0,1,2,3,4,5,6,7].map((e=>`\n        .branch-label${e} { fill: ${t["gitBranchLabel"+e]}; }\n        .commit${e} { stroke: ${t["git"+e]}; fill: ${t["git"+e]}; }\n        .commit-highlight${e} { stroke: ${t["gitInv"+e]}; fill: ${t["gitInv"+e]}; }\n        .label${e}  { fill: ${t["git"+e]}; }\n        .arrow${e} { stroke: ${t["git"+e]}; }\n        `)).join("\n")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${t.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${t.commitLabelFontSize}; fill: ${t.commitLabelColor};}\n  .commit-label-bkg { font-size: ${t.commitLabelFontSize}; fill: ${t.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${t.tagLabelFontSize}; fill: ${t.tagLabelColor};}\n  .tag-label-bkg { fill: ${t.tagLabelBackground}; stroke: ${t.tagLabelBorder}; }\n  .tag-hole { fill: ${t.textColor}; }\n\n  .commit-merge {\n    stroke: ${t.primaryColor};\n    fill: ${t.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${t.primaryColor};\n    fill: ${t.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${t.primaryColor};\n    fill: ${t.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${t.textColor};\n  }\n`),"getStyles");var Tt=Lt;var Mt={parser:_,db:A,renderer:kt,styles:Tt}}}]);