/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ARITHTOAMDGPUCONVERSIONPASS
#define GEN_PASS_DECL_ARITHTOARMSMECONVERSIONPASS
#define GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
#define GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
#define GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#define GEN_PASS_DECL_CONVERTARITHTOEMITC
#define GEN_PASS_DECL_CONVERTARITHTOSPIRV
#define GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#define GEN_PASS_DECL_CONVERTARMSMETOLLVM
#define GEN_PASS_DECL_CONVERTARMSMETOSCF
#define GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
#define GEN_PA<PERSON>_DE<PERSON>_CONVERTBUFFERIZATIONTOMEMREF
#define GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
#define GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#define GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
#define GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#define GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
#define GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
#define GEN_PASS_DECL_CONVERTFUNCTOEMITC
#define GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
#define GEN_PASS_DECL_CONVERTFUNCTOSPIRV
#define GEN_PASS_DECL_CONVERTGPUTOSPIRV
#define GEN_PASS_DECL_CONVERTGPUOPSTOLLVMSPVOPS
#define GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
#define GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
#define GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
#define GEN_PASS_DECL_CONVERTINDEXTOSPIRVPASS
#define GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#define GEN_PASS_DECL_CONVERTMATHTOEMITC
#define GEN_PASS_DECL_CONVERTMATHTOFUNCS
#define GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
#define GEN_PASS_DECL_CONVERTMATHTOLIBM
#define GEN_PASS_DECL_CONVERTMATHTOROCDL
#define GEN_PASS_DECL_CONVERTMATHTOSPIRV
#define GEN_PASS_DECL_CONVERTMEMREFTOEMITC
#define GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
#define GEN_PASS_DECL_CONVERTMESHTOMPIPASS
#define GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
#define GEN_PASS_DECL_CONVERTNVVMTOLLVMPASS
#define GEN_PASS_DECL_CONVERTOPENACCTOSCF
#define GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
#define GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#define GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#define GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
#define GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
#define GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#define GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#define GEN_PASS_DECL_CONVERTTENSORTOLINALG
#define GEN_PASS_DECL_CONVERTTENSORTOSPIRV
#define GEN_PASS_DECL_CONVERTTOLLVMPASS
#define GEN_PASS_DECL_CONVERTVECTORTOARMSME
#define GEN_PASS_DECL_CONVERTVECTORTOGPU
#define GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
#define GEN_PASS_DECL_CONVERTVECTORTOSCF
#define GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#define GEN_PASS_DECL_CONVERTVECTORTOXEGPU
#define GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_LIFTCONTROLFLOWTOSCFPASS
#define GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
#define GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
#define GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#define GEN_PASS_DECL_SCFTOCONTROLFLOW
#define GEN_PASS_DECL_SCFTOEMITC
#define GEN_PASS_DECL_SCFTOSPIRV
#define GEN_PASS_DECL_SETLLVMMODULEDATALAYOUTPASS
#define GEN_PASS_DECL_TOSATOARITH
#define GEN_PASS_DECL_TOSATOLINALG
#define GEN_PASS_DECL_TOSATOLINALGNAMED
#define GEN_PASS_DECL_TOSATOMLPROGRAM
#define GEN_PASS_DECL_TOSATOSCF
#define GEN_PASS_DECL_TOSATOTENSOR
#define GEN_PASS_DECL_UBTOLLVMCONVERSIONPASS
#define GEN_PASS_DECL_UBTOSPIRVCONVERSIONPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ArithToAMDGPUConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHTOAMDGPUCONVERSIONPASS
struct ArithToAMDGPUConversionPassOptions {
  std::string chipset = "gfx000";
  bool saturateFP8Truncf = false;
  bool allowPackedF16Rtz = false;
};
std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass();
std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass(ArithToAMDGPUConversionPassOptions options);
#undef GEN_PASS_DECL_ARITHTOAMDGPUCONVERSIONPASS
#endif // GEN_PASS_DECL_ARITHTOAMDGPUCONVERSIONPASS
#ifdef GEN_PASS_DEF_ARITHTOAMDGPUCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass(ArithToAMDGPUConversionPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithToAMDGPUConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToAMDGPUConversionPassBase;

  ArithToAMDGPUConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToAMDGPUConversionPassBase(const ArithToAMDGPUConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  ArithToAMDGPUConversionPassBase& operator=(const ArithToAMDGPUConversionPassBase &) = delete;
  ArithToAMDGPUConversionPassBase(ArithToAMDGPUConversionPassBase &&) = delete;
  ArithToAMDGPUConversionPassBase& operator=(ArithToAMDGPUConversionPassBase &&) = delete;
  ~ArithToAMDGPUConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-amdgpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-amdgpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith operations to AMDGPU-specific implementations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToAMDGPUConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToAMDGPUConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<amdgpu::AMDGPUDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToAMDGPUConversionPassBase<DerivedT>)

  ArithToAMDGPUConversionPassBase(ArithToAMDGPUConversionPassOptions options) : ArithToAMDGPUConversionPassBase() {
    chipset = std::move(options.chipset);
    saturateFP8Truncf = std::move(options.saturateFP8Truncf);
    allowPackedF16Rtz = std::move(options.allowPackedF16Rtz);
  }
protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
  ::mlir::Pass::Option<bool> saturateFP8Truncf{*this, "saturate-fp8-truncf", ::llvm::cl::desc("Use saturating truncation for 8-bit float types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowPackedF16Rtz{*this, "allow-packed-f16-round-to-zero", ::llvm::cl::desc("Whether we should allow f32->f16 packed round-to-zero conversion"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass(ArithToAMDGPUConversionPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass() {
  return impl::createArithToAMDGPUConversionPass();
}

std::unique_ptr<::mlir::Pass> createArithToAMDGPUConversionPass(ArithToAMDGPUConversionPassOptions options) {
  return impl::createArithToAMDGPUConversionPass(std::move(options));
}
#undef GEN_PASS_DEF_ARITHTOAMDGPUCONVERSIONPASS
#endif // GEN_PASS_DEF_ARITHTOAMDGPUCONVERSIONPASS

//===----------------------------------------------------------------------===//
// ArithToArmSMEConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHTOARMSMECONVERSIONPASS
std::unique_ptr<::mlir::Pass> createArithToArmSMEConversionPass();
#undef GEN_PASS_DECL_ARITHTOARMSMECONVERSIONPASS
#endif // GEN_PASS_DECL_ARITHTOARMSMECONVERSIONPASS
#ifdef GEN_PASS_DEF_ARITHTOARMSMECONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToArmSMEConversionPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithToArmSMEConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToArmSMEConversionPassBase;

  ArithToArmSMEConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToArmSMEConversionPassBase(const ArithToArmSMEConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  ArithToArmSMEConversionPassBase& operator=(const ArithToArmSMEConversionPassBase &) = delete;
  ArithToArmSMEConversionPassBase(ArithToArmSMEConversionPassBase &&) = delete;
  ArithToArmSMEConversionPassBase& operator=(ArithToArmSMEConversionPassBase &&) = delete;
  ~ArithToArmSMEConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-arm-sme");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-arm-sme"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to ArmSME dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToArmSMEConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToArmSMEConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_sme::ArmSMEDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToArmSMEConversionPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createArithToArmSMEConversionPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithToArmSMEConversionPass() {
  return impl::createArithToArmSMEConversionPass();
}
#undef GEN_PASS_DEF_ARITHTOARMSMECONVERSIONPASS
#endif // GEN_PASS_DEF_ARITHTOARMSMECONVERSIONPASS

//===----------------------------------------------------------------------===//
// ArithToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
struct ArithToLLVMConversionPassOptions {
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(ArithToLLVMConversionPassOptions options);
#undef GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_ARITHTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_ARITHTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(ArithToLLVMConversionPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ArithToLLVMConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToLLVMConversionPassBase;

  ArithToLLVMConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToLLVMConversionPassBase(const ArithToLLVMConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  ArithToLLVMConversionPassBase& operator=(const ArithToLLVMConversionPassBase &) = delete;
  ArithToLLVMConversionPassBase(ArithToLLVMConversionPassBase &&) = delete;
  ArithToLLVMConversionPassBase& operator=(ArithToLLVMConversionPassBase &&) = delete;
  ~ArithToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToLLVMConversionPassBase<DerivedT>)

  ArithToLLVMConversionPassBase(ArithToLLVMConversionPassOptions options) : ArithToLLVMConversionPassBase() {
    indexBitwidth = std::move(options.indexBitwidth);
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(ArithToLLVMConversionPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass() {
  return impl::createArithToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createArithToLLVMConversionPass(ArithToLLVMConversionPassOptions options) {
  return impl::createArithToLLVMConversionPass(std::move(options));
}
#undef GEN_PASS_DEF_ARITHTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_ARITHTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// ConvertAMDGPUToROCDL
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
struct ConvertAMDGPUToROCDLOptions {
  std::string chipset = "gfx000";
};
#undef GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
#endif // GEN_PASS_DECL_CONVERTAMDGPUTOROCDL
#ifdef GEN_PASS_DEF_CONVERTAMDGPUTOROCDL
namespace impl {

template <typename DerivedT>
class ConvertAMDGPUToROCDLBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAMDGPUToROCDLBase;

  ConvertAMDGPUToROCDLBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAMDGPUToROCDLBase(const ConvertAMDGPUToROCDLBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertAMDGPUToROCDLBase& operator=(const ConvertAMDGPUToROCDLBase &) = delete;
  ConvertAMDGPUToROCDLBase(ConvertAMDGPUToROCDLBase &&) = delete;
  ConvertAMDGPUToROCDLBase& operator=(ConvertAMDGPUToROCDLBase &&) = delete;
  ~ConvertAMDGPUToROCDLBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-amdgpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-amdgpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Convert AMDGPU dialect to ROCDL dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAMDGPUToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertAMDGPUToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<ROCDL::ROCDLDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAMDGPUToROCDLBase<DerivedT>)

  ConvertAMDGPUToROCDLBase(ConvertAMDGPUToROCDLOptions options) : ConvertAMDGPUToROCDLBase() {
    chipset = std::move(options.chipset);
  }
protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTAMDGPUTOROCDL
#endif // GEN_PASS_DEF_CONVERTAMDGPUTOROCDL

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
struct ConvertAffineForToGPUOptions {
  unsigned numBlockDims = 1u;
  unsigned numThreadDims = 1u;
};
#undef GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
#endif // GEN_PASS_DECL_CONVERTAFFINEFORTOGPU
#ifdef GEN_PASS_DEF_CONVERTAFFINEFORTOGPU
namespace impl {

template <typename DerivedT>
class ConvertAffineForToGPUBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertAffineForToGPUBase;

  ConvertAffineForToGPUBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineForToGPUBase(const ConvertAffineForToGPUBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  ConvertAffineForToGPUBase& operator=(const ConvertAffineForToGPUBase &) = delete;
  ConvertAffineForToGPUBase(ConvertAffineForToGPUBase &&) = delete;
  ConvertAffineForToGPUBase& operator=(ConvertAffineForToGPUBase &&) = delete;
  ~ConvertAffineForToGPUBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-affine-for-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-affine-for-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert top-level AffineFor Ops to GPU kernels"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineForToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineForToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<gpu::GPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineForToGPUBase<DerivedT>)

  ConvertAffineForToGPUBase(ConvertAffineForToGPUOptions options) : ConvertAffineForToGPUBase() {
    numBlockDims = std::move(options.numBlockDims);
    numThreadDims = std::move(options.numThreadDims);
  }
protected:
  ::mlir::Pass::Option<unsigned> numBlockDims{*this, "gpu-block-dims", ::llvm::cl::desc("Number of GPU block dimensions for mapping"), ::llvm::cl::init(1u)};
  ::mlir::Pass::Option<unsigned> numThreadDims{*this, "gpu-thread-dims", ::llvm::cl::desc("Number of GPU thread dimensions for mapping"), ::llvm::cl::init(1u)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTAFFINEFORTOGPU
#endif // GEN_PASS_DEF_CONVERTAFFINEFORTOGPU

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#undef GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#endif // GEN_PASS_DECL_CONVERTAFFINETOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTAFFINETOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertAffineToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAffineToStandardBase;

  ConvertAffineToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineToStandardBase(const ConvertAffineToStandardBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertAffineToStandardBase& operator=(const ConvertAffineToStandardBase &) = delete;
  ConvertAffineToStandardBase(ConvertAffineToStandardBase &&) = delete;
  ConvertAffineToStandardBase& operator=(ConvertAffineToStandardBase &&) = delete;
  ~ConvertAffineToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lower-affine"; }

  ::llvm::StringRef getDescription() const override { return "Lower Affine operations to a combination of Standard and SCF operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTAFFINETOSTANDARD
#endif // GEN_PASS_DEF_CONVERTAFFINETOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertArithToEmitC
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARITHTOEMITC
std::unique_ptr<::mlir::Pass> createConvertArithToEmitC();
#undef GEN_PASS_DECL_CONVERTARITHTOEMITC
#endif // GEN_PASS_DECL_CONVERTARITHTOEMITC
#ifdef GEN_PASS_DEF_CONVERTARITHTOEMITC

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertArithToEmitC();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertArithToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithToEmitCBase;

  ConvertArithToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithToEmitCBase(const ConvertArithToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArithToEmitCBase& operator=(const ConvertArithToEmitCBase &) = delete;
  ConvertArithToEmitCBase(ConvertArithToEmitCBase &&) = delete;
  ConvertArithToEmitCBase& operator=(ConvertArithToEmitCBase &&) = delete;
  ~ConvertArithToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to EmitC dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArithToEmitCBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertArithToEmitC() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertArithToEmitC() {
  return impl::createConvertArithToEmitC();
}
#undef GEN_PASS_DEF_CONVERTARITHTOEMITC
#endif // GEN_PASS_DEF_CONVERTARITHTOEMITC

//===----------------------------------------------------------------------===//
// ConvertArithToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARITHTOSPIRV
struct ConvertArithToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTARITHTOSPIRV
#endif // GEN_PASS_DECL_CONVERTARITHTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTARITHTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertArithToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithToSPIRVBase;

  ConvertArithToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithToSPIRVBase(const ConvertArithToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArithToSPIRVBase& operator=(const ConvertArithToSPIRVBase &) = delete;
  ConvertArithToSPIRVBase(ConvertArithToSPIRVBase &&) = delete;
  ConvertArithToSPIRVBase& operator=(ConvertArithToSPIRVBase &&) = delete;
  ~ConvertArithToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArithToSPIRVBase<DerivedT>)

  ConvertArithToSPIRVBase(ConvertArithToSPIRVOptions options) : ConvertArithToSPIRVBase() {
    emulateLT32BitScalarTypes = std::move(options.emulateLT32BitScalarTypes);
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTARITHTOSPIRV
#endif // GEN_PASS_DEF_CONVERTARITHTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#undef GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#endif // GEN_PASS_DECL_CONVERTARMNEON2DTOINTR
#ifdef GEN_PASS_DEF_CONVERTARMNEON2DTOINTR
namespace impl {

template <typename DerivedT>
class ConvertArmNeon2dToIntrBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmNeon2dToIntrBase;

  ConvertArmNeon2dToIntrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmNeon2dToIntrBase(const ConvertArmNeon2dToIntrBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArmNeon2dToIntrBase& operator=(const ConvertArmNeon2dToIntrBase &) = delete;
  ConvertArmNeon2dToIntrBase(ConvertArmNeon2dToIntrBase &&) = delete;
  ConvertArmNeon2dToIntrBase& operator=(ConvertArmNeon2dToIntrBase &&) = delete;
  ~ConvertArmNeon2dToIntrBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-neon-2d-to-intr");
  }
  ::llvm::StringRef getArgument() const override { return "arm-neon-2d-to-intr"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arm NEON structured ops to intrinsics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmNeon2dToIntr");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmNeon2dToIntr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_neon::ArmNeonDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmNeon2dToIntrBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTARMNEON2DTOINTR
#endif // GEN_PASS_DEF_CONVERTARMNEON2DTOINTR

//===----------------------------------------------------------------------===//
// ConvertArmSMEToLLVM
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARMSMETOLLVM
struct ConvertArmSMEToLLVMOptions {
  bool dumpTileLiveRanges = false;
};
#undef GEN_PASS_DECL_CONVERTARMSMETOLLVM
#endif // GEN_PASS_DECL_CONVERTARMSMETOLLVM
#ifdef GEN_PASS_DEF_CONVERTARMSMETOLLVM
namespace impl {

template <typename DerivedT>
class ConvertArmSMEToLLVMBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertArmSMEToLLVMBase;

  ConvertArmSMEToLLVMBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmSMEToLLVMBase(const ConvertArmSMEToLLVMBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  ConvertArmSMEToLLVMBase& operator=(const ConvertArmSMEToLLVMBase &) = delete;
  ConvertArmSMEToLLVMBase(ConvertArmSMEToLLVMBase &&) = delete;
  ConvertArmSMEToLLVMBase& operator=(ConvertArmSMEToLLVMBase &&) = delete;
  ~ConvertArmSMEToLLVMBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arm-sme-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arm-sme-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the ArmSME dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmSMEToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmSMEToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmSMEToLLVMBase<DerivedT>)

  ConvertArmSMEToLLVMBase(ConvertArmSMEToLLVMOptions options) : ConvertArmSMEToLLVMBase() {
    dumpTileLiveRanges = std::move(options.dumpTileLiveRanges);
  }
protected:
  ::mlir::Pass::Option<bool> dumpTileLiveRanges{*this, "dump-tile-live-ranges", ::llvm::cl::desc("Dump the live ranges of SME tiles (for debugging)"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTARMSMETOLLVM
#endif // GEN_PASS_DEF_CONVERTARMSMETOLLVM

//===----------------------------------------------------------------------===//
// ConvertArmSMEToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTARMSMETOSCF
#undef GEN_PASS_DECL_CONVERTARMSMETOSCF
#endif // GEN_PASS_DECL_CONVERTARMSMETOSCF
#ifdef GEN_PASS_DEF_CONVERTARMSMETOSCF
namespace impl {

template <typename DerivedT>
class ConvertArmSMEToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmSMEToSCFBase;

  ConvertArmSMEToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmSMEToSCFBase(const ConvertArmSMEToSCFBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArmSMEToSCFBase& operator=(const ConvertArmSMEToSCFBase &) = delete;
  ConvertArmSMEToSCFBase(ConvertArmSMEToSCFBase &&) = delete;
  ConvertArmSMEToSCFBase& operator=(ConvertArmSMEToSCFBase &&) = delete;
  ~ConvertArmSMEToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arm-sme-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arm-sme-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the ArmSME dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmSMEToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmSMEToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<arith::ArithDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<arm_sme::ArmSMEDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmSMEToSCFBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTARMSMETOSCF
#endif // GEN_PASS_DEF_CONVERTARMSMETOSCF

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass();
#undef GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTASYNCTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTASYNCTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertAsyncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertAsyncToLLVMPassBase;

  ConvertAsyncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAsyncToLLVMPassBase(const ConvertAsyncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertAsyncToLLVMPassBase& operator=(const ConvertAsyncToLLVMPassBase &) = delete;
  ConvertAsyncToLLVMPassBase(ConvertAsyncToLLVMPassBase &&) = delete;
  ConvertAsyncToLLVMPassBase& operator=(ConvertAsyncToLLVMPassBase &&) = delete;
  ~ConvertAsyncToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-async-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-async-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the async dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAsyncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertAsyncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<async::AsyncDialect>();
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAsyncToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertAsyncToLLVMPass() {
  return impl::createConvertAsyncToLLVMPass();
}
#undef GEN_PASS_DEF_CONVERTASYNCTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTASYNCTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertBufferizationToMemRef
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#undef GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#endif // GEN_PASS_DECL_CONVERTBUFFERIZATIONTOMEMREF
#ifdef GEN_PASS_DEF_CONVERTBUFFERIZATIONTOMEMREF
namespace impl {

template <typename DerivedT>
class ConvertBufferizationToMemRefBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertBufferizationToMemRefBase;

  ConvertBufferizationToMemRefBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertBufferizationToMemRefBase(const ConvertBufferizationToMemRefBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertBufferizationToMemRefBase& operator=(const ConvertBufferizationToMemRefBase &) = delete;
  ConvertBufferizationToMemRefBase(ConvertBufferizationToMemRefBase &&) = delete;
  ConvertBufferizationToMemRefBase& operator=(ConvertBufferizationToMemRefBase &&) = delete;
  ~ConvertBufferizationToMemRefBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-bufferization-to-memref");
  }
  ::llvm::StringRef getArgument() const override { return "convert-bufferization-to-memref"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the Bufferization dialect to the MemRef dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertBufferizationToMemRef");
  }
  ::llvm::StringRef getName() const override { return "ConvertBufferizationToMemRef"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertBufferizationToMemRefBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTBUFFERIZATIONTOMEMREF
#endif // GEN_PASS_DEF_CONVERTBUFFERIZATIONTOMEMREF

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass();
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertComplexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToLLVMPassBase;

  ConvertComplexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLLVMPassBase(const ConvertComplexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertComplexToLLVMPassBase& operator=(const ConvertComplexToLLVMPassBase &) = delete;
  ConvertComplexToLLVMPassBase(ConvertComplexToLLVMPassBase &&) = delete;
  ConvertComplexToLLVMPassBase& operator=(ConvertComplexToLLVMPassBase &&) = delete;
  ~ConvertComplexToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertComplexToLLVMPass() {
  return impl::createConvertComplexToLLVMPass();
}
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertComplexToLibm
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOLIBM
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOLIBM
namespace impl {

template <typename DerivedT>
class ConvertComplexToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertComplexToLibmBase;

  ConvertComplexToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLibmBase(const ConvertComplexToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertComplexToLibmBase& operator=(const ConvertComplexToLibmBase &) = delete;
  ConvertComplexToLibmBase(ConvertComplexToLibmBase &&) = delete;
  ConvertComplexToLibmBase& operator=(ConvertComplexToLibmBase &&) = delete;
  ~ConvertComplexToLibmBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLibmBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOLIBM
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOLIBM

//===----------------------------------------------------------------------===//
// ConvertComplexToSPIRVPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass();
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOSPIRVPASS
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOSPIRVPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertComplexToSPIRVPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToSPIRVPassBase;

  ConvertComplexToSPIRVPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToSPIRVPassBase(const ConvertComplexToSPIRVPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertComplexToSPIRVPassBase& operator=(const ConvertComplexToSPIRVPassBase &) = delete;
  ConvertComplexToSPIRVPassBase(ConvertComplexToSPIRVPassBase &&) = delete;
  ConvertComplexToSPIRVPassBase& operator=(ConvertComplexToSPIRVPassBase &&) = delete;
  ~ConvertComplexToSPIRVPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to SPIRV dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToSPIRVPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToSPIRVPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToSPIRVPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertComplexToSPIRVPass() {
  return impl::createConvertComplexToSPIRVPass();
}
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOSPIRVPASS
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOSPIRVPASS

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#undef GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#endif // GEN_PASS_DECL_CONVERTCOMPLEXTOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTCOMPLEXTOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertComplexToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToStandardBase;

  ConvertComplexToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToStandardBase(const ConvertComplexToStandardBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertComplexToStandardBase& operator=(const ConvertComplexToStandardBase &) = delete;
  ConvertComplexToStandardBase(ConvertComplexToStandardBase &&) = delete;
  ConvertComplexToStandardBase& operator=(ConvertComplexToStandardBase &&) = delete;
  ~ConvertComplexToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<math::MathDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTCOMPLEXTOSTANDARD
#endif // GEN_PASS_DEF_CONVERTCOMPLEXTOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertControlFlowToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
struct ConvertControlFlowToLLVMPassOptions {
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(ConvertControlFlowToLLVMPassOptions options);
#undef GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTCONTROLFLOWTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTCONTROLFLOWTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(ConvertControlFlowToLLVMPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertControlFlowToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertControlFlowToLLVMPassBase;

  ConvertControlFlowToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToLLVMPassBase(const ConvertControlFlowToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertControlFlowToLLVMPassBase& operator=(const ConvertControlFlowToLLVMPassBase &) = delete;
  ConvertControlFlowToLLVMPassBase(ConvertControlFlowToLLVMPassBase &&) = delete;
  ConvertControlFlowToLLVMPassBase& operator=(ConvertControlFlowToLLVMPassBase &&) = delete;
  ~ConvertControlFlowToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow operations to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToLLVMPassBase<DerivedT>)

  ConvertControlFlowToLLVMPassBase(ConvertControlFlowToLLVMPassOptions options) : ConvertControlFlowToLLVMPassBase() {
    indexBitwidth = std::move(options.indexBitwidth);
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(ConvertControlFlowToLLVMPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass() {
  return impl::createConvertControlFlowToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertControlFlowToLLVMPass(ConvertControlFlowToLLVMPassOptions options) {
  return impl::createConvertControlFlowToLLVMPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTCONTROLFLOWTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTCONTROLFLOWTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertControlFlowToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
struct ConvertControlFlowToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
#endif // GEN_PASS_DECL_CONVERTCONTROLFLOWTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTCONTROLFLOWTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertControlFlowToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertControlFlowToSPIRVBase;

  ConvertControlFlowToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToSPIRVBase(const ConvertControlFlowToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertControlFlowToSPIRVBase& operator=(const ConvertControlFlowToSPIRVBase &) = delete;
  ConvertControlFlowToSPIRVBase(ConvertControlFlowToSPIRVBase &&) = delete;
  ConvertControlFlowToSPIRVBase& operator=(ConvertControlFlowToSPIRVBase &&) = delete;
  ~ConvertControlFlowToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToSPIRVBase<DerivedT>)

  ConvertControlFlowToSPIRVBase(ConvertControlFlowToSPIRVOptions options) : ConvertControlFlowToSPIRVBase() {
    emulateLT32BitScalarTypes = std::move(options.emulateLT32BitScalarTypes);
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTCONTROLFLOWTOSPIRV
#endif // GEN_PASS_DEF_CONVERTCONTROLFLOWTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertFuncToEmitC
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTFUNCTOEMITC
std::unique_ptr<::mlir::Pass> createConvertFuncToEmitC();
#undef GEN_PASS_DECL_CONVERTFUNCTOEMITC
#endif // GEN_PASS_DECL_CONVERTFUNCTOEMITC
#ifdef GEN_PASS_DEF_CONVERTFUNCTOEMITC

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertFuncToEmitC();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertFuncToEmitCBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToEmitCBase;

  ConvertFuncToEmitCBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToEmitCBase(const ConvertFuncToEmitCBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertFuncToEmitCBase& operator=(const ConvertFuncToEmitCBase &) = delete;
  ConvertFuncToEmitCBase(ConvertFuncToEmitCBase &&) = delete;
  ConvertFuncToEmitCBase& operator=(ConvertFuncToEmitCBase &&) = delete;
  ~ConvertFuncToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to EmitC dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToEmitCBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertFuncToEmitC() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertFuncToEmitC() {
  return impl::createConvertFuncToEmitC();
}
#undef GEN_PASS_DEF_CONVERTFUNCTOEMITC
#endif // GEN_PASS_DEF_CONVERTFUNCTOEMITC

//===----------------------------------------------------------------------===//
// ConvertFuncToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
struct ConvertFuncToLLVMPassOptions {
  bool useBarePtrCallConv = false;
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(ConvertFuncToLLVMPassOptions options);
#undef GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTFUNCTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTFUNCTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(ConvertFuncToLLVMPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertFuncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToLLVMPassBase;

  ConvertFuncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToLLVMPassBase(const ConvertFuncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertFuncToLLVMPassBase& operator=(const ConvertFuncToLLVMPassBase &) = delete;
  ConvertFuncToLLVMPassBase(ConvertFuncToLLVMPassBase &&) = delete;
  ConvertFuncToLLVMPassBase& operator=(ConvertFuncToLLVMPassBase &&) = delete;
  ~ConvertFuncToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert from the Func dialect to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToLLVMPassBase<DerivedT>)

  ConvertFuncToLLVMPassBase(ConvertFuncToLLVMPassOptions options) : ConvertFuncToLLVMPassBase() {
    useBarePtrCallConv = std::move(options.useBarePtrCallConv);
    indexBitwidth = std::move(options.indexBitwidth);
  }
protected:
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace FuncOp's MemRef arguments with bare pointers to the MemRef element types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(ConvertFuncToLLVMPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass() {
  return impl::createConvertFuncToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertFuncToLLVMPass(ConvertFuncToLLVMPassOptions options) {
  return impl::createConvertFuncToLLVMPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTFUNCTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTFUNCTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertFuncToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTFUNCTOSPIRV
struct ConvertFuncToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTFUNCTOSPIRV
#endif // GEN_PASS_DECL_CONVERTFUNCTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTFUNCTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertFuncToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertFuncToSPIRVBase;

  ConvertFuncToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToSPIRVBase(const ConvertFuncToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertFuncToSPIRVBase& operator=(const ConvertFuncToSPIRVBase &) = delete;
  ConvertFuncToSPIRVBase(ConvertFuncToSPIRVBase &&) = delete;
  ConvertFuncToSPIRVBase& operator=(ConvertFuncToSPIRVBase &&) = delete;
  ~ConvertFuncToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToSPIRVBase<DerivedT>)

  ConvertFuncToSPIRVBase(ConvertFuncToSPIRVOptions options) : ConvertFuncToSPIRVBase() {
    emulateLT32BitScalarTypes = std::move(options.emulateLT32BitScalarTypes);
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTFUNCTOSPIRV
#endif // GEN_PASS_DEF_CONVERTFUNCTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUTOSPIRV
struct ConvertGPUToSPIRVOptions {
  bool use64bitIndex = false;
};
#undef GEN_PASS_DECL_CONVERTGPUTOSPIRV
#endif // GEN_PASS_DECL_CONVERTGPUTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTGPUTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertGPUToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGPUToSPIRVBase;

  ConvertGPUToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertGPUToSPIRVBase& operator=(const ConvertGPUToSPIRVBase &) = delete;
  ConvertGPUToSPIRVBase(ConvertGPUToSPIRVBase &&) = delete;
  ConvertGPUToSPIRVBase& operator=(ConvertGPUToSPIRVBase &&) = delete;
  ~ConvertGPUToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGPUToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertGPUToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGPUToSPIRVBase<DerivedT>)

  ConvertGPUToSPIRVBase(ConvertGPUToSPIRVOptions options) : ConvertGPUToSPIRVBase() {
    use64bitIndex = std::move(options.use64bitIndex);
  }
protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTGPUTOSPIRV
#endif // GEN_PASS_DEF_CONVERTGPUTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToLLVMSPVOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUOPSTOLLVMSPVOPS
struct ConvertGpuOpsToLLVMSPVOpsOptions {
  bool use64bitIndex = false;
};
std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps();
std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps(ConvertGpuOpsToLLVMSPVOpsOptions options);
#undef GEN_PASS_DECL_CONVERTGPUOPSTOLLVMSPVOPS
#endif // GEN_PASS_DECL_CONVERTGPUOPSTOLLVMSPVOPS
#ifdef GEN_PASS_DEF_CONVERTGPUOPSTOLLVMSPVOPS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps(ConvertGpuOpsToLLVMSPVOpsOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertGpuOpsToLLVMSPVOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToLLVMSPVOpsBase;

  ConvertGpuOpsToLLVMSPVOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToLLVMSPVOpsBase(const ConvertGpuOpsToLLVMSPVOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}
  ConvertGpuOpsToLLVMSPVOpsBase& operator=(const ConvertGpuOpsToLLVMSPVOpsBase &) = delete;
  ConvertGpuOpsToLLVMSPVOpsBase(ConvertGpuOpsToLLVMSPVOpsBase &&) = delete;
  ConvertGpuOpsToLLVMSPVOpsBase& operator=(ConvertGpuOpsToLLVMSPVOpsBase &&) = delete;
  ~ConvertGpuOpsToLLVMSPVOpsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-llvm-spv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-llvm-spv"; }

  ::llvm::StringRef getDescription() const override { return "Generate LLVM operations to be ingested by a SPIR-V backend for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToLLVMSPVOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToLLVMSPVOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToLLVMSPVOpsBase<DerivedT>)

  ConvertGpuOpsToLLVMSPVOpsBase(ConvertGpuOpsToLLVMSPVOpsOptions options) : ConvertGpuOpsToLLVMSPVOpsBase() {
    use64bitIndex = std::move(options.use64bitIndex);
  }
protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps(ConvertGpuOpsToLLVMSPVOpsOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps() {
  return impl::createConvertGpuOpsToLLVMSPVOps();
}

std::unique_ptr<::mlir::Pass> createConvertGpuOpsToLLVMSPVOps(ConvertGpuOpsToLLVMSPVOpsOptions options) {
  return impl::createConvertGpuOpsToLLVMSPVOps(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTGPUOPSTOLLVMSPVOPS
#endif // GEN_PASS_DEF_CONVERTGPUOPSTOLLVMSPVOPS

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
struct ConvertGpuOpsToNVVMOpsOptions {
  unsigned indexBitwidth = 0;
  bool hasRedux = false;
  bool useBarePtrCallConv = false;
};
std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps();
std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps(ConvertGpuOpsToNVVMOpsOptions options);
#undef GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
#endif // GEN_PASS_DECL_CONVERTGPUOPSTONVVMOPS
#ifdef GEN_PASS_DEF_CONVERTGPUOPSTONVVMOPS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps(ConvertGpuOpsToNVVMOpsOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertGpuOpsToNVVMOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToNVVMOpsBase;

  ConvertGpuOpsToNVVMOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}
  ConvertGpuOpsToNVVMOpsBase& operator=(const ConvertGpuOpsToNVVMOpsBase &) = delete;
  ConvertGpuOpsToNVVMOpsBase(ConvertGpuOpsToNVVMOpsBase &&) = delete;
  ConvertGpuOpsToNVVMOpsBase& operator=(ConvertGpuOpsToNVVMOpsBase &&) = delete;
  ~ConvertGpuOpsToNVVMOpsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Generate NVVM operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToNVVMOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToNVVMOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<NVVM::NVVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToNVVMOpsBase<DerivedT>)

  ConvertGpuOpsToNVVMOpsBase(ConvertGpuOpsToNVVMOpsOptions options) : ConvertGpuOpsToNVVMOpsBase() {
    indexBitwidth = std::move(options.indexBitwidth);
    hasRedux = std::move(options.hasRedux);
    useBarePtrCallConv = std::move(options.useBarePtrCallConv);
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> hasRedux{*this, "has-redux", ::llvm::cl::desc("Target gpu supports redux"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace memref arguments in GPU functions with bare pointers. All memrefs must have static shape."), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps(ConvertGpuOpsToNVVMOpsOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps() {
  return impl::createConvertGpuOpsToNVVMOps();
}

std::unique_ptr<::mlir::Pass> createConvertGpuOpsToNVVMOps(ConvertGpuOpsToNVVMOpsOptions options) {
  return impl::createConvertGpuOpsToNVVMOps(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTGPUOPSTONVVMOPS
#endif // GEN_PASS_DEF_CONVERTGPUOPSTONVVMOPS

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
struct ConvertGpuOpsToROCDLOpsOptions {
  std::string chipset = "gfx000";
  unsigned indexBitwidth = 0;
  bool useBarePtrCallConv = false;
  ::mlir::gpu::amd::Runtime runtime = ::mlir::gpu::amd::Runtime::Unknown;
};
#undef GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
#endif // GEN_PASS_DECL_CONVERTGPUOPSTOROCDLOPS
#ifdef GEN_PASS_DEF_CONVERTGPUOPSTOROCDLOPS
namespace impl {

template <typename DerivedT>
class ConvertGpuOpsToROCDLOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToROCDLOpsBase;

  ConvertGpuOpsToROCDLOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}
  ConvertGpuOpsToROCDLOpsBase& operator=(const ConvertGpuOpsToROCDLOpsBase &) = delete;
  ConvertGpuOpsToROCDLOpsBase(ConvertGpuOpsToROCDLOpsBase &&) = delete;
  ConvertGpuOpsToROCDLOpsBase& operator=(ConvertGpuOpsToROCDLOpsBase &&) = delete;
  ~ConvertGpuOpsToROCDLOpsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Generate ROCDL operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToROCDLOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToROCDLOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<ROCDL::ROCDLDialect>();
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToROCDLOpsBase<DerivedT>)

  ConvertGpuOpsToROCDLOpsBase(ConvertGpuOpsToROCDLOpsOptions options) : ConvertGpuOpsToROCDLOpsBase() {
    chipset = std::move(options.chipset);
    indexBitwidth = std::move(options.indexBitwidth);
    useBarePtrCallConv = std::move(options.useBarePtrCallConv);
    runtime = std::move(options.runtime);
  }
protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace memref arguments in GPU functions with bare pointers.All memrefs must have static shape"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<::mlir::gpu::amd::Runtime> runtime{*this, "runtime", ::llvm::cl::desc("Runtime code will be run on (default is Unknown, can also use HIP or OpenCl)"), ::llvm::cl::init(::mlir::gpu::amd::Runtime::Unknown), ::llvm::cl::values(
            clEnumValN(::mlir::gpu::amd::Runtime::Unknown, "unknown", "Unknown (default)"),
            clEnumValN(::mlir::gpu::amd::Runtime::HIP, "HIP", "HIP"),
            clEnumValN(::mlir::gpu::amd::Runtime::OpenCL, "OpenCL", "OpenCL")
          )};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTGPUOPSTOROCDLOPS
#endif // GEN_PASS_DEF_CONVERTGPUOPSTOROCDLOPS

//===----------------------------------------------------------------------===//
// ConvertIndexToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
struct ConvertIndexToLLVMPassOptions {
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(ConvertIndexToLLVMPassOptions options);
#undef GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTINDEXTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTINDEXTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(ConvertIndexToLLVMPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertIndexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertIndexToLLVMPassBase;

  ConvertIndexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertIndexToLLVMPassBase(const ConvertIndexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertIndexToLLVMPassBase& operator=(const ConvertIndexToLLVMPassBase &) = delete;
  ConvertIndexToLLVMPassBase(ConvertIndexToLLVMPassBase &&) = delete;
  ConvertIndexToLLVMPassBase& operator=(ConvertIndexToLLVMPassBase &&) = delete;
  ~ConvertIndexToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-index-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-index-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the `index` dialect to the `llvm` dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertIndexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertIndexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertIndexToLLVMPassBase<DerivedT>)

  ConvertIndexToLLVMPassBase(ConvertIndexToLLVMPassOptions options) : ConvertIndexToLLVMPassBase() {
    indexBitwidth = std::move(options.indexBitwidth);
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(ConvertIndexToLLVMPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass() {
  return impl::createConvertIndexToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertIndexToLLVMPass(ConvertIndexToLLVMPassOptions options) {
  return impl::createConvertIndexToLLVMPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTINDEXTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTINDEXTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertIndexToSPIRVPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTINDEXTOSPIRVPASS
struct ConvertIndexToSPIRVPassOptions {
  bool use64bitIndex = false;
};
std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass();
std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass(ConvertIndexToSPIRVPassOptions options);
#undef GEN_PASS_DECL_CONVERTINDEXTOSPIRVPASS
#endif // GEN_PASS_DECL_CONVERTINDEXTOSPIRVPASS
#ifdef GEN_PASS_DEF_CONVERTINDEXTOSPIRVPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass(ConvertIndexToSPIRVPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertIndexToSPIRVPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertIndexToSPIRVPassBase;

  ConvertIndexToSPIRVPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertIndexToSPIRVPassBase(const ConvertIndexToSPIRVPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertIndexToSPIRVPassBase& operator=(const ConvertIndexToSPIRVPassBase &) = delete;
  ConvertIndexToSPIRVPassBase(ConvertIndexToSPIRVPassBase &&) = delete;
  ConvertIndexToSPIRVPassBase& operator=(ConvertIndexToSPIRVPassBase &&) = delete;
  ~ConvertIndexToSPIRVPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-index-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-index-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Lower the `index` dialect to the `spirv` dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertIndexToSPIRVPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertIndexToSPIRVPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertIndexToSPIRVPassBase<DerivedT>)

  ConvertIndexToSPIRVPassBase(ConvertIndexToSPIRVPassOptions options) : ConvertIndexToSPIRVPassBase() {
    use64bitIndex = std::move(options.use64bitIndex);
  }
protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass(ConvertIndexToSPIRVPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass() {
  return impl::createConvertIndexToSPIRVPass();
}

std::unique_ptr<::mlir::Pass> createConvertIndexToSPIRVPass(ConvertIndexToSPIRVPassOptions options) {
  return impl::createConvertIndexToSPIRVPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTINDEXTOSPIRVPASS
#endif // GEN_PASS_DEF_CONVERTINDEXTOSPIRVPASS

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#undef GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#endif // GEN_PASS_DECL_CONVERTLINALGTOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTLINALGTOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertLinalgToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToStandardBase;

  ConvertLinalgToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToStandardBase(const ConvertLinalgToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertLinalgToStandardBase& operator=(const ConvertLinalgToStandardBase &) = delete;
  ConvertLinalgToStandardBase(ConvertLinalgToStandardBase &&) = delete;
  ConvertLinalgToStandardBase& operator=(ConvertLinalgToStandardBase &&) = delete;
  ~ConvertLinalgToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTLINALGTOSTANDARD
#endif // GEN_PASS_DEF_CONVERTLINALGTOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertMathToEmitC
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOEMITC
struct ConvertMathToEmitCOptions {
  ::mlir::emitc::LanguageTarget languageTarget = ::mlir::emitc::LanguageTarget::c99;
};
std::unique_ptr<::mlir::Pass> createConvertMathToEmitC();
std::unique_ptr<::mlir::Pass> createConvertMathToEmitC(ConvertMathToEmitCOptions options);
#undef GEN_PASS_DECL_CONVERTMATHTOEMITC
#endif // GEN_PASS_DECL_CONVERTMATHTOEMITC
#ifdef GEN_PASS_DEF_CONVERTMATHTOEMITC

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToEmitC();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToEmitC(ConvertMathToEmitCOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMathToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToEmitCBase;

  ConvertMathToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToEmitCBase(const ConvertMathToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMathToEmitCBase& operator=(const ConvertMathToEmitCBase &) = delete;
  ConvertMathToEmitCBase(ConvertMathToEmitCBase &&) = delete;
  ConvertMathToEmitCBase& operator=(ConvertMathToEmitCBase &&) = delete;
  ~ConvertMathToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert some Math operations to EmitC call_opaque operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToEmitCBase<DerivedT>)

  ConvertMathToEmitCBase(ConvertMathToEmitCOptions options) : ConvertMathToEmitCBase() {
    languageTarget = std::move(options.languageTarget);
  }
protected:
  ::mlir::Pass::Option<::mlir::emitc::LanguageTarget> languageTarget{*this, "language-target", ::llvm::cl::desc("Select the language standard target for callees (c99 or cpp11)."), ::llvm::cl::init(::mlir::emitc::LanguageTarget::c99), ::llvm::cl::values(
            clEnumValN(::mlir::emitc::LanguageTarget::c99, "c99", "c99"),
            clEnumValN(::mlir::emitc::LanguageTarget::cpp11, "cpp11", "cpp11")
          )};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMathToEmitC() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertMathToEmitC(ConvertMathToEmitCOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMathToEmitC() {
  return impl::createConvertMathToEmitC();
}

std::unique_ptr<::mlir::Pass> createConvertMathToEmitC(ConvertMathToEmitCOptions options) {
  return impl::createConvertMathToEmitC(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTMATHTOEMITC
#endif // GEN_PASS_DEF_CONVERTMATHTOEMITC

//===----------------------------------------------------------------------===//
// ConvertMathToFuncs
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOFUNCS
struct ConvertMathToFuncsOptions {
  unsigned minWidthOfFPowIExponent = 1;
  bool convertCtlz = false;
};
std::unique_ptr<::mlir::Pass> createConvertMathToFuncs();
std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(ConvertMathToFuncsOptions options);
#undef GEN_PASS_DECL_CONVERTMATHTOFUNCS
#endif // GEN_PASS_DECL_CONVERTMATHTOFUNCS
#ifdef GEN_PASS_DEF_CONVERTMATHTOFUNCS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToFuncs();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(ConvertMathToFuncsOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMathToFuncsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToFuncsBase;

  ConvertMathToFuncsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToFuncsBase(const ConvertMathToFuncsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertMathToFuncsBase& operator=(const ConvertMathToFuncsBase &) = delete;
  ConvertMathToFuncsBase(ConvertMathToFuncsBase &&) = delete;
  ConvertMathToFuncsBase& operator=(ConvertMathToFuncsBase &&) = delete;
  ~ConvertMathToFuncsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-funcs");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-funcs"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math operations to calls of outlined implementations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToFuncs");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToFuncs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToFuncsBase<DerivedT>)

  ConvertMathToFuncsBase(ConvertMathToFuncsOptions options) : ConvertMathToFuncsBase() {
    minWidthOfFPowIExponent = std::move(options.minWidthOfFPowIExponent);
    convertCtlz = std::move(options.convertCtlz);
  }
protected:
  ::mlir::Pass::Option<unsigned> minWidthOfFPowIExponent{*this, "min-width-of-fpowi-exponent", ::llvm::cl::desc("Convert FPowI only if the width of its exponent's integer type is greater than or equal to this value"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> convertCtlz{*this, "convert-ctlz", ::llvm::cl::desc("Convert math.ctlz to a software implementation. Enable for targets that do not natively support ctlz."), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMathToFuncs() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(ConvertMathToFuncsOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMathToFuncs() {
  return impl::createConvertMathToFuncs();
}

std::unique_ptr<::mlir::Pass> createConvertMathToFuncs(ConvertMathToFuncsOptions options) {
  return impl::createConvertMathToFuncs(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTMATHTOFUNCS
#endif // GEN_PASS_DEF_CONVERTMATHTOFUNCS

//===----------------------------------------------------------------------===//
// ConvertMathToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
struct ConvertMathToLLVMPassOptions {
  bool approximateLog1p = true;
};
std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(ConvertMathToLLVMPassOptions options);
#undef GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTMATHTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTMATHTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(ConvertMathToLLVMPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMathToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToLLVMPassBase;

  ConvertMathToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLLVMPassBase(const ConvertMathToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMathToLLVMPassBase& operator=(const ConvertMathToLLVMPassBase &) = delete;
  ConvertMathToLLVMPassBase(ConvertMathToLLVMPassBase &&) = delete;
  ConvertMathToLLVMPassBase& operator=(ConvertMathToLLVMPassBase &&) = delete;
  ~ConvertMathToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLLVMPassBase<DerivedT>)

  ConvertMathToLLVMPassBase(ConvertMathToLLVMPassOptions options) : ConvertMathToLLVMPassBase() {
    approximateLog1p = std::move(options.approximateLog1p);
  }
protected:
  ::mlir::Pass::Option<bool> approximateLog1p{*this, "approximate-log1p", ::llvm::cl::desc("Enable approximation of Log1p."), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(ConvertMathToLLVMPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass() {
  return impl::createConvertMathToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertMathToLLVMPass(ConvertMathToLLVMPassOptions options) {
  return impl::createConvertMathToLLVMPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTMATHTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTMATHTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertMathToLibm
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOLIBM
#undef GEN_PASS_DECL_CONVERTMATHTOLIBM
#endif // GEN_PASS_DECL_CONVERTMATHTOLIBM
#ifdef GEN_PASS_DEF_CONVERTMATHTOLIBM
namespace impl {

template <typename DerivedT>
class ConvertMathToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToLibmBase;

  ConvertMathToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLibmBase(const ConvertMathToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertMathToLibmBase& operator=(const ConvertMathToLibmBase &) = delete;
  ConvertMathToLibmBase(ConvertMathToLibmBase &&) = delete;
  ConvertMathToLibmBase& operator=(ConvertMathToLibmBase &&) = delete;
  ~ConvertMathToLibmBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLibmBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMATHTOLIBM
#endif // GEN_PASS_DEF_CONVERTMATHTOLIBM

//===----------------------------------------------------------------------===//
// ConvertMathToROCDL
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOROCDL
std::unique_ptr<::mlir::Pass> createConvertMathToROCDL();
#undef GEN_PASS_DECL_CONVERTMATHTOROCDL
#endif // GEN_PASS_DECL_CONVERTMATHTOROCDL
#ifdef GEN_PASS_DEF_CONVERTMATHTOROCDL

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMathToROCDL();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMathToROCDLBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToROCDLBase;

  ConvertMathToROCDLBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToROCDLBase(const ConvertMathToROCDLBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertMathToROCDLBase& operator=(const ConvertMathToROCDLBase &) = delete;
  ConvertMathToROCDLBase(ConvertMathToROCDLBase &&) = delete;
  ConvertMathToROCDLBase& operator=(ConvertMathToROCDLBase &&) = delete;
  ~ConvertMathToROCDLBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to ROCDL library calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<ROCDL::ROCDLDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToROCDLBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMathToROCDL() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMathToROCDL() {
  return impl::createConvertMathToROCDL();
}
#undef GEN_PASS_DEF_CONVERTMATHTOROCDL
#endif // GEN_PASS_DEF_CONVERTMATHTOROCDL

//===----------------------------------------------------------------------===//
// ConvertMathToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMATHTOSPIRV
#undef GEN_PASS_DECL_CONVERTMATHTOSPIRV
#endif // GEN_PASS_DECL_CONVERTMATHTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTMATHTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertMathToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToSPIRVBase;

  ConvertMathToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToSPIRVBase(const ConvertMathToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMathToSPIRVBase& operator=(const ConvertMathToSPIRVBase &) = delete;
  ConvertMathToSPIRVBase(ConvertMathToSPIRVBase &&) = delete;
  ConvertMathToSPIRVBase& operator=(ConvertMathToSPIRVBase &&) = delete;
  ~ConvertMathToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToSPIRVBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMATHTOSPIRV
#endif // GEN_PASS_DEF_CONVERTMATHTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertMemRefToEmitC
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMEMREFTOEMITC
std::unique_ptr<::mlir::Pass> createConvertMemRefToEmitC();
#undef GEN_PASS_DECL_CONVERTMEMREFTOEMITC
#endif // GEN_PASS_DECL_CONVERTMEMREFTOEMITC
#ifdef GEN_PASS_DEF_CONVERTMEMREFTOEMITC

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertMemRefToEmitC();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertMemRefToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMemRefToEmitCBase;

  ConvertMemRefToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToEmitCBase(const ConvertMemRefToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMemRefToEmitCBase& operator=(const ConvertMemRefToEmitCBase &) = delete;
  ConvertMemRefToEmitCBase(ConvertMemRefToEmitCBase &&) = delete;
  ConvertMemRefToEmitCBase& operator=(ConvertMemRefToEmitCBase &&) = delete;
  ~ConvertMemRefToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to EmitC dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMemRefToEmitCBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertMemRefToEmitC() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertMemRefToEmitC() {
  return impl::createConvertMemRefToEmitC();
}
#undef GEN_PASS_DEF_CONVERTMEMREFTOEMITC
#endif // GEN_PASS_DEF_CONVERTMEMREFTOEMITC

//===----------------------------------------------------------------------===//
// ConvertMemRefToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
struct ConvertMemRefToSPIRVOptions {
  int boolNumBits = 8;
  bool use64bitIndex = false;
};
#undef GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
#endif // GEN_PASS_DECL_CONVERTMEMREFTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTMEMREFTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertMemRefToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMemRefToSPIRVBase;

  ConvertMemRefToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToSPIRVBase(const ConvertMemRefToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMemRefToSPIRVBase& operator=(const ConvertMemRefToSPIRVBase &) = delete;
  ConvertMemRefToSPIRVBase(ConvertMemRefToSPIRVBase &&) = delete;
  ConvertMemRefToSPIRVBase& operator=(ConvertMemRefToSPIRVBase &&) = delete;
  ~ConvertMemRefToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMemRefToSPIRVBase<DerivedT>)

  ConvertMemRefToSPIRVBase(ConvertMemRefToSPIRVOptions options) : ConvertMemRefToSPIRVBase() {
    boolNumBits = std::move(options.boolNumBits);
    use64bitIndex = std::move(options.use64bitIndex);
  }
protected:
  ::mlir::Pass::Option<int> boolNumBits{*this, "bool-num-bits", ::llvm::cl::desc("The number of bits to store a boolean value"), ::llvm::cl::init(8)};
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMEMREFTOSPIRV
#endif // GEN_PASS_DEF_CONVERTMEMREFTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertMeshToMPIPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTMESHTOMPIPASS
#undef GEN_PASS_DECL_CONVERTMESHTOMPIPASS
#endif // GEN_PASS_DECL_CONVERTMESHTOMPIPASS
#ifdef GEN_PASS_DEF_CONVERTMESHTOMPIPASS
namespace impl {

template <typename DerivedT>
class ConvertMeshToMPIPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMeshToMPIPassBase;

  ConvertMeshToMPIPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMeshToMPIPassBase(const ConvertMeshToMPIPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMeshToMPIPassBase& operator=(const ConvertMeshToMPIPassBase &) = delete;
  ConvertMeshToMPIPassBase(ConvertMeshToMPIPassBase &&) = delete;
  ConvertMeshToMPIPassBase& operator=(ConvertMeshToMPIPassBase &&) = delete;
  ~ConvertMeshToMPIPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-mesh-to-mpi");
  }
  ::llvm::StringRef getArgument() const override { return "convert-mesh-to-mpi"; }

  ::llvm::StringRef getDescription() const override { return "Convert Mesh dialect to MPI dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMeshToMPIPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertMeshToMPIPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<mpi::MPIDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<bufferization::BufferizationDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMeshToMPIPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTMESHTOMPIPASS
#endif // GEN_PASS_DEF_CONVERTMESHTOMPIPASS

//===----------------------------------------------------------------------===//
// ConvertNVGPUToNVVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass();
#undef GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
#endif // GEN_PASS_DECL_CONVERTNVGPUTONVVMPASS
#ifdef GEN_PASS_DEF_CONVERTNVGPUTONVVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertNVGPUToNVVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertNVGPUToNVVMPassBase;

  ConvertNVGPUToNVVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertNVGPUToNVVMPassBase(const ConvertNVGPUToNVVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertNVGPUToNVVMPassBase& operator=(const ConvertNVGPUToNVVMPassBase &) = delete;
  ConvertNVGPUToNVVMPassBase(ConvertNVGPUToNVVMPassBase &&) = delete;
  ConvertNVGPUToNVVMPassBase& operator=(ConvertNVGPUToNVVMPassBase &&) = delete;
  ~ConvertNVGPUToNVVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-nvgpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-nvgpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert NVGPU dialect to NVVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertNVGPUToNVVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertNVGPUToNVVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<NVVM::NVVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertNVGPUToNVVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertNVGPUToNVVMPass() {
  return impl::createConvertNVGPUToNVVMPass();
}
#undef GEN_PASS_DEF_CONVERTNVGPUTONVVMPASS
#endif // GEN_PASS_DEF_CONVERTNVGPUTONVVMPASS

//===----------------------------------------------------------------------===//
// ConvertNVVMToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTNVVMTOLLVMPASS
std::unique_ptr<::mlir::Pass> createConvertNVVMToLLVMPass();
#undef GEN_PASS_DECL_CONVERTNVVMTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTNVVMTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTNVVMTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertNVVMToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertNVVMToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertNVVMToLLVMPassBase;

  ConvertNVVMToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertNVVMToLLVMPassBase(const ConvertNVVMToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertNVVMToLLVMPassBase& operator=(const ConvertNVVMToLLVMPassBase &) = delete;
  ConvertNVVMToLLVMPassBase(ConvertNVVMToLLVMPassBase &&) = delete;
  ConvertNVVMToLLVMPassBase& operator=(ConvertNVVMToLLVMPassBase &&) = delete;
  ~ConvertNVVMToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-nvvm-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-nvvm-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert NVVM to PTX with Inline Assembly in LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertNVVMToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertNVVMToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<NVVM::NVVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertNVVMToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertNVVMToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertNVVMToLLVMPass() {
  return impl::createConvertNVVMToLLVMPass();
}
#undef GEN_PASS_DEF_CONVERTNVVMTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTNVVMTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTOPENACCTOSCF
#undef GEN_PASS_DECL_CONVERTOPENACCTOSCF
#endif // GEN_PASS_DECL_CONVERTOPENACCTOSCF
#ifdef GEN_PASS_DEF_CONVERTOPENACCTOSCF
namespace impl {

template <typename DerivedT>
class ConvertOpenACCToSCFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToSCFBase;

  ConvertOpenACCToSCFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToSCFBase(const ConvertOpenACCToSCFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertOpenACCToSCFBase& operator=(const ConvertOpenACCToSCFBase &) = delete;
  ConvertOpenACCToSCFBase(ConvertOpenACCToSCFBase &&) = delete;
  ConvertOpenACCToSCFBase& operator=(ConvertOpenACCToSCFBase &&) = delete;
  ~ConvertOpenACCToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to OpenACC with SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<acc::OpenACCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenACCToSCFBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTOPENACCTOSCF
#endif // GEN_PASS_DEF_CONVERTOPENACCTOSCF

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass();
#undef GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTOPENMPTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTOPENMPTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertOpenMPToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenMPToLLVMPassBase;

  ConvertOpenMPToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenMPToLLVMPassBase(const ConvertOpenMPToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertOpenMPToLLVMPassBase& operator=(const ConvertOpenMPToLLVMPassBase &) = delete;
  ConvertOpenMPToLLVMPassBase(ConvertOpenMPToLLVMPassBase &&) = delete;
  ConvertOpenMPToLLVMPassBase& operator=(ConvertOpenMPToLLVMPassBase &&) = delete;
  ~ConvertOpenMPToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openmp-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openmp-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenMP ops to OpenMP ops with LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenMPToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenMPToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenMPToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertOpenMPToLLVMPass() {
  return impl::createConvertOpenMPToLLVMPass();
}
#undef GEN_PASS_DEF_CONVERTOPENMPTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTOPENMPTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#undef GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#endif // GEN_PASS_DECL_CONVERTPDLTOPDLINTERP
#ifdef GEN_PASS_DEF_CONVERTPDLTOPDLINTERP
namespace impl {

template <typename DerivedT>
class ConvertPDLToPDLInterpBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertPDLToPDLInterpBase;

  ConvertPDLToPDLInterpBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertPDLToPDLInterpBase(const ConvertPDLToPDLInterpBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertPDLToPDLInterpBase& operator=(const ConvertPDLToPDLInterpBase &) = delete;
  ConvertPDLToPDLInterpBase(ConvertPDLToPDLInterpBase &&) = delete;
  ConvertPDLToPDLInterpBase& operator=(ConvertPDLToPDLInterpBase &&) = delete;
  ~ConvertPDLToPDLInterpBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-pdl-to-pdl-interp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-pdl-to-pdl-interp"; }

  ::llvm::StringRef getDescription() const override { return "Convert PDL ops to PDL interpreter ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertPDLToPDLInterp");
  }
  ::llvm::StringRef getName() const override { return "ConvertPDLToPDLInterp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<pdl_interp::PDLInterpDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertPDLToPDLInterpBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTPDLTOPDLINTERP
#endif // GEN_PASS_DEF_CONVERTPDLTOPDLINTERP

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#undef GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#endif // GEN_PASS_DECL_CONVERTPARALLELLOOPTOGPU
#ifdef GEN_PASS_DEF_CONVERTPARALLELLOOPTOGPU
namespace impl {

template <typename DerivedT>
class ConvertParallelLoopToGpuBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertParallelLoopToGpuBase;

  ConvertParallelLoopToGpuBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertParallelLoopToGpuBase(const ConvertParallelLoopToGpuBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertParallelLoopToGpuBase& operator=(const ConvertParallelLoopToGpuBase &) = delete;
  ConvertParallelLoopToGpuBase(ConvertParallelLoopToGpuBase &&) = delete;
  ConvertParallelLoopToGpuBase& operator=(ConvertParallelLoopToGpuBase &&) = delete;
  ~ConvertParallelLoopToGpuBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-parallel-loops-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-parallel-loops-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert mapped scf.parallel ops to gpu launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertParallelLoopToGpu");
  }
  ::llvm::StringRef getName() const override { return "ConvertParallelLoopToGpu"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<gpu::GPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertParallelLoopToGpuBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTPARALLELLOOPTOGPU
#endif // GEN_PASS_DEF_CONVERTPARALLELLOOPTOGPU

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMPPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
struct ConvertSCFToOpenMPPassOptions {
  unsigned numThreads = 0;
};
std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass();
std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(ConvertSCFToOpenMPPassOptions options);
#undef GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
#endif // GEN_PASS_DECL_CONVERTSCFTOOPENMPPASS
#ifdef GEN_PASS_DEF_CONVERTSCFTOOPENMPPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(ConvertSCFToOpenMPPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertSCFToOpenMPPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSCFToOpenMPPassBase;

  ConvertSCFToOpenMPPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSCFToOpenMPPassBase(const ConvertSCFToOpenMPPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertSCFToOpenMPPassBase& operator=(const ConvertSCFToOpenMPPassBase &) = delete;
  ConvertSCFToOpenMPPassBase(ConvertSCFToOpenMPPassBase &&) = delete;
  ConvertSCFToOpenMPPassBase& operator=(ConvertSCFToOpenMPPassBase &&) = delete;
  ~ConvertSCFToOpenMPPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-openmp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-openmp"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF parallel loop to OpenMP parallel + workshare constructs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSCFToOpenMPPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSCFToOpenMPPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<omp::OpenMPDialect>();
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSCFToOpenMPPassBase<DerivedT>)

  ConvertSCFToOpenMPPassBase(ConvertSCFToOpenMPPassOptions options) : ConvertSCFToOpenMPPassBase() {
    numThreads = std::move(options.numThreads);
  }
protected:
  ::mlir::Pass::Option<unsigned> numThreads{*this, "num-threads", ::llvm::cl::desc("Number of threads to use"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(ConvertSCFToOpenMPPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass() {
  return impl::createConvertSCFToOpenMPPass();
}

std::unique_ptr<::mlir::Pass> createConvertSCFToOpenMPPass(ConvertSCFToOpenMPPassOptions options) {
  return impl::createConvertSCFToOpenMPPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTSCFTOOPENMPPASS
#endif // GEN_PASS_DEF_CONVERTSCFTOOPENMPPASS

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
struct ConvertSPIRVToLLVMPassOptions {
  ::mlir::spirv::ClientAPI clientAPI = ::mlir::spirv::ClientAPI::Unknown;
};
std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(ConvertSPIRVToLLVMPassOptions options);
#undef GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTSPIRVTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTSPIRVTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(ConvertSPIRVToLLVMPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertSPIRVToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSPIRVToLLVMPassBase;

  ConvertSPIRVToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSPIRVToLLVMPassBase(const ConvertSPIRVToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertSPIRVToLLVMPassBase& operator=(const ConvertSPIRVToLLVMPassBase &) = delete;
  ConvertSPIRVToLLVMPassBase(ConvertSPIRVToLLVMPassBase &&) = delete;
  ConvertSPIRVToLLVMPassBase& operator=(ConvertSPIRVToLLVMPassBase &&) = delete;
  ~ConvertSPIRVToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-spirv-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-spirv-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert SPIR-V dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSPIRVToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSPIRVToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSPIRVToLLVMPassBase<DerivedT>)

  ConvertSPIRVToLLVMPassBase(ConvertSPIRVToLLVMPassOptions options) : ConvertSPIRVToLLVMPassBase() {
    clientAPI = std::move(options.clientAPI);
  }
protected:
  ::mlir::Pass::Option<::mlir::spirv::ClientAPI> clientAPI{*this, "client-api", ::llvm::cl::desc("Derive StorageClass to address space mapping from the client API"), ::llvm::cl::init(::mlir::spirv::ClientAPI::Unknown), ::llvm::cl::values(
	     clEnumValN(::mlir::spirv::ClientAPI::Unknown, "Unknown", "Unknown (default)"),
	     clEnumValN(::mlir::spirv::ClientAPI::Metal, "Metal", "Metal"),
	     clEnumValN(::mlir::spirv::ClientAPI::OpenCL, "OpenCL", "OpenCL"),
	     clEnumValN(::mlir::spirv::ClientAPI::Vulkan, "Vulkan", "Vulkan"),
	     clEnumValN(::mlir::spirv::ClientAPI::WebGPU, "WebGPU", "WebGPU")
	   )};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(ConvertSPIRVToLLVMPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass() {
  return impl::createConvertSPIRVToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertSPIRVToLLVMPass(ConvertSPIRVToLLVMPassOptions options) {
  return impl::createConvertSPIRVToLLVMPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTSPIRVTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTSPIRVTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#undef GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#endif // GEN_PASS_DECL_CONVERTSHAPECONSTRAINTS
#ifdef GEN_PASS_DEF_CONVERTSHAPECONSTRAINTS
namespace impl {

template <typename DerivedT>
class ConvertShapeConstraintsBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertShapeConstraintsBase;

  ConvertShapeConstraintsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeConstraintsBase(const ConvertShapeConstraintsBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertShapeConstraintsBase& operator=(const ConvertShapeConstraintsBase &) = delete;
  ConvertShapeConstraintsBase(ConvertShapeConstraintsBase &&) = delete;
  ConvertShapeConstraintsBase& operator=(ConvertShapeConstraintsBase &&) = delete;
  ~ConvertShapeConstraintsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Convert shape constraint operations to the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeConstraintsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTSHAPECONSTRAINTS
#endif // GEN_PASS_DEF_CONVERTSHAPECONSTRAINTS

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#undef GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#endif // GEN_PASS_DECL_CONVERTSHAPETOSTANDARD
#ifdef GEN_PASS_DEF_CONVERTSHAPETOSTANDARD
namespace impl {

template <typename DerivedT>
class ConvertShapeToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertShapeToStandardBase;

  ConvertShapeToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeToStandardBase(const ConvertShapeToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertShapeToStandardBase& operator=(const ConvertShapeToStandardBase &) = delete;
  ConvertShapeToStandardBase(ConvertShapeToStandardBase &&) = delete;
  ConvertShapeToStandardBase& operator=(ConvertShapeToStandardBase &&) = delete;
  ~ConvertShapeToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the shape dialect into the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeToStandardBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTSHAPETOSTANDARD
#endif // GEN_PASS_DEF_CONVERTSHAPETOSTANDARD

//===----------------------------------------------------------------------===//
// ConvertTensorToLinalg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTENSORTOLINALG
#undef GEN_PASS_DECL_CONVERTTENSORTOLINALG
#endif // GEN_PASS_DECL_CONVERTTENSORTOLINALG
#ifdef GEN_PASS_DEF_CONVERTTENSORTOLINALG
namespace impl {

template <typename DerivedT>
class ConvertTensorToLinalgBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertTensorToLinalgBase;

  ConvertTensorToLinalgBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToLinalgBase(const ConvertTensorToLinalgBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertTensorToLinalgBase& operator=(const ConvertTensorToLinalgBase &) = delete;
  ConvertTensorToLinalgBase(ConvertTensorToLinalgBase &&) = delete;
  ConvertTensorToLinalgBase& operator=(ConvertTensorToLinalgBase &&) = delete;
  ~ConvertTensorToLinalgBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert some Tensor dialect ops to Linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToLinalgBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTENSORTOLINALG
#endif // GEN_PASS_DEF_CONVERTTENSORTOLINALG

//===----------------------------------------------------------------------===//
// ConvertTensorToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTENSORTOSPIRV
struct ConvertTensorToSPIRVOptions {
  bool emulateLT32BitScalarTypes = true;
};
#undef GEN_PASS_DECL_CONVERTTENSORTOSPIRV
#endif // GEN_PASS_DECL_CONVERTTENSORTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTTENSORTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertTensorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertTensorToSPIRVBase;

  ConvertTensorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToSPIRVBase(const ConvertTensorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertTensorToSPIRVBase& operator=(const ConvertTensorToSPIRVBase &) = delete;
  ConvertTensorToSPIRVBase(ConvertTensorToSPIRVBase &&) = delete;
  ConvertTensorToSPIRVBase& operator=(ConvertTensorToSPIRVBase &&) = delete;
  ~ConvertTensorToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Tensor dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToSPIRVBase<DerivedT>)

  ConvertTensorToSPIRVBase(ConvertTensorToSPIRVOptions options) : ConvertTensorToSPIRVBase() {
    emulateLT32BitScalarTypes = std::move(options.emulateLT32BitScalarTypes);
  }
protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTENSORTOSPIRV
#endif // GEN_PASS_DEF_CONVERTTENSORTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTOLLVMPASS
struct ConvertToLLVMPassOptions {
  ::llvm::SmallVector<std::string> filterDialects;
  bool useDynamic = false;
};
#undef GEN_PASS_DECL_CONVERTTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTTOLLVMPASS
namespace impl {

template <typename DerivedT>
class ConvertToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertToLLVMPassBase;

  ConvertToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertToLLVMPassBase(const ConvertToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertToLLVMPassBase& operator=(const ConvertToLLVMPassBase &) = delete;
  ConvertToLLVMPassBase(ConvertToLLVMPassBase &&) = delete;
  ConvertToLLVMPassBase& operator=(ConvertToLLVMPassBase &&) = delete;
  ~ConvertToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert to LLVM via dialect interfaces found in the input IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertToLLVMPassBase<DerivedT>)

  ConvertToLLVMPassBase(ConvertToLLVMPassOptions options) : ConvertToLLVMPassBase() {
    filterDialects = std::move(options.filterDialects);
    useDynamic = std::move(options.useDynamic);
  }
protected:
  ::mlir::Pass::ListOption<std::string> filterDialects{*this, "filter-dialects", ::llvm::cl::desc("Test conversion patterns of only the specified dialects")};
  ::mlir::Pass::Option<bool> useDynamic{*this, "dynamic", ::llvm::cl::desc("Use op conversion attributes to configure the conversion"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertVectorToArmSME
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOARMSME
#undef GEN_PASS_DECL_CONVERTVECTORTOARMSME
#endif // GEN_PASS_DECL_CONVERTVECTORTOARMSME
#ifdef GEN_PASS_DEF_CONVERTVECTORTOARMSME
namespace impl {

template <typename DerivedT>
class ConvertVectorToArmSMEBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToArmSMEBase;

  ConvertVectorToArmSMEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToArmSMEBase(const ConvertVectorToArmSMEBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToArmSMEBase& operator=(const ConvertVectorToArmSMEBase &) = delete;
  ConvertVectorToArmSMEBase(ConvertVectorToArmSMEBase &&) = delete;
  ConvertVectorToArmSMEBase& operator=(ConvertVectorToArmSMEBase &&) = delete;
  ~ConvertVectorToArmSMEBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-arm-sme");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-arm-sme"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the ArmSME dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToArmSME");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToArmSME"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<arm_sve::ArmSVEDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToArmSMEBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOARMSME
#endif // GEN_PASS_DEF_CONVERTVECTORTOARMSME

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOGPU
struct ConvertVectorToGPUOptions {
  bool useNvGpu = false;
};
#undef GEN_PASS_DECL_CONVERTVECTORTOGPU
#endif // GEN_PASS_DECL_CONVERTVECTORTOGPU
#ifdef GEN_PASS_DEF_CONVERTVECTORTOGPU
namespace impl {

template <typename DerivedT>
class ConvertVectorToGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToGPUBase;

  ConvertVectorToGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToGPUBase(const ConvertVectorToGPUBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToGPUBase& operator=(const ConvertVectorToGPUBase &) = delete;
  ConvertVectorToGPUBase(ConvertVectorToGPUBase &&) = delete;
  ConvertVectorToGPUBase& operator=(ConvertVectorToGPUBase &&) = delete;
  ~ConvertVectorToGPUBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the GPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<gpu::GPUDialect>();
    registry.insert<affine::AffineDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<nvgpu::NVGPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToGPUBase<DerivedT>)

  ConvertVectorToGPUBase(ConvertVectorToGPUOptions options) : ConvertVectorToGPUBase() {
    useNvGpu = std::move(options.useNvGpu);
  }
protected:
  ::mlir::Pass::Option<bool> useNvGpu{*this, "use-nvgpu", ::llvm::cl::desc("convert to NvGPU ops instead of GPU dialect ops"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOGPU
#endif // GEN_PASS_DEF_CONVERTVECTORTOGPU

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
struct ConvertVectorToLLVMPassOptions {
  bool reassociateFPReductions = false;
  bool force32BitVectorIndices = true;
  bool amx = false;
  bool armNeon = false;
  bool armSVE = false;
  bool x86Vector = false;
  vector::VectorTransformsOptions vectorTransformsOptions = vector::VectorTransformsOptions();
};
std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass();
std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(ConvertVectorToLLVMPassOptions options);
#undef GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
#endif // GEN_PASS_DECL_CONVERTVECTORTOLLVMPASS
#ifdef GEN_PASS_DEF_CONVERTVECTORTOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(ConvertVectorToLLVMPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertVectorToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToLLVMPassBase;

  ConvertVectorToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToLLVMPassBase(const ConvertVectorToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToLLVMPassBase& operator=(const ConvertVectorToLLVMPassBase &) = delete;
  ConvertVectorToLLVMPassBase(ConvertVectorToLLVMPassBase &&) = delete;
  ConvertVectorToLLVMPassBase& operator=(ConvertVectorToLLVMPassBase &&) = delete;
  ~ConvertVectorToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToLLVMPassBase<DerivedT>)

  ConvertVectorToLLVMPassBase(ConvertVectorToLLVMPassOptions options) : ConvertVectorToLLVMPassBase() {
    reassociateFPReductions = std::move(options.reassociateFPReductions);
    force32BitVectorIndices = std::move(options.force32BitVectorIndices);
    amx = std::move(options.amx);
    armNeon = std::move(options.armNeon);
    armSVE = std::move(options.armSVE);
    x86Vector = std::move(options.x86Vector);
    vectorTransformsOptions = std::move(options.vectorTransformsOptions);
  }
protected:
  ::mlir::Pass::Option<bool> reassociateFPReductions{*this, "reassociate-fp-reductions", ::llvm::cl::desc("Allows llvm to reassociate floating-point reductions for speed"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> force32BitVectorIndices{*this, "force-32bit-vector-indices", ::llvm::cl::desc("Allows compiler to assume vector indices fit in 32-bit if that yields faster code"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> amx{*this, "enable-amx", ::llvm::cl::desc("Enables the use of AMX dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armNeon{*this, "enable-arm-neon", ::llvm::cl::desc("Enables the use of ArmNeon dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armSVE{*this, "enable-arm-sve", ::llvm::cl::desc("Enables the use of ArmSVE dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> x86Vector{*this, "enable-x86vector", ::llvm::cl::desc("Enables the use of X86Vector dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<vector::VectorTransformsOptions> vectorTransformsOptions{*this, "vector-transform-options", ::llvm::cl::desc("Options to lower some operations like contractions and transposes."), ::llvm::cl::init(vector::VectorTransformsOptions())};
private:

  friend std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(ConvertVectorToLLVMPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass() {
  return impl::createConvertVectorToLLVMPass();
}

std::unique_ptr<::mlir::Pass> createConvertVectorToLLVMPass(ConvertVectorToLLVMPassOptions options) {
  return impl::createConvertVectorToLLVMPass(std::move(options));
}
#undef GEN_PASS_DEF_CONVERTVECTORTOLLVMPASS
#endif // GEN_PASS_DEF_CONVERTVECTORTOLLVMPASS

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOSCF
struct ConvertVectorToSCFOptions {
  bool fullUnroll = false;
  unsigned targetRank = 1;
  bool lowerTensors = false;
  bool lowerScalable = false;
};
#undef GEN_PASS_DECL_CONVERTVECTORTOSCF
#endif // GEN_PASS_DECL_CONVERTVECTORTOSCF
#ifdef GEN_PASS_DEF_CONVERTVECTORTOSCF
namespace impl {

template <typename DerivedT>
class ConvertVectorToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSCFBase;

  ConvertVectorToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSCFBase(const ConvertVectorToSCFBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToSCFBase& operator=(const ConvertVectorToSCFBase &) = delete;
  ConvertVectorToSCFBase(ConvertVectorToSCFBase &&) = delete;
  ConvertVectorToSCFBase& operator=(ConvertVectorToSCFBase &&) = delete;
  ~ConvertVectorToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSCFBase<DerivedT>)

  ConvertVectorToSCFBase(ConvertVectorToSCFOptions options) : ConvertVectorToSCFBase() {
    fullUnroll = std::move(options.fullUnroll);
    targetRank = std::move(options.targetRank);
    lowerTensors = std::move(options.lowerTensors);
    lowerScalable = std::move(options.lowerScalable);
  }
protected:
  ::mlir::Pass::Option<bool> fullUnroll{*this, "full-unroll", ::llvm::cl::desc("Perform full unrolling when converting vector transfers to SCF"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> targetRank{*this, "target-rank", ::llvm::cl::desc("Target vector rank to which transfer ops should be lowered"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> lowerTensors{*this, "lower-tensors", ::llvm::cl::desc("Lower transfer ops that operate on tensors"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> lowerScalable{*this, "lower-scalable", ::llvm::cl::desc("Add scalable vector specific lowerings (that introduce loops)"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOSCF
#endif // GEN_PASS_DEF_CONVERTVECTORTOSCF

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#undef GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#endif // GEN_PASS_DECL_CONVERTVECTORTOSPIRV
#ifdef GEN_PASS_DEF_CONVERTVECTORTOSPIRV
namespace impl {

template <typename DerivedT>
class ConvertVectorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSPIRVBase;

  ConvertVectorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSPIRVBase(const ConvertVectorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToSPIRVBase& operator=(const ConvertVectorToSPIRVBase &) = delete;
  ConvertVectorToSPIRVBase(ConvertVectorToSPIRVBase &&) = delete;
  ConvertVectorToSPIRVBase& operator=(ConvertVectorToSPIRVBase &&) = delete;
  ~ConvertVectorToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Vector dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
    registry.insert<ub::UBDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSPIRVBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOSPIRV
#endif // GEN_PASS_DEF_CONVERTVECTORTOSPIRV

//===----------------------------------------------------------------------===//
// ConvertVectorToXeGPU
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTVECTORTOXEGPU
#undef GEN_PASS_DECL_CONVERTVECTORTOXEGPU
#endif // GEN_PASS_DECL_CONVERTVECTORTOXEGPU
#ifdef GEN_PASS_DEF_CONVERTVECTORTOXEGPU
namespace impl {

template <typename DerivedT>
class ConvertVectorToXeGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToXeGPUBase;

  ConvertVectorToXeGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToXeGPUBase(const ConvertVectorToXeGPUBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToXeGPUBase& operator=(const ConvertVectorToXeGPUBase &) = delete;
  ConvertVectorToXeGPUBase(ConvertVectorToXeGPUBase &&) = delete;
  ConvertVectorToXeGPUBase& operator=(ConvertVectorToXeGPUBase &&) = delete;
  ~ConvertVectorToXeGPUBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-xegpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-xegpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the XeGPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToXeGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToXeGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<arith::ArithDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<xegpu::XeGPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToXeGPUBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTVECTORTOXEGPU
#endif // GEN_PASS_DEF_CONVERTVECTORTOXEGPU

//===----------------------------------------------------------------------===//
// FinalizeMemRefToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
struct FinalizeMemRefToLLVMConversionPassOptions {
  bool useAlignedAlloc = false;
  unsigned indexBitwidth = 0;
  bool useGenericFunctions = false;
};
std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(FinalizeMemRefToLLVMConversionPassOptions options);
#undef GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_FINALIZEMEMREFTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(FinalizeMemRefToLLVMConversionPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class FinalizeMemRefToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FinalizeMemRefToLLVMConversionPassBase;

  FinalizeMemRefToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizeMemRefToLLVMConversionPassBase(const FinalizeMemRefToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FinalizeMemRefToLLVMConversionPassBase& operator=(const FinalizeMemRefToLLVMConversionPassBase &) = delete;
  FinalizeMemRefToLLVMConversionPassBase(FinalizeMemRefToLLVMConversionPassBase &&) = delete;
  FinalizeMemRefToLLVMConversionPassBase& operator=(FinalizeMemRefToLLVMConversionPassBase &&) = delete;
  ~FinalizeMemRefToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalize-memref-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "finalize-memref-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Finalize MemRef dialect to LLVM dialect conversion"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizeMemRefToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "FinalizeMemRefToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalizeMemRefToLLVMConversionPassBase<DerivedT>)

  FinalizeMemRefToLLVMConversionPassBase(FinalizeMemRefToLLVMConversionPassOptions options) : FinalizeMemRefToLLVMConversionPassBase() {
    useAlignedAlloc = std::move(options.useAlignedAlloc);
    indexBitwidth = std::move(options.indexBitwidth);
    useGenericFunctions = std::move(options.useGenericFunctions);
  }
protected:
  ::mlir::Pass::Option<bool> useAlignedAlloc{*this, "use-aligned-alloc", ::llvm::cl::desc("Use aligned_alloc in place of malloc for heap allocations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useGenericFunctions{*this, "use-generic-functions", ::llvm::cl::desc("Use generic allocation and deallocation functions instead of the classic 'malloc', 'aligned_alloc' and 'free' functions"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(FinalizeMemRefToLLVMConversionPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass() {
  return impl::createFinalizeMemRefToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createFinalizeMemRefToLLVMConversionPass(FinalizeMemRefToLLVMConversionPassOptions options) {
  return impl::createFinalizeMemRefToLLVMConversionPass(std::move(options));
}
#undef GEN_PASS_DEF_FINALIZEMEMREFTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_FINALIZEMEMREFTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
struct GpuToLLVMConversionPassOptions {
  bool hostBarePtrCallConv = false;
  bool kernelBarePtrCallConv = false;
  bool kernelIntersperseSizeCallConv = false;
};
std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(GpuToLLVMConversionPassOptions options);
#undef GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_GPUTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_GPUTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(GpuToLLVMConversionPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class GpuToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuToLLVMConversionPassBase;

  GpuToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  GpuToLLVMConversionPassBase& operator=(const GpuToLLVMConversionPassBase &) = delete;
  GpuToLLVMConversionPassBase(GpuToLLVMConversionPassBase &&) = delete;
  GpuToLLVMConversionPassBase& operator=(GpuToLLVMConversionPassBase &&) = delete;
  ~GpuToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to LLVM dialect with GPU runtime calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuToLLVMConversionPassBase<DerivedT>)

  GpuToLLVMConversionPassBase(GpuToLLVMConversionPassOptions options) : GpuToLLVMConversionPassBase() {
    hostBarePtrCallConv = std::move(options.hostBarePtrCallConv);
    kernelBarePtrCallConv = std::move(options.kernelBarePtrCallConv);
    kernelIntersperseSizeCallConv = std::move(options.kernelIntersperseSizeCallConv);
  }
protected:
  ::mlir::Pass::Option<bool> hostBarePtrCallConv{*this, "use-bare-pointers-for-host", ::llvm::cl::desc("Use bare pointers to pass memref arguments to host functions. All memrefs must have static shape."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> kernelBarePtrCallConv{*this, "use-bare-pointers-for-kernels", ::llvm::cl::desc("Use bare pointers to pass memref arguments to kernels. The kernel must use the same setting for this option."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> kernelIntersperseSizeCallConv{*this, "intersperse-sizes-for-kernels", ::llvm::cl::desc("Inserts a size_t argument following each memref argument, containing the static size in bytes of the buffer. Incompatible arguments are rejected. This is intended for use by the Vulkan runtime with the kernel bare pointer calling convention, to enable dynamic binding of buffers as arguments without static type info."), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(GpuToLLVMConversionPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass() {
  return impl::createGpuToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createGpuToLLVMConversionPass(GpuToLLVMConversionPassOptions options) {
  return impl::createGpuToLLVMConversionPass(std::move(options));
}
#undef GEN_PASS_DEF_GPUTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_GPUTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// LiftControlFlowToSCFPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LIFTCONTROLFLOWTOSCFPASS
std::unique_ptr<::mlir::Pass> createLiftControlFlowToSCFPass();
#undef GEN_PASS_DECL_LIFTCONTROLFLOWTOSCFPASS
#endif // GEN_PASS_DECL_LIFTCONTROLFLOWTOSCFPASS
#ifdef GEN_PASS_DEF_LIFTCONTROLFLOWTOSCFPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLiftControlFlowToSCFPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LiftControlFlowToSCFPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LiftControlFlowToSCFPassBase;

  LiftControlFlowToSCFPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LiftControlFlowToSCFPassBase(const LiftControlFlowToSCFPassBase &other) : ::mlir::OperationPass<>(other) {}
  LiftControlFlowToSCFPassBase& operator=(const LiftControlFlowToSCFPassBase &) = delete;
  LiftControlFlowToSCFPassBase(LiftControlFlowToSCFPassBase &&) = delete;
  LiftControlFlowToSCFPassBase& operator=(LiftControlFlowToSCFPassBase &&) = delete;
  ~LiftControlFlowToSCFPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lift-cf-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "lift-cf-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lift ControlFlow dialect to SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LiftControlFlowToSCFPass");
  }
  ::llvm::StringRef getName() const override { return "LiftControlFlowToSCFPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<arith::ArithDialect>();
    registry.insert<ub::UBDialect>();
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LiftControlFlowToSCFPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLiftControlFlowToSCFPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLiftControlFlowToSCFPass() {
  return impl::createLiftControlFlowToSCFPass();
}
#undef GEN_PASS_DEF_LIFTCONTROLFLOWTOSCFPASS
#endif // GEN_PASS_DEF_LIFTCONTROLFLOWTOSCFPASS

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVMPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass();
#undef GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
#endif // GEN_PASS_DECL_LOWERHOSTCODETOLLVMPASS
#ifdef GEN_PASS_DEF_LOWERHOSTCODETOLLVMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LowerHostCodeToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LowerHostCodeToLLVMPassBase;

  LowerHostCodeToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerHostCodeToLLVMPassBase(const LowerHostCodeToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LowerHostCodeToLLVMPassBase& operator=(const LowerHostCodeToLLVMPassBase &) = delete;
  LowerHostCodeToLLVMPassBase(LowerHostCodeToLLVMPassBase &&) = delete;
  LowerHostCodeToLLVMPassBase& operator=(LowerHostCodeToLLVMPassBase &&) = delete;
  ~LowerHostCodeToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "lower-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lowers the host module code and `gpu.launch_func` to LLVM"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerHostCodeToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "LowerHostCodeToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerHostCodeToLLVMPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLowerHostCodeToLLVMPass() {
  return impl::createLowerHostCodeToLLVMPass();
}
#undef GEN_PASS_DEF_LOWERHOSTCODETOLLVMPASS
#endif // GEN_PASS_DEF_LOWERHOSTCODETOLLVMPASS

//===----------------------------------------------------------------------===//
// MapMemRefStorageClass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
struct MapMemRefStorageClassOptions {
  std::string clientAPI = "vulkan";
};
#undef GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
#endif // GEN_PASS_DECL_MAPMEMREFSTORAGECLASS
#ifdef GEN_PASS_DEF_MAPMEMREFSTORAGECLASS
namespace impl {

template <typename DerivedT>
class MapMemRefStorageClassBase : public ::mlir::OperationPass<> {
public:
  using Base = MapMemRefStorageClassBase;

  MapMemRefStorageClassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  MapMemRefStorageClassBase(const MapMemRefStorageClassBase &other) : ::mlir::OperationPass<>(other) {}
  MapMemRefStorageClassBase& operator=(const MapMemRefStorageClassBase &) = delete;
  MapMemRefStorageClassBase(MapMemRefStorageClassBase &&) = delete;
  MapMemRefStorageClassBase& operator=(MapMemRefStorageClassBase &&) = delete;
  ~MapMemRefStorageClassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("map-memref-spirv-storage-class");
  }
  ::llvm::StringRef getArgument() const override { return "map-memref-spirv-storage-class"; }

  ::llvm::StringRef getDescription() const override { return "Map numeric MemRef memory spaces to SPIR-V storage classes"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MapMemRefStorageClass");
  }
  ::llvm::StringRef getName() const override { return "MapMemRefStorageClass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MapMemRefStorageClassBase<DerivedT>)

  MapMemRefStorageClassBase(MapMemRefStorageClassOptions options) : MapMemRefStorageClassBase() {
    clientAPI = std::move(options.clientAPI);
  }
protected:
  ::mlir::Pass::Option<std::string> clientAPI{*this, "client-api", ::llvm::cl::desc("The client API to use for populating mappings"), ::llvm::cl::init("vulkan")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_MAPMEMREFSTORAGECLASS
#endif // GEN_PASS_DEF_MAPMEMREFSTORAGECLASS

//===----------------------------------------------------------------------===//
// ReconcileUnrealizedCasts
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#undef GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#endif // GEN_PASS_DECL_RECONCILEUNREALIZEDCASTS
#ifdef GEN_PASS_DEF_RECONCILEUNREALIZEDCASTS
namespace impl {

template <typename DerivedT>
class ReconcileUnrealizedCastsBase : public ::mlir::OperationPass<> {
public:
  using Base = ReconcileUnrealizedCastsBase;

  ReconcileUnrealizedCastsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ReconcileUnrealizedCastsBase(const ReconcileUnrealizedCastsBase &other) : ::mlir::OperationPass<>(other) {}
  ReconcileUnrealizedCastsBase& operator=(const ReconcileUnrealizedCastsBase &) = delete;
  ReconcileUnrealizedCastsBase(ReconcileUnrealizedCastsBase &&) = delete;
  ReconcileUnrealizedCastsBase& operator=(ReconcileUnrealizedCastsBase &&) = delete;
  ~ReconcileUnrealizedCastsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("reconcile-unrealized-casts");
  }
  ::llvm::StringRef getArgument() const override { return "reconcile-unrealized-casts"; }

  ::llvm::StringRef getDescription() const override { return "Simplify and eliminate unrealized conversion casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReconcileUnrealizedCasts");
  }
  ::llvm::StringRef getName() const override { return "ReconcileUnrealizedCasts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ReconcileUnrealizedCastsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_RECONCILEUNREALIZEDCASTS
#endif // GEN_PASS_DEF_RECONCILEUNREALIZEDCASTS

//===----------------------------------------------------------------------===//
// SCFToControlFlow
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFTOCONTROLFLOW
#undef GEN_PASS_DECL_SCFTOCONTROLFLOW
#endif // GEN_PASS_DECL_SCFTOCONTROLFLOW
#ifdef GEN_PASS_DEF_SCFTOCONTROLFLOW
namespace impl {

template <typename DerivedT>
class SCFToControlFlowBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToControlFlowBase;

  SCFToControlFlowBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToControlFlowBase(const SCFToControlFlowBase &other) : ::mlir::OperationPass<>(other) {}
  SCFToControlFlowBase& operator=(const SCFToControlFlowBase &) = delete;
  SCFToControlFlowBase(SCFToControlFlowBase &&) = delete;
  SCFToControlFlowBase& operator=(SCFToControlFlowBase &&) = delete;
  ~SCFToControlFlowBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-cf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-cf"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to ControlFlow dialect, replacing structured control flow with a CFG"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToControlFlow");
  }
  ::llvm::StringRef getName() const override { return "SCFToControlFlow"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<cf::ControlFlowDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToControlFlowBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFTOCONTROLFLOW
#endif // GEN_PASS_DEF_SCFTOCONTROLFLOW

//===----------------------------------------------------------------------===//
// SCFToEmitC
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFTOEMITC
std::unique_ptr<::mlir::Pass> createSCFToEmitC();
#undef GEN_PASS_DECL_SCFTOEMITC
#endif // GEN_PASS_DECL_SCFTOEMITC
#ifdef GEN_PASS_DEF_SCFTOEMITC

namespace impl {
  std::unique_ptr<::mlir::Pass> createSCFToEmitC();
} // namespace impl
namespace impl {

template <typename DerivedT>
class SCFToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToEmitCBase;

  SCFToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToEmitCBase(const SCFToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  SCFToEmitCBase& operator=(const SCFToEmitCBase &) = delete;
  SCFToEmitCBase(SCFToEmitCBase &&) = delete;
  SCFToEmitCBase& operator=(SCFToEmitCBase &&) = delete;
  ~SCFToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to EmitC dialect, maintaining structured control flow"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToEmitC");
  }
  ::llvm::StringRef getName() const override { return "SCFToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToEmitCBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createSCFToEmitC() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createSCFToEmitC() {
  return impl::createSCFToEmitC();
}
#undef GEN_PASS_DEF_SCFTOEMITC
#endif // GEN_PASS_DEF_SCFTOEMITC

//===----------------------------------------------------------------------===//
// SCFToSPIRV
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFTOSPIRV
std::unique_ptr<::mlir::Pass> createSCFToSPIRV();
#undef GEN_PASS_DECL_SCFTOSPIRV
#endif // GEN_PASS_DECL_SCFTOSPIRV
#ifdef GEN_PASS_DEF_SCFTOSPIRV

namespace impl {
  std::unique_ptr<::mlir::Pass> createSCFToSPIRV();
} // namespace impl
namespace impl {

template <typename DerivedT>
class SCFToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToSPIRVBase;

  SCFToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToSPIRVBase(const SCFToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  SCFToSPIRVBase& operator=(const SCFToSPIRVBase &) = delete;
  SCFToSPIRVBase(SCFToSPIRVBase &&) = delete;
  SCFToSPIRVBase& operator=(SCFToSPIRVBase &&) = delete;
  ~SCFToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to SPIR-V dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "SCFToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToSPIRVBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createSCFToSPIRV() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createSCFToSPIRV() {
  return impl::createSCFToSPIRV();
}
#undef GEN_PASS_DEF_SCFTOSPIRV
#endif // GEN_PASS_DEF_SCFTOSPIRV

//===----------------------------------------------------------------------===//
// SetLLVMModuleDataLayoutPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SETLLVMMODULEDATALAYOUTPASS
struct SetLLVMModuleDataLayoutPassOptions {
  std::string dataLayout = "";
};
std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass();
std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass(SetLLVMModuleDataLayoutPassOptions options);
#undef GEN_PASS_DECL_SETLLVMMODULEDATALAYOUTPASS
#endif // GEN_PASS_DECL_SETLLVMMODULEDATALAYOUTPASS
#ifdef GEN_PASS_DEF_SETLLVMMODULEDATALAYOUTPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass(SetLLVMModuleDataLayoutPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class SetLLVMModuleDataLayoutPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SetLLVMModuleDataLayoutPassBase;

  SetLLVMModuleDataLayoutPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SetLLVMModuleDataLayoutPassBase(const SetLLVMModuleDataLayoutPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  SetLLVMModuleDataLayoutPassBase& operator=(const SetLLVMModuleDataLayoutPassBase &) = delete;
  SetLLVMModuleDataLayoutPassBase(SetLLVMModuleDataLayoutPassBase &&) = delete;
  SetLLVMModuleDataLayoutPassBase& operator=(SetLLVMModuleDataLayoutPassBase &&) = delete;
  ~SetLLVMModuleDataLayoutPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("set-llvm-module-datalayout");
  }
  ::llvm::StringRef getArgument() const override { return "set-llvm-module-datalayout"; }

  ::llvm::StringRef getDescription() const override { return "Attach a datalayout string as a module attribute"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SetLLVMModuleDataLayoutPass");
  }
  ::llvm::StringRef getName() const override { return "SetLLVMModuleDataLayoutPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SetLLVMModuleDataLayoutPassBase<DerivedT>)

  SetLLVMModuleDataLayoutPassBase(SetLLVMModuleDataLayoutPassOptions options) : SetLLVMModuleDataLayoutPassBase() {
    dataLayout = std::move(options.dataLayout);
  }
protected:
  ::mlir::Pass::Option<std::string> dataLayout{*this, "data-layout", ::llvm::cl::desc("String description (LLVM format) of the data layout that is expected on the produced module"), ::llvm::cl::init("")};
private:

  friend std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass(SetLLVMModuleDataLayoutPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass() {
  return impl::createSetLLVMModuleDataLayoutPass();
}

std::unique_ptr<::mlir::Pass> createSetLLVMModuleDataLayoutPass(SetLLVMModuleDataLayoutPassOptions options) {
  return impl::createSetLLVMModuleDataLayoutPass(std::move(options));
}
#undef GEN_PASS_DEF_SETLLVMMODULEDATALAYOUTPASS
#endif // GEN_PASS_DEF_SETLLVMMODULEDATALAYOUTPASS

//===----------------------------------------------------------------------===//
// TosaToArith
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOARITH
struct TosaToArithOptions {
  bool includeApplyRescale = false;
  bool use32Bit = false;
};
#undef GEN_PASS_DECL_TOSATOARITH
#endif // GEN_PASS_DECL_TOSATOARITH
#ifdef GEN_PASS_DEF_TOSATOARITH
namespace impl {

template <typename DerivedT>
class TosaToArithBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToArithBase;

  TosaToArithBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToArithBase(const TosaToArithBase &other) : ::mlir::OperationPass<>(other) {}
  TosaToArithBase& operator=(const TosaToArithBase &) = delete;
  TosaToArithBase(TosaToArithBase &&) = delete;
  TosaToArithBase& operator=(TosaToArithBase &&) = delete;
  ~TosaToArithBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-arith");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-arith"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Arith dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToArith");
  }
  ::llvm::StringRef getName() const override { return "TosaToArith"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToArithBase<DerivedT>)

  TosaToArithBase(TosaToArithOptions options) : TosaToArithBase() {
    includeApplyRescale = std::move(options.includeApplyRescale);
    use32Bit = std::move(options.use32Bit);
  }
protected:
  ::mlir::Pass::Option<bool> includeApplyRescale{*this, "include-apply-rescale", ::llvm::cl::desc("Whether to include the lowering for tosa.apply_rescale to arith"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> use32Bit{*this, "use-32-bit", ::llvm::cl::desc("Whether to prioritze lowering to 32-bit operations"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOARITH
#endif // GEN_PASS_DEF_TOSATOARITH

//===----------------------------------------------------------------------===//
// TosaToLinalg
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOLINALG
struct TosaToLinalgOptions {
  bool disableTosaDecompositions = false;
  bool aggressiveReduceConstant = false;
};
#undef GEN_PASS_DECL_TOSATOLINALG
#endif // GEN_PASS_DECL_TOSATOLINALG
#ifdef GEN_PASS_DEF_TOSATOLINALG
namespace impl {

template <typename DerivedT>
class TosaToLinalgBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgBase;

  TosaToLinalgBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgBase(const TosaToLinalgBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  TosaToLinalgBase& operator=(const TosaToLinalgBase &) = delete;
  TosaToLinalgBase(TosaToLinalgBase &&) = delete;
  TosaToLinalgBase& operator=(TosaToLinalgBase &&) = delete;
  ~TosaToLinalgBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalg");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgBase<DerivedT>)

  TosaToLinalgBase(TosaToLinalgOptions options) : TosaToLinalgBase() {
    disableTosaDecompositions = std::move(options.disableTosaDecompositions);
    aggressiveReduceConstant = std::move(options.aggressiveReduceConstant);
  }
protected:
  ::mlir::Pass::Option<bool> disableTosaDecompositions{*this, "disable-tosa-decompositions", ::llvm::cl::desc("Disable tosa decompositions pass"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> aggressiveReduceConstant{*this, "aggressive-reduce-constant", ::llvm::cl::desc("Always perform the reduce constant optimization"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOLINALG
#endif // GEN_PASS_DEF_TOSATOLINALG

//===----------------------------------------------------------------------===//
// TosaToLinalgNamed
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOLINALGNAMED
struct TosaToLinalgNamedOptions {
  bool preferConv2DKernelLayoutHWCF = false;
};
#undef GEN_PASS_DECL_TOSATOLINALGNAMED
#endif // GEN_PASS_DECL_TOSATOLINALGNAMED
#ifdef GEN_PASS_DEF_TOSATOLINALGNAMED
namespace impl {

template <typename DerivedT>
class TosaToLinalgNamedBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgNamedBase;

  TosaToLinalgNamedBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgNamedBase(const TosaToLinalgNamedBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  TosaToLinalgNamedBase& operator=(const TosaToLinalgNamedBase &) = delete;
  TosaToLinalgNamedBase(TosaToLinalgNamedBase &&) = delete;
  TosaToLinalgNamedBase& operator=(TosaToLinalgNamedBase &&) = delete;
  ~TosaToLinalgNamedBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg-named");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg-named"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg named operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalgNamed");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalgNamed"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgNamedBase<DerivedT>)

  TosaToLinalgNamedBase(TosaToLinalgNamedOptions options) : TosaToLinalgNamedBase() {
    preferConv2DKernelLayoutHWCF = std::move(options.preferConv2DKernelLayoutHWCF);
  }
protected:
  ::mlir::Pass::Option<bool> preferConv2DKernelLayoutHWCF{*this, "prefer-conv2d-kernel-layout-hwcf", ::llvm::cl::desc("Prefer generating linalg.conv_2d_nhwc_hwcf over linalg.conv_2d_nhwc_fhwc"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOLINALGNAMED
#endif // GEN_PASS_DEF_TOSATOLINALGNAMED

//===----------------------------------------------------------------------===//
// TosaToMLProgram
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOMLPROGRAM
std::unique_ptr<::mlir::Pass> createTosaToMLProgram();
#undef GEN_PASS_DECL_TOSATOMLPROGRAM
#endif // GEN_PASS_DECL_TOSATOMLPROGRAM
#ifdef GEN_PASS_DEF_TOSATOMLPROGRAM

namespace impl {
  std::unique_ptr<::mlir::Pass> createTosaToMLProgram();
} // namespace impl
namespace impl {

template <typename DerivedT>
class TosaToMLProgramBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TosaToMLProgramBase;

  TosaToMLProgramBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToMLProgramBase(const TosaToMLProgramBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TosaToMLProgramBase& operator=(const TosaToMLProgramBase &) = delete;
  TosaToMLProgramBase(TosaToMLProgramBase &&) = delete;
  TosaToMLProgramBase& operator=(TosaToMLProgramBase &&) = delete;
  ~TosaToMLProgramBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-mlprogram");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-mlprogram"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the MLProgram dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToMLProgram");
  }
  ::llvm::StringRef getName() const override { return "TosaToMLProgram"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<ml_program::MLProgramDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToMLProgramBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createTosaToMLProgram() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createTosaToMLProgram() {
  return impl::createTosaToMLProgram();
}
#undef GEN_PASS_DEF_TOSATOMLPROGRAM
#endif // GEN_PASS_DEF_TOSATOMLPROGRAM

//===----------------------------------------------------------------------===//
// TosaToSCF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOSCF
#undef GEN_PASS_DECL_TOSATOSCF
#endif // GEN_PASS_DECL_TOSATOSCF
#ifdef GEN_PASS_DEF_TOSATOSCF
namespace impl {

template <typename DerivedT>
class TosaToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToSCFBase;

  TosaToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToSCFBase(const TosaToSCFBase &other) : ::mlir::OperationPass<>(other) {}
  TosaToSCFBase& operator=(const TosaToSCFBase &) = delete;
  TosaToSCFBase(TosaToSCFBase &&) = delete;
  TosaToSCFBase& operator=(TosaToSCFBase &&) = delete;
  ~TosaToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToSCF");
  }
  ::llvm::StringRef getName() const override { return "TosaToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tensor::TensorDialect, scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToSCFBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOSCF
#endif // GEN_PASS_DEF_TOSATOSCF

//===----------------------------------------------------------------------===//
// TosaToTensor
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOSATOTENSOR
#undef GEN_PASS_DECL_TOSATOTENSOR
#endif // GEN_PASS_DECL_TOSATOTENSOR
#ifdef GEN_PASS_DEF_TOSATOTENSOR
namespace impl {

template <typename DerivedT>
class TosaToTensorBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToTensorBase;

  TosaToTensorBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToTensorBase(const TosaToTensorBase &other) : ::mlir::OperationPass<>(other) {}
  TosaToTensorBase& operator=(const TosaToTensorBase &) = delete;
  TosaToTensorBase(TosaToTensorBase &&) = delete;
  TosaToTensorBase& operator=(TosaToTensorBase &&) = delete;
  ~TosaToTensorBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-tensor");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-tensor"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Tensor dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToTensor");
  }
  ::llvm::StringRef getName() const override { return "TosaToTensor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToTensorBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOSATOTENSOR
#endif // GEN_PASS_DEF_TOSATOTENSOR

//===----------------------------------------------------------------------===//
// UBToLLVMConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_UBTOLLVMCONVERSIONPASS
struct UBToLLVMConversionPassOptions {
  unsigned indexBitwidth = 0;
};
std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass();
std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass(UBToLLVMConversionPassOptions options);
#undef GEN_PASS_DECL_UBTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DECL_UBTOLLVMCONVERSIONPASS
#ifdef GEN_PASS_DEF_UBTOLLVMCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass(UBToLLVMConversionPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class UBToLLVMConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = UBToLLVMConversionPassBase;

  UBToLLVMConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  UBToLLVMConversionPassBase(const UBToLLVMConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  UBToLLVMConversionPassBase& operator=(const UBToLLVMConversionPassBase &) = delete;
  UBToLLVMConversionPassBase(UBToLLVMConversionPassBase &&) = delete;
  UBToLLVMConversionPassBase& operator=(UBToLLVMConversionPassBase &&) = delete;
  ~UBToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-ub-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-ub-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert UB dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UBToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "UBToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UBToLLVMConversionPassBase<DerivedT>)

  UBToLLVMConversionPassBase(UBToLLVMConversionPassOptions options) : UBToLLVMConversionPassBase() {
    indexBitwidth = std::move(options.indexBitwidth);
  }
protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
private:

  friend std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass(UBToLLVMConversionPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass() {
  return impl::createUBToLLVMConversionPass();
}

std::unique_ptr<::mlir::Pass> createUBToLLVMConversionPass(UBToLLVMConversionPassOptions options) {
  return impl::createUBToLLVMConversionPass(std::move(options));
}
#undef GEN_PASS_DEF_UBTOLLVMCONVERSIONPASS
#endif // GEN_PASS_DEF_UBTOLLVMCONVERSIONPASS

//===----------------------------------------------------------------------===//
// UBToSPIRVConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_UBTOSPIRVCONVERSIONPASS
std::unique_ptr<::mlir::Pass> createUBToSPIRVConversionPass();
#undef GEN_PASS_DECL_UBTOSPIRVCONVERSIONPASS
#endif // GEN_PASS_DECL_UBTOSPIRVCONVERSIONPASS
#ifdef GEN_PASS_DEF_UBTOSPIRVCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createUBToSPIRVConversionPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class UBToSPIRVConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = UBToSPIRVConversionPassBase;

  UBToSPIRVConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  UBToSPIRVConversionPassBase(const UBToSPIRVConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  UBToSPIRVConversionPassBase& operator=(const UBToSPIRVConversionPassBase &) = delete;
  UBToSPIRVConversionPassBase(UBToSPIRVConversionPassBase &&) = delete;
  UBToSPIRVConversionPassBase& operator=(UBToSPIRVConversionPassBase &&) = delete;
  ~UBToSPIRVConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-ub-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-ub-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert UB dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UBToSPIRVConversionPass");
  }
  ::llvm::StringRef getName() const override { return "UBToSPIRVConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UBToSPIRVConversionPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createUBToSPIRVConversionPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createUBToSPIRVConversionPass() {
  return impl::createUBToSPIRVConversionPass();
}
#undef GEN_PASS_DEF_UBTOSPIRVCONVERSIONPASS
#endif // GEN_PASS_DEF_UBTOSPIRVCONVERSIONPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ArithToAMDGPUConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerArithToAMDGPUConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToAMDGPUConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithToAMDGPUConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToAMDGPUConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// ArithToArmSMEConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerArithToArmSMEConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToArmSMEConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithToArmSMEConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToArmSMEConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// ArithToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerArithToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerArithToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createArithToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAMDGPUToROCDL Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAMDGPUToROCDL() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertAMDGPUToROCDLPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAMDGPUToROCDLPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertAMDGPUToROCDLPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineForToGPU() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineForToGPUPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAffineForToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineForToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerAffinePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAffineToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerAffinePass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArithToEmitC Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArithToEmitC() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertArithToEmitC();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArithToEmitCPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertArithToEmitC();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArithToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArithToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createConvertArithToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArithToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createConvertArithToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArmNeon2dToIntr() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmNeon2dToIntrPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArmNeon2dToIntrPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmNeon2dToIntrPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArmSMEToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArmSMEToLLVM() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmSMEToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArmSMEToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmSMEToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArmSMEToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArmSMEToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmSMEToSCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertArmSMEToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmSMEToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAsyncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertAsyncToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertAsyncToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertAsyncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertBufferizationToMemRef Registration
//===----------------------------------------------------------------------===//

inline void registerConvertBufferizationToMemRef() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferizationToMemRefPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertBufferizationToMemRefPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferizationToMemRefPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToLibm Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToLibm() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToLibmPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToLibmPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToLibmPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToSPIRVPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToSPIRVPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertComplexToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToStandardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertComplexToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertControlFlowToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertControlFlowToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertControlFlowToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertControlFlowToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertControlFlowToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertControlFlowToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertControlFlowToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertControlFlowToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertControlFlowToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertControlFlowToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToEmitC Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToEmitC() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertFuncToEmitC();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertFuncToEmitCPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertFuncToEmitC();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertFuncToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertFuncToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertFuncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertFuncToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertFuncToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertFuncToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGPUToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGPUToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGPUToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGPUToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToLLVMSPVOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToLLVMSPVOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertGpuOpsToLLVMSPVOps();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGpuOpsToLLVMSPVOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertGpuOpsToLLVMSPVOps();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToNVVMOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertGpuOpsToNVVMOps();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGpuOpsToNVVMOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertGpuOpsToNVVMOps();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToROCDLOps() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToROCDLOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertGpuOpsToROCDLOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToROCDLOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertIndexToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertIndexToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertIndexToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertIndexToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertIndexToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertIndexToSPIRVPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertIndexToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertIndexToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertIndexToSPIRVPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertIndexToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToStandardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertLinalgToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToEmitC Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToEmitC() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToEmitC();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToEmitCPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToEmitC();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToFuncs Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToFuncs() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToFuncs();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToFuncsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToFuncs();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLibm Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLibm() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLibmPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToLibmPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLibmPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToROCDL Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToROCDL() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToROCDL();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToROCDLPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMathToROCDL();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMathToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMemRefToEmitC Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMemRefToEmitC() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMemRefToEmitC();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMemRefToEmitCPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertMemRefToEmitC();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMemRefToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMemRefToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMemRefToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMemRefToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMemRefToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMeshToMPIPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMeshToMPIPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMeshToMPIPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertMeshToMPIPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMeshToMPIPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertNVGPUToNVVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertNVGPUToNVVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertNVGPUToNVVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertNVGPUToNVVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertNVGPUToNVVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertNVVMToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertNVVMToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertNVVMToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertNVVMToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertNVVMToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToSCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertOpenACCToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenMPToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertOpenMPToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertOpenMPToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertOpenMPToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp Registration
//===----------------------------------------------------------------------===//

inline void registerConvertPDLToPDLInterp() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPDLToPDLInterpPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertPDLToPDLInterpPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPDLToPDLInterpPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu Registration
//===----------------------------------------------------------------------===//

inline void registerConvertParallelLoopToGpu() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopToGpuPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertParallelLoopToGpuPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopToGpuPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMPPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSCFToOpenMPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSCFToOpenMPPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertSCFToOpenMPPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSCFToOpenMPPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSPIRVToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSPIRVToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertSPIRVToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertSPIRVToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeConstraints() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeConstraintsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertShapeConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeToStandard() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeToStandardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertShapeToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertTensorToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTensorToLinalg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToLinalgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertTensorToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertTensorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTensorToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertTensorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToArmSME Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToArmSME() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToArmSMEPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToArmSMEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToArmSMEPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToGPU() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToGPUPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertVectorToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertVectorToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSPIRVPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToXeGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToXeGPU() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToXeGPUPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertVectorToXeGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToXeGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// FinalizeMemRefToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerFinalizeMemRefToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFinalizeMemRefToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFinalizeMemRefToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFinalizeMemRefToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createGpuToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGpuToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createGpuToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// LiftControlFlowToSCFPass Registration
//===----------------------------------------------------------------------===//

inline void registerLiftControlFlowToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLiftControlFlowToSCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLiftControlFlowToSCFPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLiftControlFlowToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVMPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerHostCodeToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerHostCodeToLLVMPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerHostCodeToLLVMPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLowerHostCodeToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// MapMemRefStorageClass Registration
//===----------------------------------------------------------------------===//

inline void registerMapMemRefStorageClass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createMapMemRefStorageClassPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMapMemRefStorageClassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createMapMemRefStorageClassPass();
  });
}

//===----------------------------------------------------------------------===//
// ReconcileUnrealizedCasts Registration
//===----------------------------------------------------------------------===//

inline void registerReconcileUnrealizedCasts() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createReconcileUnrealizedCastsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerReconcileUnrealizedCastsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createReconcileUnrealizedCastsPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToControlFlow Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToControlFlow() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToCFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFToControlFlowPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToCFPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToEmitC Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToEmitC() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSCFToEmitC();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFToEmitCPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSCFToEmitC();
  });
}

//===----------------------------------------------------------------------===//
// SCFToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToSPIRV() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSCFToSPIRV();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSCFToSPIRV();
  });
}

//===----------------------------------------------------------------------===//
// SetLLVMModuleDataLayoutPass Registration
//===----------------------------------------------------------------------===//

inline void registerSetLLVMModuleDataLayoutPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSetLLVMModuleDataLayoutPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSetLLVMModuleDataLayoutPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSetLLVMModuleDataLayoutPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaToArith Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToArith() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToArith();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToArithPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToArith();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalg() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalg();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalg();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalgNamed Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalgNamed() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalgNamed();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToLinalgNamedPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalgNamed();
  });
}

//===----------------------------------------------------------------------===//
// TosaToMLProgram Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToMLProgram() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaToMLProgram();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToMLProgramPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaToMLProgram();
  });
}

//===----------------------------------------------------------------------===//
// TosaToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToSCF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToSCF();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToSCF();
  });
}

//===----------------------------------------------------------------------===//
// TosaToTensor Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToTensor() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToTensor();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTosaToTensorPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToTensor();
  });
}

//===----------------------------------------------------------------------===//
// UBToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerUBToLLVMConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUBToLLVMConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerUBToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUBToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// UBToSPIRVConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerUBToSPIRVConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUBToSPIRVConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerUBToSPIRVConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUBToSPIRVConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// Conversion Registration
//===----------------------------------------------------------------------===//

inline void registerConversionPasses() {
  registerArithToAMDGPUConversionPass();
  registerArithToArmSMEConversionPass();
  registerArithToLLVMConversionPass();
  registerConvertAMDGPUToROCDL();
  registerConvertAffineForToGPU();
  registerConvertAffineToStandard();
  registerConvertArithToEmitC();
  registerConvertArithToSPIRV();
  registerConvertArmNeon2dToIntr();
  registerConvertArmSMEToLLVM();
  registerConvertArmSMEToSCF();
  registerConvertAsyncToLLVMPass();
  registerConvertBufferizationToMemRef();
  registerConvertComplexToLLVMPass();
  registerConvertComplexToLibm();
  registerConvertComplexToSPIRVPass();
  registerConvertComplexToStandard();
  registerConvertControlFlowToLLVMPass();
  registerConvertControlFlowToSPIRV();
  registerConvertFuncToEmitC();
  registerConvertFuncToLLVMPass();
  registerConvertFuncToSPIRV();
  registerConvertGPUToSPIRV();
  registerConvertGpuOpsToLLVMSPVOps();
  registerConvertGpuOpsToNVVMOps();
  registerConvertGpuOpsToROCDLOps();
  registerConvertIndexToLLVMPass();
  registerConvertIndexToSPIRVPass();
  registerConvertLinalgToStandard();
  registerConvertMathToEmitC();
  registerConvertMathToFuncs();
  registerConvertMathToLLVMPass();
  registerConvertMathToLibm();
  registerConvertMathToROCDL();
  registerConvertMathToSPIRV();
  registerConvertMemRefToEmitC();
  registerConvertMemRefToSPIRV();
  registerConvertMeshToMPIPass();
  registerConvertNVGPUToNVVMPass();
  registerConvertNVVMToLLVMPass();
  registerConvertOpenACCToSCF();
  registerConvertOpenMPToLLVMPass();
  registerConvertPDLToPDLInterp();
  registerConvertParallelLoopToGpu();
  registerConvertSCFToOpenMPPass();
  registerConvertSPIRVToLLVMPass();
  registerConvertShapeConstraints();
  registerConvertShapeToStandard();
  registerConvertTensorToLinalg();
  registerConvertTensorToSPIRV();
  registerConvertToLLVMPass();
  registerConvertVectorToArmSME();
  registerConvertVectorToGPU();
  registerConvertVectorToLLVMPass();
  registerConvertVectorToSCF();
  registerConvertVectorToSPIRV();
  registerConvertVectorToXeGPU();
  registerFinalizeMemRefToLLVMConversionPass();
  registerGpuToLLVMConversionPass();
  registerLiftControlFlowToSCFPass();
  registerLowerHostCodeToLLVMPass();
  registerMapMemRefStorageClass();
  registerReconcileUnrealizedCasts();
  registerSCFToControlFlow();
  registerSCFToEmitC();
  registerSCFToSPIRV();
  registerSetLLVMModuleDataLayoutPass();
  registerTosaToArith();
  registerTosaToLinalg();
  registerTosaToLinalgNamed();
  registerTosaToMLProgram();
  registerTosaToSCF();
  registerTosaToTensor();
  registerUBToLLVMConversionPass();
  registerUBToSPIRVConversionPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ArithToAMDGPUConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToAMDGPUConversionPassBase;

  ArithToAMDGPUConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToAMDGPUConversionPassBase(const ArithToAMDGPUConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  ArithToAMDGPUConversionPassBase& operator=(const ArithToAMDGPUConversionPassBase &) = delete;
  ArithToAMDGPUConversionPassBase(ArithToAMDGPUConversionPassBase &&) = delete;
  ArithToAMDGPUConversionPassBase& operator=(ArithToAMDGPUConversionPassBase &&) = delete;
  ~ArithToAMDGPUConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-amdgpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-amdgpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith operations to AMDGPU-specific implementations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToAMDGPUConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToAMDGPUConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<amdgpu::AMDGPUDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToAMDGPUConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
  ::mlir::Pass::Option<bool> saturateFP8Truncf{*this, "saturate-fp8-truncf", ::llvm::cl::desc("Use saturating truncation for 8-bit float types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowPackedF16Rtz{*this, "allow-packed-f16-round-to-zero", ::llvm::cl::desc("Whether we should allow f32->f16 packed round-to-zero conversion"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ArithToArmSMEConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToArmSMEConversionPassBase;

  ArithToArmSMEConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToArmSMEConversionPassBase(const ArithToArmSMEConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  ArithToArmSMEConversionPassBase& operator=(const ArithToArmSMEConversionPassBase &) = delete;
  ArithToArmSMEConversionPassBase(ArithToArmSMEConversionPassBase &&) = delete;
  ArithToArmSMEConversionPassBase& operator=(ArithToArmSMEConversionPassBase &&) = delete;
  ~ArithToArmSMEConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-arm-sme");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-arm-sme"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to ArmSME dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToArmSMEConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToArmSMEConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_sme::ArmSMEDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToArmSMEConversionPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ArithToLLVMConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ArithToLLVMConversionPassBase;

  ArithToLLVMConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ArithToLLVMConversionPassBase(const ArithToLLVMConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  ArithToLLVMConversionPassBase& operator=(const ArithToLLVMConversionPassBase &) = delete;
  ArithToLLVMConversionPassBase(ArithToLLVMConversionPassBase &&) = delete;
  ArithToLLVMConversionPassBase& operator=(ArithToLLVMConversionPassBase &&) = delete;
  ~ArithToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ArithToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "ArithToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ArithToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertAMDGPUToROCDLBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAMDGPUToROCDLBase;

  ConvertAMDGPUToROCDLBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAMDGPUToROCDLBase(const ConvertAMDGPUToROCDLBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertAMDGPUToROCDLBase& operator=(const ConvertAMDGPUToROCDLBase &) = delete;
  ConvertAMDGPUToROCDLBase(ConvertAMDGPUToROCDLBase &&) = delete;
  ConvertAMDGPUToROCDLBase& operator=(ConvertAMDGPUToROCDLBase &&) = delete;
  ~ConvertAMDGPUToROCDLBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-amdgpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-amdgpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Convert AMDGPU dialect to ROCDL dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAMDGPUToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertAMDGPUToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<ROCDL::ROCDLDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAMDGPUToROCDLBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
};

template <typename DerivedT>
class ConvertAffineForToGPUBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertAffineForToGPUBase;

  ConvertAffineForToGPUBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineForToGPUBase(const ConvertAffineForToGPUBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  ConvertAffineForToGPUBase& operator=(const ConvertAffineForToGPUBase &) = delete;
  ConvertAffineForToGPUBase(ConvertAffineForToGPUBase &&) = delete;
  ConvertAffineForToGPUBase& operator=(ConvertAffineForToGPUBase &&) = delete;
  ~ConvertAffineForToGPUBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-affine-for-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-affine-for-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert top-level AffineFor Ops to GPU kernels"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineForToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineForToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<gpu::GPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineForToGPUBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> numBlockDims{*this, "gpu-block-dims", ::llvm::cl::desc("Number of GPU block dimensions for mapping"), ::llvm::cl::init(1u)};
  ::mlir::Pass::Option<unsigned> numThreadDims{*this, "gpu-thread-dims", ::llvm::cl::desc("Number of GPU thread dimensions for mapping"), ::llvm::cl::init(1u)};
};

template <typename DerivedT>
class ConvertAffineToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAffineToStandardBase;

  ConvertAffineToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineToStandardBase(const ConvertAffineToStandardBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertAffineToStandardBase& operator=(const ConvertAffineToStandardBase &) = delete;
  ConvertAffineToStandardBase(ConvertAffineToStandardBase &&) = delete;
  ConvertAffineToStandardBase& operator=(ConvertAffineToStandardBase &&) = delete;
  ~ConvertAffineToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lower-affine"; }

  ::llvm::StringRef getDescription() const override { return "Lower Affine operations to a combination of Standard and SCF operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAffineToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertArithToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithToEmitCBase;

  ConvertArithToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithToEmitCBase(const ConvertArithToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArithToEmitCBase& operator=(const ConvertArithToEmitCBase &) = delete;
  ConvertArithToEmitCBase(ConvertArithToEmitCBase &&) = delete;
  ConvertArithToEmitCBase& operator=(ConvertArithToEmitCBase &&) = delete;
  ~ConvertArithToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to EmitC dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArithToEmitCBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertArithToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithToSPIRVBase;

  ConvertArithToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithToSPIRVBase(const ConvertArithToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArithToSPIRVBase& operator=(const ConvertArithToSPIRVBase &) = delete;
  ConvertArithToSPIRVBase(ConvertArithToSPIRVBase &&) = delete;
  ConvertArithToSPIRVBase& operator=(ConvertArithToSPIRVBase &&) = delete;
  ~ConvertArithToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arith dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArithToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertArmNeon2dToIntrBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmNeon2dToIntrBase;

  ConvertArmNeon2dToIntrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmNeon2dToIntrBase(const ConvertArmNeon2dToIntrBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArmNeon2dToIntrBase& operator=(const ConvertArmNeon2dToIntrBase &) = delete;
  ConvertArmNeon2dToIntrBase(ConvertArmNeon2dToIntrBase &&) = delete;
  ConvertArmNeon2dToIntrBase& operator=(ConvertArmNeon2dToIntrBase &&) = delete;
  ~ConvertArmNeon2dToIntrBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-neon-2d-to-intr");
  }
  ::llvm::StringRef getArgument() const override { return "arm-neon-2d-to-intr"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arm NEON structured ops to intrinsics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmNeon2dToIntr");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmNeon2dToIntr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_neon::ArmNeonDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmNeon2dToIntrBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertArmSMEToLLVMBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertArmSMEToLLVMBase;

  ConvertArmSMEToLLVMBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmSMEToLLVMBase(const ConvertArmSMEToLLVMBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  ConvertArmSMEToLLVMBase& operator=(const ConvertArmSMEToLLVMBase &) = delete;
  ConvertArmSMEToLLVMBase(ConvertArmSMEToLLVMBase &&) = delete;
  ConvertArmSMEToLLVMBase& operator=(ConvertArmSMEToLLVMBase &&) = delete;
  ~ConvertArmSMEToLLVMBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arm-sme-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arm-sme-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the ArmSME dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmSMEToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmSMEToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmSMEToLLVMBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> dumpTileLiveRanges{*this, "dump-tile-live-ranges", ::llvm::cl::desc("Dump the live ranges of SME tiles (for debugging)"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertArmSMEToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmSMEToSCFBase;

  ConvertArmSMEToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmSMEToSCFBase(const ConvertArmSMEToSCFBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertArmSMEToSCFBase& operator=(const ConvertArmSMEToSCFBase &) = delete;
  ConvertArmSMEToSCFBase(ConvertArmSMEToSCFBase &&) = delete;
  ConvertArmSMEToSCFBase& operator=(ConvertArmSMEToSCFBase &&) = delete;
  ~ConvertArmSMEToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arm-sme-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arm-sme-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the ArmSME dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmSMEToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmSMEToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<arith::ArithDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<arm_sme::ArmSMEDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertArmSMEToSCFBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertAsyncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertAsyncToLLVMPassBase;

  ConvertAsyncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAsyncToLLVMPassBase(const ConvertAsyncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertAsyncToLLVMPassBase& operator=(const ConvertAsyncToLLVMPassBase &) = delete;
  ConvertAsyncToLLVMPassBase(ConvertAsyncToLLVMPassBase &&) = delete;
  ConvertAsyncToLLVMPassBase& operator=(ConvertAsyncToLLVMPassBase &&) = delete;
  ~ConvertAsyncToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-async-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-async-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the async dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAsyncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertAsyncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<async::AsyncDialect>();
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertAsyncToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertBufferizationToMemRefBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertBufferizationToMemRefBase;

  ConvertBufferizationToMemRefBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertBufferizationToMemRefBase(const ConvertBufferizationToMemRefBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertBufferizationToMemRefBase& operator=(const ConvertBufferizationToMemRefBase &) = delete;
  ConvertBufferizationToMemRefBase(ConvertBufferizationToMemRefBase &&) = delete;
  ConvertBufferizationToMemRefBase& operator=(ConvertBufferizationToMemRefBase &&) = delete;
  ~ConvertBufferizationToMemRefBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-bufferization-to-memref");
  }
  ::llvm::StringRef getArgument() const override { return "convert-bufferization-to-memref"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the Bufferization dialect to the MemRef dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertBufferizationToMemRef");
  }
  ::llvm::StringRef getName() const override { return "ConvertBufferizationToMemRef"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertBufferizationToMemRefBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToLLVMPassBase;

  ConvertComplexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLLVMPassBase(const ConvertComplexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertComplexToLLVMPassBase& operator=(const ConvertComplexToLLVMPassBase &) = delete;
  ConvertComplexToLLVMPassBase(ConvertComplexToLLVMPassBase &&) = delete;
  ConvertComplexToLLVMPassBase& operator=(ConvertComplexToLLVMPassBase &&) = delete;
  ~ConvertComplexToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertComplexToLibmBase;

  ConvertComplexToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLibmBase(const ConvertComplexToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertComplexToLibmBase& operator=(const ConvertComplexToLibmBase &) = delete;
  ConvertComplexToLibmBase(ConvertComplexToLibmBase &&) = delete;
  ConvertComplexToLibmBase& operator=(ConvertComplexToLibmBase &&) = delete;
  ~ConvertComplexToLibmBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToLibmBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToSPIRVPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToSPIRVPassBase;

  ConvertComplexToSPIRVPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToSPIRVPassBase(const ConvertComplexToSPIRVPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertComplexToSPIRVPassBase& operator=(const ConvertComplexToSPIRVPassBase &) = delete;
  ConvertComplexToSPIRVPassBase(ConvertComplexToSPIRVPassBase &&) = delete;
  ConvertComplexToSPIRVPassBase& operator=(ConvertComplexToSPIRVPassBase &&) = delete;
  ~ConvertComplexToSPIRVPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to SPIRV dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToSPIRVPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToSPIRVPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToSPIRVPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertComplexToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToStandardBase;

  ConvertComplexToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToStandardBase(const ConvertComplexToStandardBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertComplexToStandardBase& operator=(const ConvertComplexToStandardBase &) = delete;
  ConvertComplexToStandardBase(ConvertComplexToStandardBase &&) = delete;
  ConvertComplexToStandardBase& operator=(ConvertComplexToStandardBase &&) = delete;
  ~ConvertComplexToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<math::MathDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertComplexToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertControlFlowToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertControlFlowToLLVMPassBase;

  ConvertControlFlowToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToLLVMPassBase(const ConvertControlFlowToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertControlFlowToLLVMPassBase& operator=(const ConvertControlFlowToLLVMPassBase &) = delete;
  ConvertControlFlowToLLVMPassBase(ConvertControlFlowToLLVMPassBase &&) = delete;
  ConvertControlFlowToLLVMPassBase& operator=(ConvertControlFlowToLLVMPassBase &&) = delete;
  ~ConvertControlFlowToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow operations to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertControlFlowToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertControlFlowToSPIRVBase;

  ConvertControlFlowToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToSPIRVBase(const ConvertControlFlowToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertControlFlowToSPIRVBase& operator=(const ConvertControlFlowToSPIRVBase &) = delete;
  ConvertControlFlowToSPIRVBase(ConvertControlFlowToSPIRVBase &&) = delete;
  ConvertControlFlowToSPIRVBase& operator=(ConvertControlFlowToSPIRVBase &&) = delete;
  ~ConvertControlFlowToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertControlFlowToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertFuncToEmitCBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToEmitCBase;

  ConvertFuncToEmitCBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToEmitCBase(const ConvertFuncToEmitCBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertFuncToEmitCBase& operator=(const ConvertFuncToEmitCBase &) = delete;
  ConvertFuncToEmitCBase(ConvertFuncToEmitCBase &&) = delete;
  ConvertFuncToEmitCBase& operator=(ConvertFuncToEmitCBase &&) = delete;
  ~ConvertFuncToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to EmitC dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToEmitCBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertFuncToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToLLVMPassBase;

  ConvertFuncToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToLLVMPassBase(const ConvertFuncToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertFuncToLLVMPassBase& operator=(const ConvertFuncToLLVMPassBase &) = delete;
  ConvertFuncToLLVMPassBase(ConvertFuncToLLVMPassBase &&) = delete;
  ConvertFuncToLLVMPassBase& operator=(ConvertFuncToLLVMPassBase &&) = delete;
  ~ConvertFuncToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert from the Func dialect to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace FuncOp's MemRef arguments with bare pointers to the MemRef element types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertFuncToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertFuncToSPIRVBase;

  ConvertFuncToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToSPIRVBase(const ConvertFuncToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertFuncToSPIRVBase& operator=(const ConvertFuncToSPIRVBase &) = delete;
  ConvertFuncToSPIRVBase(ConvertFuncToSPIRVBase &&) = delete;
  ConvertFuncToSPIRVBase& operator=(ConvertFuncToSPIRVBase &&) = delete;
  ~ConvertFuncToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertFuncToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertGPUToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGPUToSPIRVBase;

  ConvertGPUToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertGPUToSPIRVBase& operator=(const ConvertGPUToSPIRVBase &) = delete;
  ConvertGPUToSPIRVBase(ConvertGPUToSPIRVBase &&) = delete;
  ConvertGPUToSPIRVBase& operator=(ConvertGPUToSPIRVBase &&) = delete;
  ~ConvertGPUToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGPUToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertGPUToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGPUToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertGpuOpsToLLVMSPVOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToLLVMSPVOpsBase;

  ConvertGpuOpsToLLVMSPVOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToLLVMSPVOpsBase(const ConvertGpuOpsToLLVMSPVOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}
  ConvertGpuOpsToLLVMSPVOpsBase& operator=(const ConvertGpuOpsToLLVMSPVOpsBase &) = delete;
  ConvertGpuOpsToLLVMSPVOpsBase(ConvertGpuOpsToLLVMSPVOpsBase &&) = delete;
  ConvertGpuOpsToLLVMSPVOpsBase& operator=(ConvertGpuOpsToLLVMSPVOpsBase &&) = delete;
  ~ConvertGpuOpsToLLVMSPVOpsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-llvm-spv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-llvm-spv"; }

  ::llvm::StringRef getDescription() const override { return "Generate LLVM operations to be ingested by a SPIR-V backend for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToLLVMSPVOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToLLVMSPVOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToLLVMSPVOpsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertGpuOpsToNVVMOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToNVVMOpsBase;

  ConvertGpuOpsToNVVMOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}
  ConvertGpuOpsToNVVMOpsBase& operator=(const ConvertGpuOpsToNVVMOpsBase &) = delete;
  ConvertGpuOpsToNVVMOpsBase(ConvertGpuOpsToNVVMOpsBase &&) = delete;
  ConvertGpuOpsToNVVMOpsBase& operator=(ConvertGpuOpsToNVVMOpsBase &&) = delete;
  ~ConvertGpuOpsToNVVMOpsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Generate NVVM operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToNVVMOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToNVVMOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<NVVM::NVVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToNVVMOpsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> hasRedux{*this, "has-redux", ::llvm::cl::desc("Target gpu supports redux"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace memref arguments in GPU functions with bare pointers. All memrefs must have static shape."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertGpuOpsToROCDLOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToROCDLOpsBase;

  ConvertGpuOpsToROCDLOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}
  ConvertGpuOpsToROCDLOpsBase& operator=(const ConvertGpuOpsToROCDLOpsBase &) = delete;
  ConvertGpuOpsToROCDLOpsBase(ConvertGpuOpsToROCDLOpsBase &&) = delete;
  ConvertGpuOpsToROCDLOpsBase& operator=(ConvertGpuOpsToROCDLOpsBase &&) = delete;
  ~ConvertGpuOpsToROCDLOpsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Generate ROCDL operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToROCDLOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToROCDLOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<ROCDL::ROCDLDialect>();
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertGpuOpsToROCDLOpsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> chipset{*this, "chipset", ::llvm::cl::desc("Chipset that these operations will run on"), ::llvm::cl::init("gfx000")};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace memref arguments in GPU functions with bare pointers.All memrefs must have static shape"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<::mlir::gpu::amd::Runtime> runtime{*this, "runtime", ::llvm::cl::desc("Runtime code will be run on (default is Unknown, can also use HIP or OpenCl)"), ::llvm::cl::init(::mlir::gpu::amd::Runtime::Unknown), ::llvm::cl::values(
            clEnumValN(::mlir::gpu::amd::Runtime::Unknown, "unknown", "Unknown (default)"),
            clEnumValN(::mlir::gpu::amd::Runtime::HIP, "HIP", "HIP"),
            clEnumValN(::mlir::gpu::amd::Runtime::OpenCL, "OpenCL", "OpenCL")
          )};
};

template <typename DerivedT>
class ConvertIndexToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertIndexToLLVMPassBase;

  ConvertIndexToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertIndexToLLVMPassBase(const ConvertIndexToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertIndexToLLVMPassBase& operator=(const ConvertIndexToLLVMPassBase &) = delete;
  ConvertIndexToLLVMPassBase(ConvertIndexToLLVMPassBase &&) = delete;
  ConvertIndexToLLVMPassBase& operator=(ConvertIndexToLLVMPassBase &&) = delete;
  ~ConvertIndexToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-index-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-index-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the `index` dialect to the `llvm` dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertIndexToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertIndexToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertIndexToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertIndexToSPIRVPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertIndexToSPIRVPassBase;

  ConvertIndexToSPIRVPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertIndexToSPIRVPassBase(const ConvertIndexToSPIRVPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertIndexToSPIRVPassBase& operator=(const ConvertIndexToSPIRVPassBase &) = delete;
  ConvertIndexToSPIRVPassBase(ConvertIndexToSPIRVPassBase &&) = delete;
  ConvertIndexToSPIRVPassBase& operator=(ConvertIndexToSPIRVPassBase &&) = delete;
  ~ConvertIndexToSPIRVPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-index-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-index-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Lower the `index` dialect to the `spirv` dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertIndexToSPIRVPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertIndexToSPIRVPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertIndexToSPIRVPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertLinalgToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToStandardBase;

  ConvertLinalgToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToStandardBase(const ConvertLinalgToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertLinalgToStandardBase& operator=(const ConvertLinalgToStandardBase &) = delete;
  ConvertLinalgToStandardBase(ConvertLinalgToStandardBase &&) = delete;
  ConvertLinalgToStandardBase& operator=(ConvertLinalgToStandardBase &&) = delete;
  ~ConvertLinalgToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<func::FuncDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMathToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToEmitCBase;

  ConvertMathToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToEmitCBase(const ConvertMathToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMathToEmitCBase& operator=(const ConvertMathToEmitCBase &) = delete;
  ConvertMathToEmitCBase(ConvertMathToEmitCBase &&) = delete;
  ConvertMathToEmitCBase& operator=(ConvertMathToEmitCBase &&) = delete;
  ~ConvertMathToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert some Math operations to EmitC call_opaque operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToEmitCBase<DerivedT>)

protected:
  ::mlir::Pass::Option<::mlir::emitc::LanguageTarget> languageTarget{*this, "language-target", ::llvm::cl::desc("Select the language standard target for callees (c99 or cpp11)."), ::llvm::cl::init(::mlir::emitc::LanguageTarget::c99), ::llvm::cl::values(
            clEnumValN(::mlir::emitc::LanguageTarget::c99, "c99", "c99"),
            clEnumValN(::mlir::emitc::LanguageTarget::cpp11, "cpp11", "cpp11")
          )};
};

template <typename DerivedT>
class ConvertMathToFuncsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToFuncsBase;

  ConvertMathToFuncsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToFuncsBase(const ConvertMathToFuncsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertMathToFuncsBase& operator=(const ConvertMathToFuncsBase &) = delete;
  ConvertMathToFuncsBase(ConvertMathToFuncsBase &&) = delete;
  ConvertMathToFuncsBase& operator=(ConvertMathToFuncsBase &&) = delete;
  ~ConvertMathToFuncsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-funcs");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-funcs"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math operations to calls of outlined implementations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToFuncs");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToFuncs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToFuncsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> minWidthOfFPowIExponent{*this, "min-width-of-fpowi-exponent", ::llvm::cl::desc("Convert FPowI only if the width of its exponent's integer type is greater than or equal to this value"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> convertCtlz{*this, "convert-ctlz", ::llvm::cl::desc("Convert math.ctlz to a software implementation. Enable for targets that do not natively support ctlz."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertMathToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToLLVMPassBase;

  ConvertMathToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLLVMPassBase(const ConvertMathToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMathToLLVMPassBase& operator=(const ConvertMathToLLVMPassBase &) = delete;
  ConvertMathToLLVMPassBase(ConvertMathToLLVMPassBase &&) = delete;
  ConvertMathToLLVMPassBase& operator=(ConvertMathToLLVMPassBase &&) = delete;
  ~ConvertMathToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> approximateLog1p{*this, "approximate-log1p", ::llvm::cl::desc("Enable approximation of Log1p."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertMathToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToLibmBase;

  ConvertMathToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLibmBase(const ConvertMathToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertMathToLibmBase& operator=(const ConvertMathToLibmBase &) = delete;
  ConvertMathToLibmBase(ConvertMathToLibmBase &&) = delete;
  ConvertMathToLibmBase& operator=(ConvertMathToLibmBase &&) = delete;
  ~ConvertMathToLibmBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToLibmBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMathToROCDLBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToROCDLBase;

  ConvertMathToROCDLBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToROCDLBase(const ConvertMathToROCDLBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertMathToROCDLBase& operator=(const ConvertMathToROCDLBase &) = delete;
  ConvertMathToROCDLBase(ConvertMathToROCDLBase &&) = delete;
  ConvertMathToROCDLBase& operator=(ConvertMathToROCDLBase &&) = delete;
  ~ConvertMathToROCDLBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to ROCDL library calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<ROCDL::ROCDLDialect>();
    registry.insert<vector::VectorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToROCDLBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMathToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToSPIRVBase;

  ConvertMathToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToSPIRVBase(const ConvertMathToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMathToSPIRVBase& operator=(const ConvertMathToSPIRVBase &) = delete;
  ConvertMathToSPIRVBase(ConvertMathToSPIRVBase &&) = delete;
  ConvertMathToSPIRVBase& operator=(ConvertMathToSPIRVBase &&) = delete;
  ~ConvertMathToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMathToSPIRVBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMemRefToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMemRefToEmitCBase;

  ConvertMemRefToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToEmitCBase(const ConvertMemRefToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMemRefToEmitCBase& operator=(const ConvertMemRefToEmitCBase &) = delete;
  ConvertMemRefToEmitCBase(ConvertMemRefToEmitCBase &&) = delete;
  ConvertMemRefToEmitCBase& operator=(ConvertMemRefToEmitCBase &&) = delete;
  ~ConvertMemRefToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to EmitC dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToEmitC");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMemRefToEmitCBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertMemRefToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMemRefToSPIRVBase;

  ConvertMemRefToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToSPIRVBase(const ConvertMemRefToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMemRefToSPIRVBase& operator=(const ConvertMemRefToSPIRVBase &) = delete;
  ConvertMemRefToSPIRVBase(ConvertMemRefToSPIRVBase &&) = delete;
  ConvertMemRefToSPIRVBase& operator=(ConvertMemRefToSPIRVBase &&) = delete;
  ~ConvertMemRefToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMemRefToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int> boolNumBits{*this, "bool-num-bits", ::llvm::cl::desc("The number of bits to store a boolean value"), ::llvm::cl::init(8)};
  ::mlir::Pass::Option<bool> use64bitIndex{*this, "use-64bit-index", ::llvm::cl::desc("Use 64-bit integers to convert index types"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertMeshToMPIPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMeshToMPIPassBase;

  ConvertMeshToMPIPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMeshToMPIPassBase(const ConvertMeshToMPIPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertMeshToMPIPassBase& operator=(const ConvertMeshToMPIPassBase &) = delete;
  ConvertMeshToMPIPassBase(ConvertMeshToMPIPassBase &&) = delete;
  ConvertMeshToMPIPassBase& operator=(ConvertMeshToMPIPassBase &&) = delete;
  ~ConvertMeshToMPIPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-mesh-to-mpi");
  }
  ::llvm::StringRef getArgument() const override { return "convert-mesh-to-mpi"; }

  ::llvm::StringRef getDescription() const override { return "Convert Mesh dialect to MPI dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMeshToMPIPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertMeshToMPIPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<mpi::MPIDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<bufferization::BufferizationDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertMeshToMPIPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertNVGPUToNVVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertNVGPUToNVVMPassBase;

  ConvertNVGPUToNVVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertNVGPUToNVVMPassBase(const ConvertNVGPUToNVVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertNVGPUToNVVMPassBase& operator=(const ConvertNVGPUToNVVMPassBase &) = delete;
  ConvertNVGPUToNVVMPassBase(ConvertNVGPUToNVVMPassBase &&) = delete;
  ConvertNVGPUToNVVMPassBase& operator=(ConvertNVGPUToNVVMPassBase &&) = delete;
  ~ConvertNVGPUToNVVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-nvgpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-nvgpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert NVGPU dialect to NVVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertNVGPUToNVVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertNVGPUToNVVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<NVVM::NVVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertNVGPUToNVVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertNVVMToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertNVVMToLLVMPassBase;

  ConvertNVVMToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertNVVMToLLVMPassBase(const ConvertNVVMToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertNVVMToLLVMPassBase& operator=(const ConvertNVVMToLLVMPassBase &) = delete;
  ConvertNVVMToLLVMPassBase(ConvertNVVMToLLVMPassBase &&) = delete;
  ConvertNVVMToLLVMPassBase& operator=(ConvertNVVMToLLVMPassBase &&) = delete;
  ~ConvertNVVMToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-nvvm-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-nvvm-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert NVVM to PTX with Inline Assembly in LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertNVVMToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertNVVMToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<NVVM::NVVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertNVVMToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertOpenACCToSCFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToSCFBase;

  ConvertOpenACCToSCFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToSCFBase(const ConvertOpenACCToSCFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertOpenACCToSCFBase& operator=(const ConvertOpenACCToSCFBase &) = delete;
  ConvertOpenACCToSCFBase(ConvertOpenACCToSCFBase &&) = delete;
  ConvertOpenACCToSCFBase& operator=(ConvertOpenACCToSCFBase &&) = delete;
  ~ConvertOpenACCToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to OpenACC with SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<acc::OpenACCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenACCToSCFBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertOpenMPToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenMPToLLVMPassBase;

  ConvertOpenMPToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenMPToLLVMPassBase(const ConvertOpenMPToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertOpenMPToLLVMPassBase& operator=(const ConvertOpenMPToLLVMPassBase &) = delete;
  ConvertOpenMPToLLVMPassBase(ConvertOpenMPToLLVMPassBase &&) = delete;
  ConvertOpenMPToLLVMPassBase& operator=(ConvertOpenMPToLLVMPassBase &&) = delete;
  ~ConvertOpenMPToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openmp-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openmp-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenMP ops to OpenMP ops with LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenMPToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenMPToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertOpenMPToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertPDLToPDLInterpBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertPDLToPDLInterpBase;

  ConvertPDLToPDLInterpBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertPDLToPDLInterpBase(const ConvertPDLToPDLInterpBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertPDLToPDLInterpBase& operator=(const ConvertPDLToPDLInterpBase &) = delete;
  ConvertPDLToPDLInterpBase(ConvertPDLToPDLInterpBase &&) = delete;
  ConvertPDLToPDLInterpBase& operator=(ConvertPDLToPDLInterpBase &&) = delete;
  ~ConvertPDLToPDLInterpBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-pdl-to-pdl-interp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-pdl-to-pdl-interp"; }

  ::llvm::StringRef getDescription() const override { return "Convert PDL ops to PDL interpreter ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertPDLToPDLInterp");
  }
  ::llvm::StringRef getName() const override { return "ConvertPDLToPDLInterp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<pdl_interp::PDLInterpDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertPDLToPDLInterpBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertParallelLoopToGpuBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertParallelLoopToGpuBase;

  ConvertParallelLoopToGpuBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertParallelLoopToGpuBase(const ConvertParallelLoopToGpuBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertParallelLoopToGpuBase& operator=(const ConvertParallelLoopToGpuBase &) = delete;
  ConvertParallelLoopToGpuBase(ConvertParallelLoopToGpuBase &&) = delete;
  ConvertParallelLoopToGpuBase& operator=(ConvertParallelLoopToGpuBase &&) = delete;
  ~ConvertParallelLoopToGpuBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-parallel-loops-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-parallel-loops-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert mapped scf.parallel ops to gpu launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertParallelLoopToGpu");
  }
  ::llvm::StringRef getName() const override { return "ConvertParallelLoopToGpu"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<gpu::GPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertParallelLoopToGpuBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertSCFToOpenMPPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSCFToOpenMPPassBase;

  ConvertSCFToOpenMPPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSCFToOpenMPPassBase(const ConvertSCFToOpenMPPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertSCFToOpenMPPassBase& operator=(const ConvertSCFToOpenMPPassBase &) = delete;
  ConvertSCFToOpenMPPassBase(ConvertSCFToOpenMPPassBase &&) = delete;
  ConvertSCFToOpenMPPassBase& operator=(ConvertSCFToOpenMPPassBase &&) = delete;
  ~ConvertSCFToOpenMPPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-openmp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-openmp"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF parallel loop to OpenMP parallel + workshare constructs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSCFToOpenMPPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSCFToOpenMPPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<omp::OpenMPDialect>();
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSCFToOpenMPPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> numThreads{*this, "num-threads", ::llvm::cl::desc("Number of threads to use"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ConvertSPIRVToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSPIRVToLLVMPassBase;

  ConvertSPIRVToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSPIRVToLLVMPassBase(const ConvertSPIRVToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertSPIRVToLLVMPassBase& operator=(const ConvertSPIRVToLLVMPassBase &) = delete;
  ConvertSPIRVToLLVMPassBase(ConvertSPIRVToLLVMPassBase &&) = delete;
  ConvertSPIRVToLLVMPassBase& operator=(ConvertSPIRVToLLVMPassBase &&) = delete;
  ~ConvertSPIRVToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-spirv-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-spirv-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert SPIR-V dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSPIRVToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertSPIRVToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertSPIRVToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<::mlir::spirv::ClientAPI> clientAPI{*this, "client-api", ::llvm::cl::desc("Derive StorageClass to address space mapping from the client API"), ::llvm::cl::init(::mlir::spirv::ClientAPI::Unknown), ::llvm::cl::values(
	     clEnumValN(::mlir::spirv::ClientAPI::Unknown, "Unknown", "Unknown (default)"),
	     clEnumValN(::mlir::spirv::ClientAPI::Metal, "Metal", "Metal"),
	     clEnumValN(::mlir::spirv::ClientAPI::OpenCL, "OpenCL", "OpenCL"),
	     clEnumValN(::mlir::spirv::ClientAPI::Vulkan, "Vulkan", "Vulkan"),
	     clEnumValN(::mlir::spirv::ClientAPI::WebGPU, "WebGPU", "WebGPU")
	   )};
};

template <typename DerivedT>
class ConvertShapeConstraintsBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertShapeConstraintsBase;

  ConvertShapeConstraintsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeConstraintsBase(const ConvertShapeConstraintsBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertShapeConstraintsBase& operator=(const ConvertShapeConstraintsBase &) = delete;
  ConvertShapeConstraintsBase(ConvertShapeConstraintsBase &&) = delete;
  ConvertShapeConstraintsBase& operator=(ConvertShapeConstraintsBase &&) = delete;
  ~ConvertShapeConstraintsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Convert shape constraint operations to the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<cf::ControlFlowDialect>();
    registry.insert<scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeConstraintsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertShapeToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertShapeToStandardBase;

  ConvertShapeToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeToStandardBase(const ConvertShapeToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertShapeToStandardBase& operator=(const ConvertShapeToStandardBase &) = delete;
  ConvertShapeToStandardBase(ConvertShapeToStandardBase &&) = delete;
  ConvertShapeToStandardBase& operator=(ConvertShapeToStandardBase &&) = delete;
  ~ConvertShapeToStandardBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the shape dialect into the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertShapeToStandardBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertTensorToLinalgBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertTensorToLinalgBase;

  ConvertTensorToLinalgBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToLinalgBase(const ConvertTensorToLinalgBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertTensorToLinalgBase& operator=(const ConvertTensorToLinalgBase &) = delete;
  ConvertTensorToLinalgBase(ConvertTensorToLinalgBase &&) = delete;
  ConvertTensorToLinalgBase& operator=(ConvertTensorToLinalgBase &&) = delete;
  ~ConvertTensorToLinalgBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert some Tensor dialect ops to Linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToLinalgBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertTensorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertTensorToSPIRVBase;

  ConvertTensorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToSPIRVBase(const ConvertTensorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertTensorToSPIRVBase& operator=(const ConvertTensorToSPIRVBase &) = delete;
  ConvertTensorToSPIRVBase(ConvertTensorToSPIRVBase &&) = delete;
  ConvertTensorToSPIRVBase& operator=(ConvertTensorToSPIRVBase &&) = delete;
  ~ConvertTensorToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Tensor dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertTensorToSPIRVBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> emulateLT32BitScalarTypes{*this, "emulate-lt-32-bit-scalar-types", ::llvm::cl::desc("Emulate narrower scalar types with 32-bit ones if not supported by the target"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ConvertToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertToLLVMPassBase;

  ConvertToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertToLLVMPassBase(const ConvertToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertToLLVMPassBase& operator=(const ConvertToLLVMPassBase &) = delete;
  ConvertToLLVMPassBase(ConvertToLLVMPassBase &&) = delete;
  ConvertToLLVMPassBase& operator=(ConvertToLLVMPassBase &&) = delete;
  ~ConvertToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert to LLVM via dialect interfaces found in the input IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> filterDialects{*this, "filter-dialects", ::llvm::cl::desc("Test conversion patterns of only the specified dialects")};
  ::mlir::Pass::Option<bool> useDynamic{*this, "dynamic", ::llvm::cl::desc("Use op conversion attributes to configure the conversion"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertVectorToArmSMEBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToArmSMEBase;

  ConvertVectorToArmSMEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToArmSMEBase(const ConvertVectorToArmSMEBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToArmSMEBase& operator=(const ConvertVectorToArmSMEBase &) = delete;
  ConvertVectorToArmSMEBase(ConvertVectorToArmSMEBase &&) = delete;
  ConvertVectorToArmSMEBase& operator=(ConvertVectorToArmSMEBase &&) = delete;
  ~ConvertVectorToArmSMEBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-arm-sme");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-arm-sme"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the ArmSME dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToArmSME");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToArmSME"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arm_sme::ArmSMEDialect>();
    registry.insert<arm_sve::ArmSVEDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToArmSMEBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertVectorToGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToGPUBase;

  ConvertVectorToGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToGPUBase(const ConvertVectorToGPUBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToGPUBase& operator=(const ConvertVectorToGPUBase &) = delete;
  ConvertVectorToGPUBase(ConvertVectorToGPUBase &&) = delete;
  ConvertVectorToGPUBase& operator=(ConvertVectorToGPUBase &&) = delete;
  ~ConvertVectorToGPUBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the GPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<gpu::GPUDialect>();
    registry.insert<affine::AffineDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<nvgpu::NVGPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToGPUBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useNvGpu{*this, "use-nvgpu", ::llvm::cl::desc("convert to NvGPU ops instead of GPU dialect ops"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertVectorToLLVMPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToLLVMPassBase;

  ConvertVectorToLLVMPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToLLVMPassBase(const ConvertVectorToLLVMPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToLLVMPassBase& operator=(const ConvertVectorToLLVMPassBase &) = delete;
  ConvertVectorToLLVMPassBase(ConvertVectorToLLVMPassBase &&) = delete;
  ConvertVectorToLLVMPassBase& operator=(ConvertVectorToLLVMPassBase &&) = delete;
  ~ConvertVectorToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToLLVMPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> reassociateFPReductions{*this, "reassociate-fp-reductions", ::llvm::cl::desc("Allows llvm to reassociate floating-point reductions for speed"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> force32BitVectorIndices{*this, "force-32bit-vector-indices", ::llvm::cl::desc("Allows compiler to assume vector indices fit in 32-bit if that yields faster code"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> amx{*this, "enable-amx", ::llvm::cl::desc("Enables the use of AMX dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armNeon{*this, "enable-arm-neon", ::llvm::cl::desc("Enables the use of ArmNeon dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armSVE{*this, "enable-arm-sve", ::llvm::cl::desc("Enables the use of ArmSVE dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> x86Vector{*this, "enable-x86vector", ::llvm::cl::desc("Enables the use of X86Vector dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<vector::VectorTransformsOptions> vectorTransformsOptions{*this, "vector-transform-options", ::llvm::cl::desc("Options to lower some operations like contractions and transposes."), ::llvm::cl::init(vector::VectorTransformsOptions())};
};

template <typename DerivedT>
class ConvertVectorToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSCFBase;

  ConvertVectorToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSCFBase(const ConvertVectorToSCFBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToSCFBase& operator=(const ConvertVectorToSCFBase &) = delete;
  ConvertVectorToSCFBase(ConvertVectorToSCFBase &&) = delete;
  ConvertVectorToSCFBase& operator=(ConvertVectorToSCFBase &&) = delete;
  ~ConvertVectorToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSCFBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> fullUnroll{*this, "full-unroll", ::llvm::cl::desc("Perform full unrolling when converting vector transfers to SCF"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> targetRank{*this, "target-rank", ::llvm::cl::desc("Target vector rank to which transfer ops should be lowered"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> lowerTensors{*this, "lower-tensors", ::llvm::cl::desc("Lower transfer ops that operate on tensors"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> lowerScalable{*this, "lower-scalable", ::llvm::cl::desc("Add scalable vector specific lowerings (that introduce loops)"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ConvertVectorToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSPIRVBase;

  ConvertVectorToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSPIRVBase(const ConvertVectorToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToSPIRVBase& operator=(const ConvertVectorToSPIRVBase &) = delete;
  ConvertVectorToSPIRVBase(ConvertVectorToSPIRVBase &&) = delete;
  ConvertVectorToSPIRVBase& operator=(ConvertVectorToSPIRVBase &&) = delete;
  ~ConvertVectorToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Vector dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
    registry.insert<ub::UBDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToSPIRVBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertVectorToXeGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToXeGPUBase;

  ConvertVectorToXeGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToXeGPUBase(const ConvertVectorToXeGPUBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertVectorToXeGPUBase& operator=(const ConvertVectorToXeGPUBase &) = delete;
  ConvertVectorToXeGPUBase(ConvertVectorToXeGPUBase &&) = delete;
  ConvertVectorToXeGPUBase& operator=(ConvertVectorToXeGPUBase &&) = delete;
  ~ConvertVectorToXeGPUBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-xegpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-xegpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the XeGPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToXeGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToXeGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<memref::MemRefDialect>();
    registry.insert<arith::ArithDialect>();
    registry.insert<vector::VectorDialect>();
    registry.insert<xegpu::XeGPUDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertVectorToXeGPUBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FinalizeMemRefToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FinalizeMemRefToLLVMConversionPassBase;

  FinalizeMemRefToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizeMemRefToLLVMConversionPassBase(const FinalizeMemRefToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FinalizeMemRefToLLVMConversionPassBase& operator=(const FinalizeMemRefToLLVMConversionPassBase &) = delete;
  FinalizeMemRefToLLVMConversionPassBase(FinalizeMemRefToLLVMConversionPassBase &&) = delete;
  FinalizeMemRefToLLVMConversionPassBase& operator=(FinalizeMemRefToLLVMConversionPassBase &&) = delete;
  ~FinalizeMemRefToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalize-memref-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "finalize-memref-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Finalize MemRef dialect to LLVM dialect conversion"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizeMemRefToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "FinalizeMemRefToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FinalizeMemRefToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useAlignedAlloc{*this, "use-aligned-alloc", ::llvm::cl::desc("Use aligned_alloc in place of malloc for heap allocations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> useGenericFunctions{*this, "use-generic-functions", ::llvm::cl::desc("Use generic allocation and deallocation functions instead of the classic 'malloc', 'aligned_alloc' and 'free' functions"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class GpuToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuToLLVMConversionPassBase;

  GpuToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  GpuToLLVMConversionPassBase& operator=(const GpuToLLVMConversionPassBase &) = delete;
  GpuToLLVMConversionPassBase(GpuToLLVMConversionPassBase &&) = delete;
  GpuToLLVMConversionPassBase& operator=(GpuToLLVMConversionPassBase &&) = delete;
  ~GpuToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to LLVM dialect with GPU runtime calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GpuToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> hostBarePtrCallConv{*this, "use-bare-pointers-for-host", ::llvm::cl::desc("Use bare pointers to pass memref arguments to host functions. All memrefs must have static shape."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> kernelBarePtrCallConv{*this, "use-bare-pointers-for-kernels", ::llvm::cl::desc("Use bare pointers to pass memref arguments to kernels. The kernel must use the same setting for this option."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> kernelIntersperseSizeCallConv{*this, "intersperse-sizes-for-kernels", ::llvm::cl::desc("Inserts a size_t argument following each memref argument, containing the static size in bytes of the buffer. Incompatible arguments are rejected. This is intended for use by the Vulkan runtime with the kernel bare pointer calling convention, to enable dynamic binding of buffers as arguments without static type info."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LiftControlFlowToSCFPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LiftControlFlowToSCFPassBase;

  LiftControlFlowToSCFPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LiftControlFlowToSCFPassBase(const LiftControlFlowToSCFPassBase &other) : ::mlir::OperationPass<>(other) {}
  LiftControlFlowToSCFPassBase& operator=(const LiftControlFlowToSCFPassBase &) = delete;
  LiftControlFlowToSCFPassBase(LiftControlFlowToSCFPassBase &&) = delete;
  LiftControlFlowToSCFPassBase& operator=(LiftControlFlowToSCFPassBase &&) = delete;
  ~LiftControlFlowToSCFPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lift-cf-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "lift-cf-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lift ControlFlow dialect to SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LiftControlFlowToSCFPass");
  }
  ::llvm::StringRef getName() const override { return "LiftControlFlowToSCFPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<scf::SCFDialect>();
    registry.insert<arith::ArithDialect>();
    registry.insert<ub::UBDialect>();
    registry.insert<func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LiftControlFlowToSCFPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LowerHostCodeToLLVMPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LowerHostCodeToLLVMPassBase;

  LowerHostCodeToLLVMPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerHostCodeToLLVMPassBase(const LowerHostCodeToLLVMPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LowerHostCodeToLLVMPassBase& operator=(const LowerHostCodeToLLVMPassBase &) = delete;
  LowerHostCodeToLLVMPassBase(LowerHostCodeToLLVMPassBase &&) = delete;
  LowerHostCodeToLLVMPassBase& operator=(LowerHostCodeToLLVMPassBase &&) = delete;
  ~LowerHostCodeToLLVMPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "lower-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lowers the host module code and `gpu.launch_func` to LLVM"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerHostCodeToLLVMPass");
  }
  ::llvm::StringRef getName() const override { return "LowerHostCodeToLLVMPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerHostCodeToLLVMPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class MapMemRefStorageClassBase : public ::mlir::OperationPass<> {
public:
  using Base = MapMemRefStorageClassBase;

  MapMemRefStorageClassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  MapMemRefStorageClassBase(const MapMemRefStorageClassBase &other) : ::mlir::OperationPass<>(other) {}
  MapMemRefStorageClassBase& operator=(const MapMemRefStorageClassBase &) = delete;
  MapMemRefStorageClassBase(MapMemRefStorageClassBase &&) = delete;
  MapMemRefStorageClassBase& operator=(MapMemRefStorageClassBase &&) = delete;
  ~MapMemRefStorageClassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("map-memref-spirv-storage-class");
  }
  ::llvm::StringRef getArgument() const override { return "map-memref-spirv-storage-class"; }

  ::llvm::StringRef getDescription() const override { return "Map numeric MemRef memory spaces to SPIR-V storage classes"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MapMemRefStorageClass");
  }
  ::llvm::StringRef getName() const override { return "MapMemRefStorageClass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MapMemRefStorageClassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> clientAPI{*this, "client-api", ::llvm::cl::desc("The client API to use for populating mappings"), ::llvm::cl::init("vulkan")};
};

template <typename DerivedT>
class ReconcileUnrealizedCastsBase : public ::mlir::OperationPass<> {
public:
  using Base = ReconcileUnrealizedCastsBase;

  ReconcileUnrealizedCastsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ReconcileUnrealizedCastsBase(const ReconcileUnrealizedCastsBase &other) : ::mlir::OperationPass<>(other) {}
  ReconcileUnrealizedCastsBase& operator=(const ReconcileUnrealizedCastsBase &) = delete;
  ReconcileUnrealizedCastsBase(ReconcileUnrealizedCastsBase &&) = delete;
  ReconcileUnrealizedCastsBase& operator=(ReconcileUnrealizedCastsBase &&) = delete;
  ~ReconcileUnrealizedCastsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("reconcile-unrealized-casts");
  }
  ::llvm::StringRef getArgument() const override { return "reconcile-unrealized-casts"; }

  ::llvm::StringRef getDescription() const override { return "Simplify and eliminate unrealized conversion casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReconcileUnrealizedCasts");
  }
  ::llvm::StringRef getName() const override { return "ReconcileUnrealizedCasts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ReconcileUnrealizedCastsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFToControlFlowBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToControlFlowBase;

  SCFToControlFlowBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToControlFlowBase(const SCFToControlFlowBase &other) : ::mlir::OperationPass<>(other) {}
  SCFToControlFlowBase& operator=(const SCFToControlFlowBase &) = delete;
  SCFToControlFlowBase(SCFToControlFlowBase &&) = delete;
  SCFToControlFlowBase& operator=(SCFToControlFlowBase &&) = delete;
  ~SCFToControlFlowBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-cf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-cf"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to ControlFlow dialect, replacing structured control flow with a CFG"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToControlFlow");
  }
  ::llvm::StringRef getName() const override { return "SCFToControlFlow"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<cf::ControlFlowDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToControlFlowBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFToEmitCBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToEmitCBase;

  SCFToEmitCBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToEmitCBase(const SCFToEmitCBase &other) : ::mlir::OperationPass<>(other) {}
  SCFToEmitCBase& operator=(const SCFToEmitCBase &) = delete;
  SCFToEmitCBase(SCFToEmitCBase &&) = delete;
  SCFToEmitCBase& operator=(SCFToEmitCBase &&) = delete;
  ~SCFToEmitCBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-emitc");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-emitc"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to EmitC dialect, maintaining structured control flow"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToEmitC");
  }
  ::llvm::StringRef getName() const override { return "SCFToEmitC"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<emitc::EmitCDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToEmitCBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFToSPIRVBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToSPIRVBase;

  SCFToSPIRVBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToSPIRVBase(const SCFToSPIRVBase &other) : ::mlir::OperationPass<>(other) {}
  SCFToSPIRVBase& operator=(const SCFToSPIRVBase &) = delete;
  SCFToSPIRVBase(SCFToSPIRVBase &&) = delete;
  SCFToSPIRVBase& operator=(SCFToSPIRVBase &&) = delete;
  ~SCFToSPIRVBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to SPIR-V dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "SCFToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFToSPIRVBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SetLLVMModuleDataLayoutPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SetLLVMModuleDataLayoutPassBase;

  SetLLVMModuleDataLayoutPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SetLLVMModuleDataLayoutPassBase(const SetLLVMModuleDataLayoutPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  SetLLVMModuleDataLayoutPassBase& operator=(const SetLLVMModuleDataLayoutPassBase &) = delete;
  SetLLVMModuleDataLayoutPassBase(SetLLVMModuleDataLayoutPassBase &&) = delete;
  SetLLVMModuleDataLayoutPassBase& operator=(SetLLVMModuleDataLayoutPassBase &&) = delete;
  ~SetLLVMModuleDataLayoutPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("set-llvm-module-datalayout");
  }
  ::llvm::StringRef getArgument() const override { return "set-llvm-module-datalayout"; }

  ::llvm::StringRef getDescription() const override { return "Attach a datalayout string as a module attribute"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SetLLVMModuleDataLayoutPass");
  }
  ::llvm::StringRef getName() const override { return "SetLLVMModuleDataLayoutPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SetLLVMModuleDataLayoutPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> dataLayout{*this, "data-layout", ::llvm::cl::desc("String description (LLVM format) of the data layout that is expected on the produced module"), ::llvm::cl::init("")};
};

template <typename DerivedT>
class TosaToArithBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToArithBase;

  TosaToArithBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToArithBase(const TosaToArithBase &other) : ::mlir::OperationPass<>(other) {}
  TosaToArithBase& operator=(const TosaToArithBase &) = delete;
  TosaToArithBase(TosaToArithBase &&) = delete;
  TosaToArithBase& operator=(TosaToArithBase &&) = delete;
  ~TosaToArithBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-arith");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-arith"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Arith dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToArith");
  }
  ::llvm::StringRef getName() const override { return "TosaToArith"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToArithBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> includeApplyRescale{*this, "include-apply-rescale", ::llvm::cl::desc("Whether to include the lowering for tosa.apply_rescale to arith"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> use32Bit{*this, "use-32-bit", ::llvm::cl::desc("Whether to prioritze lowering to 32-bit operations"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TosaToLinalgBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgBase;

  TosaToLinalgBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgBase(const TosaToLinalgBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  TosaToLinalgBase& operator=(const TosaToLinalgBase &) = delete;
  TosaToLinalgBase(TosaToLinalgBase &&) = delete;
  TosaToLinalgBase& operator=(TosaToLinalgBase &&) = delete;
  ~TosaToLinalgBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalg");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> disableTosaDecompositions{*this, "disable-tosa-decompositions", ::llvm::cl::desc("Disable tosa decompositions pass"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> aggressiveReduceConstant{*this, "aggressive-reduce-constant", ::llvm::cl::desc("Always perform the reduce constant optimization"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TosaToLinalgNamedBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgNamedBase;

  TosaToLinalgNamedBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgNamedBase(const TosaToLinalgNamedBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  TosaToLinalgNamedBase& operator=(const TosaToLinalgNamedBase &) = delete;
  TosaToLinalgNamedBase(TosaToLinalgNamedBase &&) = delete;
  TosaToLinalgNamedBase& operator=(TosaToLinalgNamedBase &&) = delete;
  ~TosaToLinalgNamedBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg-named");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg-named"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg named operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalgNamed");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalgNamed"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToLinalgNamedBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> preferConv2DKernelLayoutHWCF{*this, "prefer-conv2d-kernel-layout-hwcf", ::llvm::cl::desc("Prefer generating linalg.conv_2d_nhwc_hwcf over linalg.conv_2d_nhwc_fhwc"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TosaToMLProgramBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TosaToMLProgramBase;

  TosaToMLProgramBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToMLProgramBase(const TosaToMLProgramBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TosaToMLProgramBase& operator=(const TosaToMLProgramBase &) = delete;
  TosaToMLProgramBase(TosaToMLProgramBase &&) = delete;
  TosaToMLProgramBase& operator=(TosaToMLProgramBase &&) = delete;
  ~TosaToMLProgramBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-mlprogram");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-mlprogram"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the MLProgram dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToMLProgram");
  }
  ::llvm::StringRef getName() const override { return "TosaToMLProgram"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<ml_program::MLProgramDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToMLProgramBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToSCFBase;

  TosaToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToSCFBase(const TosaToSCFBase &other) : ::mlir::OperationPass<>(other) {}
  TosaToSCFBase& operator=(const TosaToSCFBase &) = delete;
  TosaToSCFBase(TosaToSCFBase &&) = delete;
  TosaToSCFBase& operator=(TosaToSCFBase &&) = delete;
  ~TosaToSCFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToSCF");
  }
  ::llvm::StringRef getName() const override { return "TosaToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tensor::TensorDialect, scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToSCFBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TosaToTensorBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToTensorBase;

  TosaToTensorBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToTensorBase(const TosaToTensorBase &other) : ::mlir::OperationPass<>(other) {}
  TosaToTensorBase& operator=(const TosaToTensorBase &) = delete;
  TosaToTensorBase(TosaToTensorBase &&) = delete;
  TosaToTensorBase& operator=(TosaToTensorBase &&) = delete;
  ~TosaToTensorBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-tensor");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-tensor"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Tensor dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToTensor");
  }
  ::llvm::StringRef getName() const override { return "TosaToTensor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TosaToTensorBase<DerivedT>)

protected:
};

template <typename DerivedT>
class UBToLLVMConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = UBToLLVMConversionPassBase;

  UBToLLVMConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  UBToLLVMConversionPassBase(const UBToLLVMConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  UBToLLVMConversionPassBase& operator=(const UBToLLVMConversionPassBase &) = delete;
  UBToLLVMConversionPassBase(UBToLLVMConversionPassBase &&) = delete;
  UBToLLVMConversionPassBase& operator=(UBToLLVMConversionPassBase &&) = delete;
  ~UBToLLVMConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-ub-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-ub-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert UB dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UBToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "UBToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<LLVM::LLVMDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UBToLLVMConversionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class UBToSPIRVConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = UBToSPIRVConversionPassBase;

  UBToSPIRVConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  UBToSPIRVConversionPassBase(const UBToSPIRVConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  UBToSPIRVConversionPassBase& operator=(const UBToSPIRVConversionPassBase &) = delete;
  UBToSPIRVConversionPassBase(UBToSPIRVConversionPassBase &&) = delete;
  UBToSPIRVConversionPassBase& operator=(UBToSPIRVConversionPassBase &&) = delete;
  ~UBToSPIRVConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-ub-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-ub-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert UB dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UBToSPIRVConversionPass");
  }
  ::llvm::StringRef getName() const override { return "UBToSPIRVConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<spirv::SPIRVDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UBToSPIRVConversionPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
