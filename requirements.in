# Core Data Science and Machine Learning Libraries
numpy
pandas
scikit-learn

# Data Visualization
matplotlib
seaborn
plotly

# Scientific Computing
scipy

# Jupyter Notebook Support (optional but recommended for data analysis)
jupyter
ipykernel

# Oil & Gas Specific Libraries (optional - uncomment if needed)
# petropy>=0.1.0  # Petrophysical analysis
# welly>=0.5.0    # Well log analysis
# lasio>=0.30     # LAS file reading

# Advanced ML Libraries
tensorflow
# torch>=1.11.0
# xgboost>=1.5.0
# lightgbm>=3.3.0

# Architecture Diagrams
diagrams

# Data Processing and Engineering
# openpyxl>=3.0.0  # Excel file support
# xlrd>=2.0.0      # Excel file reading
# requests>=2.25.0 # HTTP requests for data APIs

# Development and Testing Tools (optional)
# pytest>=6.0.0
# black>=22.0.0    # Code formatting
# flake8>=4.0.0    # Code linting
# mypy>=0.910      # Type checking

# Database Connectivity (optional - uncomment if needed)
# sqlalchemy>=1.4.0
# psycopg2-binary>=2.8.0  # PostgreSQL
# pymongo>=4.0.0          # MongoDB

# Configuration and Environment Management
# python-dotenv>=0.19.0
