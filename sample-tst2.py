import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
from sklearn.preprocessing import StandardScaler

# Prepare data for deep learning
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Deep neural network
def create_deep_model():
    model = Sequential([
        Dense(64, activation='relu', input_shape=(4,)),
        BatchNormalization(),
        Dropout(0.3),
        Den<PERSON>(32, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        <PERSON><PERSON>(16, activation='relu'),
        <PERSON><PERSON>(8, activation='relu'),
        Dense(1, activation='linear')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='mse',
        metrics=['mae']
    )
    return model

deep_model = create_deep_model()
deep_model.summary()

# Generate more synthetic data for training
np.random.seed(42)
n_samples = 1000

# Generate realistic oil well data
pressure = np.random.normal(3000, 500, n_samples)
temp = np.random.normal(80, 10, n_samples)
porosity = np.random.normal(0.15, 0.05, n_samples)
permeability = np.random.lognormal(3, 1, n_samples)

# Complex relationship for production
production = (
    0.05 * pressure + 
    2.0 * temp + 
    500 * porosity + 
    1.5 * permeability + 
    0.001 * pressure * porosity + 
    np.random.normal(0, 20, n_samples)
)

X_large = np.column_stack([pressure, temp, porosity, permeability])
y_large = production

X_large_scaled = scaler.fit_transform(X_large)

# Train deep model
history = deep_model.fit(
    X_large_scaled, y_large,
    epochs=100,
    batch_size=32,
    validation_split=0.2,
    verbose=0
)

print(f"Deep Learning Final Loss: {history.history['loss'][-1]:.3f}")