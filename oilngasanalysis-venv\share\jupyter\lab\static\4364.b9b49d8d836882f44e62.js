"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4364],{54364:(e,t,n)=>{n.r(t);n.d(t,{Hooks:()=>ze,Lexer:()=>$e,Marked:()=>Ae,Parser:()=>Te,Renderer:()=>Re,TextRenderer:()=>Se,Tokenizer:()=>ye,defaults:()=>r,getDefaults:()=>s,lexer:()=>ve,marked:()=>Ie,options:()=>Pe,parse:()=>Ee,parseInline:()=>qe,parser:()=>Ze,setOptions:()=>Le,use:()=>Ce,walkTokens:()=>Be});function s(){return{async:false,breaks:false,extensions:null,gfm:true,hooks:null,pedantic:false,renderer:null,silent:false,tokenizer:null,walkTokens:null}}let r=s();function i(e){r=e}const l={exec:()=>null};function o(e,t=""){let n=typeof e==="string"?e:e.source;const s={replace:(e,t)=>{let r=typeof t==="string"?t:t.source;r=r.replace(a.caret,"$1");n=n.replace(e,r);return s},getRegex:()=>new RegExp(n,t)};return s}const a={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[\t ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")};const c=/^(?:[ \t]*(?:\n|$))+/;const h=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/;const p=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/;const u=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/;const f=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/;const g=/(?:[*+-]|\d{1,9}[.)])/;const k=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/;const d=o(k).replace(/bull/g,g).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex();const x=o(k).replace(/bull/g,g).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex();const b=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/;const w=/^[^\n]+/;const m=/(?!\s*\])(?:\\.|[^\[\]\\])+/;const y=o(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",m).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex();const $=o(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,g).getRegex();const R="address|article|aside|base|basefont|blockquote|body|caption"+"|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption"+"|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe"+"|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option"+"|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title"+"|tr|track|ul";const S=/<!--(?:-?>|[\s\S]*?(?:-->|$))/;const T=o("^ {0,3}(?:"+"<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)"+"|comment[^\\n]*(\\n+|$)"+"|<\\?[\\s\\S]*?(?:\\?>\\n*|$)"+"|<![A-Z][\\s\\S]*?(?:>\\n*|$)"+"|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)"+"|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)"+"|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)"+"|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)"+")","i").replace("comment",S).replace("tag",R).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();const z=o(b).replace("hr",u).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",R).getRegex();const A=o(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",z).getRegex();const _={blockquote:A,code:h,def:y,fences:p,heading:f,hr:u,html:T,lheading:d,list:$,newline:c,paragraph:z,table:l,text:w};const I=o("^ *([^\\n ].*)\\n"+" {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)"+"(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",u).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",R).getRegex();const P={..._,lheading:x,table:I,paragraph:o(b).replace("hr",u).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",I).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",R).getRegex()};const L={..._,html:o("^ *(?:comment *(?:\\n|\\s*$)"+"|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)"+"|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",S).replace(/tag/g,"(?!(?:"+"a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub"+"|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)"+"\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:l,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:o(b).replace("hr",u).replace("heading"," *#{1,6} *[^\n]").replace("lheading",d).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()};const C=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/;const B=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/;const q=/^( {2,}|\\)\n(?!\s*$)/;const E=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/;const Z=/[\p{P}\p{S}]/u;const v=/[\s\p{P}\p{S}]/u;const D=/[^\s\p{P}\p{S}]/u;const M=o(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,v).getRegex();const j=/(?!~)[\p{P}\p{S}]/u;const O=/(?!~)[\s\p{P}\p{S}]/u;const Q=/(?:[^\s\p{P}\p{S}]|~)/u;const N=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g;const G=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/;const H=o(G,"u").replace(/punct/g,Z).getRegex();const X=o(G,"u").replace(/punct/g,j).getRegex();const F="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)"+"|[^*]+(?=[^*])"+"|(?!\\*)punct(\\*+)(?=[\\s]|$)"+"|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)"+"|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)"+"|[\\s](\\*+)(?!\\*)(?=punct)"+"|(?!\\*)punct(\\*+)(?!\\*)(?=punct)"+"|notPunctSpace(\\*+)(?=notPunctSpace)";const U=o(F,"gu").replace(/notPunctSpace/g,D).replace(/punctSpace/g,v).replace(/punct/g,Z).getRegex();const J=o(F,"gu").replace(/notPunctSpace/g,Q).replace(/punctSpace/g,O).replace(/punct/g,j).getRegex();const K=o("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)"+"|[^_]+(?=[^_])"+"|(?!_)punct(_+)(?=[\\s]|$)"+"|notPunctSpace(_+)(?!_)(?=punctSpace|$)"+"|(?!_)punctSpace(_+)(?=notPunctSpace)"+"|[\\s](_+)(?!_)(?=punct)"+"|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,D).replace(/punctSpace/g,v).replace(/punct/g,Z).getRegex();const V=o(/\\(punct)/,"gu").replace(/punct/g,Z).getRegex();const W=o(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();const Y=o(S).replace("(?:--\x3e|$)","--\x3e").getRegex();const ee=o("^comment"+"|^</[a-zA-Z][\\w:-]*\\s*>"+"|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>"+"|^<\\?[\\s\\S]*?\\?>"+"|^<![a-zA-Z]+\\s[\\s\\S]*?>"+"|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Y).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex();const te=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;const ne=o(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",te).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex();const se=o(/^!?\[(label)\]\[(ref)\]/).replace("label",te).replace("ref",m).getRegex();const re=o(/^!?\[(ref)\](?:\[\])?/).replace("ref",m).getRegex();const ie=o("reflink|nolink(?!\\()","g").replace("reflink",se).replace("nolink",re).getRegex();const le={_backpedal:l,anyPunctuation:V,autolink:W,blockSkip:N,br:q,code:B,del:l,emStrongLDelim:H,emStrongRDelimAst:U,emStrongRDelimUnd:K,escape:C,link:ne,nolink:re,punctuation:M,reflink:se,reflinkSearch:ie,tag:ee,text:E,url:l};const oe={...le,link:o(/^!?\[(label)\]\((.*?)\)/).replace("label",te).getRegex(),reflink:o(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",te).getRegex()};const ae={...le,emStrongRDelimAst:J,emStrongLDelim:X,url:o(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};const ce={...ae,br:o(q).replace("{2,}","*").getRegex(),text:o(ae.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};const he={normal:_,gfm:P,pedantic:L};const pe={normal:le,gfm:ae,breaks:ce,pedantic:oe};const ue={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};const fe=e=>ue[e];function ge(e,t){if(t){if(a.escapeTest.test(e)){return e.replace(a.escapeReplace,fe)}}else{if(a.escapeTestNoEncode.test(e)){return e.replace(a.escapeReplaceNoEncode,fe)}}return e}function ke(e){try{e=encodeURI(e).replace(a.percentDecode,"%")}catch{return null}return e}function de(e,t){const n=e.replace(a.findPipe,((e,t,n)=>{let s=false;let r=t;while(--r>=0&&n[r]==="\\")s=!s;if(s){return"|"}else{return" |"}})),s=n.split(a.splitPipe);let r=0;if(!s[0].trim()){s.shift()}if(s.length>0&&!s.at(-1)?.trim()){s.pop()}if(t){if(s.length>t){s.splice(t)}else{while(s.length<t)s.push("")}}for(;r<s.length;r++){s[r]=s[r].trim().replace(a.slashPipe,"|")}return s}function xe(e,t,n){const s=e.length;if(s===0){return""}let r=0;while(r<s){const n=e.charAt(s-r-1);if(n===t&&true){r++}else{break}}return e.slice(0,s-r)}function be(e,t){if(e.indexOf(t[1])===-1){return-1}let n=0;for(let s=0;s<e.length;s++){if(e[s]==="\\"){s++}else if(e[s]===t[0]){n++}else if(e[s]===t[1]){n--;if(n<0){return s}}}return-1}function we(e,t,n,s,r){const i=t.href;const l=t.title||null;const o=e[1].replace(r.other.outputLinkReplace,"$1");if(e[0].charAt(0)!=="!"){s.state.inLink=true;const e={type:"link",raw:n,href:i,title:l,text:o,tokens:s.inlineTokens(o)};s.state.inLink=false;return e}return{type:"image",raw:n,href:i,title:l,text:o}}function me(e,t,n){const s=e.match(n.other.indentCodeCompensation);if(s===null){return t}const r=s[1];return t.split("\n").map((e=>{const t=e.match(n.other.beginningSpace);if(t===null){return e}const[s]=t;if(s.length>=r.length){return e.slice(r.length)}return e})).join("\n")}class ye{options;rules;lexer;constructor(e){this.options=e||r}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0){return{type:"space",raw:t[0]}}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:!this.options.pedantic?xe(e,"\n"):e}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0];const n=me(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){const t=xe(e,"#");if(this.options.pedantic){e=t.trim()}else if(!t||this.rules.other.endingSpaceChar.test(t)){e=t.trim()}}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t){return{type:"hr",raw:xe(t[0],"\n")}}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let e=xe(t[0],"\n").split("\n");let n="";let s="";const r=[];while(e.length>0){let t=false;const i=[];let l;for(l=0;l<e.length;l++){if(this.rules.other.blockquoteStart.test(e[l])){i.push(e[l]);t=true}else if(!t){i.push(e[l])}else{break}}e=e.slice(l);const o=i.join("\n");const a=o.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}\n${o}`:o;s=s?`${s}\n${a}`:a;const c=this.lexer.state.top;this.lexer.state.top=true;this.lexer.blockTokens(a,r,true);this.lexer.state.top=c;if(e.length===0){break}const h=r.at(-1);if(h?.type==="code"){break}else if(h?.type==="blockquote"){const t=h;const i=t.raw+"\n"+e.join("\n");const l=this.blockquote(i);r[r.length-1]=l;n=n.substring(0,n.length-t.raw.length)+l.raw;s=s.substring(0,s.length-t.text.length)+l.text;break}else if(h?.type==="list"){const t=h;const i=t.raw+"\n"+e.join("\n");const l=this.list(i);r[r.length-1]=l;n=n.substring(0,n.length-h.raw.length)+l.raw;s=s.substring(0,s.length-t.raw.length)+l.raw;e=i.substring(r.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:n,tokens:r,text:s}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1;const r={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:false,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`;if(this.options.pedantic){n=s?n:"[*+-]"}const i=this.rules.other.listItemRegex(n);let l=false;while(e){let n=false;let s="";let o="";if(!(t=i.exec(e))){break}if(this.rules.block.hr.test(e)){break}s=t[0];e=e.substring(s.length);let a=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,(e=>" ".repeat(3*e.length)));let c=e.split("\n",1)[0];let h=!a.trim();let p=0;if(this.options.pedantic){p=2;o=a.trimStart()}else if(h){p=t[1].length+1}else{p=t[2].search(this.rules.other.nonSpaceChar);p=p>4?1:p;o=a.slice(p);p+=t[1].length}if(h&&this.rules.other.blankLine.test(c)){s+=c+"\n";e=e.substring(c.length+1);n=true}if(!n){const t=this.rules.other.nextBulletRegex(p);const n=this.rules.other.hrRegex(p);const r=this.rules.other.fencesBeginRegex(p);const i=this.rules.other.headingBeginRegex(p);const l=this.rules.other.htmlBeginRegex(p);while(e){const u=e.split("\n",1)[0];let f;c=u;if(this.options.pedantic){c=c.replace(this.rules.other.listReplaceNesting,"  ");f=c}else{f=c.replace(this.rules.other.tabCharGlobal,"    ")}if(r.test(c)){break}if(i.test(c)){break}if(l.test(c)){break}if(t.test(c)){break}if(n.test(c)){break}if(f.search(this.rules.other.nonSpaceChar)>=p||!c.trim()){o+="\n"+f.slice(p)}else{if(h){break}if(a.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4){break}if(r.test(a)){break}if(i.test(a)){break}if(n.test(a)){break}o+="\n"+c}if(!h&&!c.trim()){h=true}s+=u+"\n";e=e.substring(u.length+1);a=f.slice(p)}}if(!r.loose){if(l){r.loose=true}else if(this.rules.other.doubleBlankLine.test(s)){l=true}}let u=null;let f;if(this.options.gfm){u=this.rules.other.listIsTask.exec(o);if(u){f=u[0]!=="[ ] ";o=o.replace(this.rules.other.listReplaceTask,"")}}r.items.push({type:"list_item",raw:s,task:!!u,checked:f,loose:false,text:o,tokens:[]});r.raw+=s}const o=r.items.at(-1);if(o){o.raw=o.raw.trimEnd();o.text=o.text.trimEnd()}else{return}r.raw=r.raw.trimEnd();for(let e=0;e<r.items.length;e++){this.lexer.state.top=false;r.items[e].tokens=this.lexer.blockTokens(r.items[e].text,[]);if(!r.loose){const t=r.items[e].tokens.filter((e=>e.type==="space"));const n=t.length>0&&t.some((e=>this.rules.other.anyLine.test(e.raw)));r.loose=n}}if(r.loose){for(let e=0;e<r.items.length;e++){r.items[e].loose=true}}return r}}html(e){const t=this.rules.block.html.exec(e);if(t){const e={type:"html",block:true,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]};return e}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," ");const n=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"";const s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(!t){return}if(!this.rules.other.tableDelimiter.test(t[2])){return}const n=de(t[1]);const s=t[2].replace(this.rules.other.tableAlignChars,"").split("|");const r=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[];const i={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length!==s.length){return}for(const l of s){if(this.rules.other.tableAlignRight.test(l)){i.align.push("right")}else if(this.rules.other.tableAlignCenter.test(l)){i.align.push("center")}else if(this.rules.other.tableAlignLeft.test(l)){i.align.push("left")}else{i.align.push(null)}}for(let l=0;l<n.length;l++){i.header.push({text:n[l],tokens:this.lexer.inline(n[l]),header:true,align:i.align[l]})}for(const l of r){i.rows.push(de(l,i.header.length).map(((e,t)=>({text:e,tokens:this.lexer.inline(e),header:false,align:i.align[t]}))))}return i}lheading(e){const t=this.rules.block.lheading.exec(e);if(t){return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e=t[1].charAt(t[1].length-1)==="\n"?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t){return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}}escape(e){const t=this.rules.inline.escape.exec(e);if(t){return{type:"escape",raw:t[0],text:t[1]}}}tag(e){const t=this.rules.inline.tag.exec(e);if(t){if(!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])){this.lexer.state.inLink=true}else if(this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])){this.lexer.state.inLink=false}if(!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])){this.lexer.state.inRawBlock=true}else if(this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])){this.lexer.state.inRawBlock=false}return{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:false,text:t[0]}}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e)){return}const t=xe(e.slice(0,-1),"\\");if((e.length-t.length)%2===0){return}}else{const e=be(t[2],"()");if(e>-1){const n=t[0].indexOf("!")===0?5:4;const s=n+t[1].length+e;t[2]=t[2].substring(0,e);t[0]=t[0].substring(0,s).trim();t[3]=""}}let n=t[2];let s="";if(this.options.pedantic){const e=this.rules.other.pedanticHrefTitle.exec(n);if(e){n=e[1];s=e[3]}}else{s=t[3]?t[3].slice(1,-1):""}n=n.trim();if(this.rules.other.startAngleBracket.test(n)){if(this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)){n=n.slice(1)}else{n=n.slice(1,-1)}}return we(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:s?s.replace(this.rules.inline.anyPunctuation,"$1"):s},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const e=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," ");const s=t[e.toLowerCase()];if(!s){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return we(n,s,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(!s)return;if(s[3]&&n.match(this.rules.other.unicodeAlphaNumeric))return;const r=s[1]||s[2]||"";if(!r||!n||this.rules.inline.punctuation.exec(n)){const n=[...s[0]].length-1;let r,i,l=n,o=0;const a=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;a.lastIndex=0;t=t.slice(-1*e.length+n);while((s=a.exec(t))!=null){r=s[1]||s[2]||s[3]||s[4]||s[5]||s[6];if(!r)continue;i=[...r].length;if(s[3]||s[4]){l+=i;continue}else if(s[5]||s[6]){if(n%3&&!((n+i)%3)){o+=i;continue}}l-=i;if(l>0)continue;i=Math.min(i,i+l+o);const t=[...s[0]][0].length;const a=e.slice(0,n+s.index+t+i);if(Math.min(n,i)%2){const e=a.slice(1,-1);return{type:"em",raw:a,text:e,tokens:this.lexer.inlineTokens(e)}}const c=a.slice(2,-2);return{type:"strong",raw:a,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," ");const n=this.rules.other.nonSpaceChar.test(e);const s=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);if(n&&s){e=e.substring(1,e.length-1)}return{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t){return{type:"br",raw:t[0]}}}del(e){const t=this.rules.inline.del.exec(e);if(t){return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let e,n;if(t[2]==="@"){e=t[1];n="mailto:"+e}else{e=t[1];n=e}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if(t[2]==="@"){e=t[0];n="mailto:"+e}else{let s;do{s=t[0];t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(s!==t[0]);e=t[0];if(t[1]==="www."){n="http://"+t[0]}else{n=t[0]}}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}}class $e{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[];this.tokens.links=Object.create(null);this.options=e||r;this.options.tokenizer=this.options.tokenizer||new ye;this.tokenizer=this.options.tokenizer;this.tokenizer.options=this.options;this.tokenizer.lexer=this;this.inlineQueue=[];this.state={inLink:false,inRawBlock:false,top:true};const t={other:a,block:he.normal,inline:pe.normal};if(this.options.pedantic){t.block=he.pedantic;t.inline=pe.pedantic}else if(this.options.gfm){t.block=he.gfm;if(this.options.breaks){t.inline=pe.breaks}else{t.inline=pe.gfm}}this.tokenizer.rules=t}static get rules(){return{block:he,inline:pe}}static lex(e,t){const n=new $e(t);return n.lex(e)}static lexInline(e,t){const n=new $e(t);return n.inlineTokens(e)}lex(e){e=e.replace(a.carriageReturn,"\n");this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const e=this.inlineQueue[t];this.inlineTokens(e.src,e.tokens)}this.inlineQueue=[];return this.tokens}blockTokens(e,t=[],n=false){if(this.options.pedantic){e=e.replace(a.tabCharGlobal,"    ").replace(a.spaceLine,"")}while(e){let s;if(this.options.extensions?.block?.some((n=>{if(s=n.call({lexer:this},e,t)){e=e.substring(s.raw.length);t.push(s);return true}return false}))){continue}if(s=this.tokenizer.space(e)){e=e.substring(s.raw.length);const n=t.at(-1);if(s.raw.length===1&&n!==undefined){n.raw+="\n"}else{t.push(s)}continue}if(s=this.tokenizer.code(e)){e=e.substring(s.raw.length);const n=t.at(-1);if(n?.type==="paragraph"||n?.type==="text"){n.raw+="\n"+s.raw;n.text+="\n"+s.text;this.inlineQueue.at(-1).src=n.text}else{t.push(s)}continue}if(s=this.tokenizer.fences(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.heading(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.hr(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.blockquote(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.list(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.html(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.def(e)){e=e.substring(s.raw.length);const n=t.at(-1);if(n?.type==="paragraph"||n?.type==="text"){n.raw+="\n"+s.raw;n.text+="\n"+s.raw;this.inlineQueue.at(-1).src=n.text}else if(!this.tokens.links[s.tag]){this.tokens.links[s.tag]={href:s.href,title:s.title}}continue}if(s=this.tokenizer.table(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.lheading(e)){e=e.substring(s.raw.length);t.push(s);continue}let r=e;if(this.options.extensions?.startBlock){let t=Infinity;const n=e.slice(1);let s;this.options.extensions.startBlock.forEach((e=>{s=e.call({lexer:this},n);if(typeof s==="number"&&s>=0){t=Math.min(t,s)}}));if(t<Infinity&&t>=0){r=e.substring(0,t+1)}}if(this.state.top&&(s=this.tokenizer.paragraph(r))){const i=t.at(-1);if(n&&i?.type==="paragraph"){i.raw+="\n"+s.raw;i.text+="\n"+s.text;this.inlineQueue.pop();this.inlineQueue.at(-1).src=i.text}else{t.push(s)}n=r.length!==e.length;e=e.substring(s.raw.length);continue}if(s=this.tokenizer.text(e)){e=e.substring(s.raw.length);const n=t.at(-1);if(n?.type==="text"){n.raw+="\n"+s.raw;n.text+="\n"+s.text;this.inlineQueue.pop();this.inlineQueue.at(-1).src=n.text}else{t.push(s)}continue}if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}else{throw new Error(t)}}}this.state.top=true;return t}inline(e,t=[]){this.inlineQueue.push({src:e,tokens:t});return t}inlineTokens(e,t=[]){let n=e;let s=null;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0){while((s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null){if(e.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))){n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex)}}}}while((s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null){n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex)}while((s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null){n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex)}let r=false;let i="";while(e){if(!r){i=""}r=false;let s;if(this.options.extensions?.inline?.some((n=>{if(s=n.call({lexer:this},e,t)){e=e.substring(s.raw.length);t.push(s);return true}return false}))){continue}if(s=this.tokenizer.escape(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.tag(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.link(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(s.raw.length);const n=t.at(-1);if(s.type==="text"&&n?.type==="text"){n.raw+=s.raw;n.text+=s.text}else{t.push(s)}continue}if(s=this.tokenizer.emStrong(e,n,i)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.codespan(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.br(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.del(e)){e=e.substring(s.raw.length);t.push(s);continue}if(s=this.tokenizer.autolink(e)){e=e.substring(s.raw.length);t.push(s);continue}if(!this.state.inLink&&(s=this.tokenizer.url(e))){e=e.substring(s.raw.length);t.push(s);continue}let l=e;if(this.options.extensions?.startInline){let t=Infinity;const n=e.slice(1);let s;this.options.extensions.startInline.forEach((e=>{s=e.call({lexer:this},n);if(typeof s==="number"&&s>=0){t=Math.min(t,s)}}));if(t<Infinity&&t>=0){l=e.substring(0,t+1)}}if(s=this.tokenizer.inlineText(l)){e=e.substring(s.raw.length);if(s.raw.slice(-1)!=="_"){i=s.raw.slice(-1)}r=true;const n=t.at(-1);if(n?.type==="text"){n.raw+=s.raw;n.text+=s.text}else{t.push(s)}continue}if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}else{throw new Error(t)}}}return t}}class Re{options;parser;constructor(e){this.options=e||r}space(e){return""}code({text:e,lang:t,escaped:n}){const s=(t||"").match(a.notSpaceStart)?.[0];const r=e.replace(a.endingNewline,"")+"\n";if(!s){return"<pre><code>"+(n?r:ge(r,true))+"</code></pre>\n"}return'<pre><code class="language-'+ge(s)+'">'+(n?r:ge(r,true))+"</code></pre>\n"}blockquote({tokens:e}){const t=this.parser.parse(e);return`<blockquote>\n${t}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){const t=e.ordered;const n=e.start;let s="";for(let l=0;l<e.items.length;l++){const t=e.items[l];s+=this.listitem(t)}const r=t?"ol":"ul";const i=t&&n!==1?' start="'+n+'"':"";return"<"+r+i+">\n"+s+"</"+r+">\n"}listitem(e){let t="";if(e.task){const n=this.checkbox({checked:!!e.checked});if(e.loose){if(e.tokens[0]?.type==="paragraph"){e.tokens[0].text=n+" "+e.tokens[0].text;if(e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"){e.tokens[0].tokens[0].text=n+" "+ge(e.tokens[0].tokens[0].text);e.tokens[0].tokens[0].escaped=true}}else{e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:true})}}else{t+=n+" "}}t+=this.parser.parse(e.tokens,!!e.loose);return`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="";let n="";for(let r=0;r<e.header.length;r++){n+=this.tablecell(e.header[r])}t+=this.tablerow({text:n});let s="";for(let r=0;r<e.rows.length;r++){const t=e.rows[r];n="";for(let e=0;e<t.length;e++){n+=this.tablecell(t[e])}s+=this.tablerow({text:n})}if(s)s=`<tbody>${s}</tbody>`;return"<table>\n"+"<thead>\n"+t+"</thead>\n"+s+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){const t=this.parser.parseInline(e.tokens);const n=e.header?"th":"td";const s=e.align?`<${n} align="${e.align}">`:`<${n}>`;return s+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${ge(e,true)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const s=this.parser.parseInline(n);const r=ke(e);if(r===null){return s}e=r;let i='<a href="'+e+'"';if(t){i+=' title="'+ge(t)+'"'}i+=">"+s+"</a>";return i}image({href:e,title:t,text:n}){const s=ke(e);if(s===null){return ge(n)}e=s;let r=`<img src="${e}" alt="${n}"`;if(t){r+=` title="${ge(t)}"`}r+=">";return r}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:ge(e.text)}}class Se{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class Te{options;renderer;textRenderer;constructor(e){this.options=e||r;this.options.renderer=this.options.renderer||new Re;this.renderer=this.options.renderer;this.renderer.options=this.options;this.renderer.parser=this;this.textRenderer=new Se}static parse(e,t){const n=new Te(t);return n.parse(e)}static parseInline(e,t){const n=new Te(t);return n.parseInline(e)}parse(e,t=true){let n="";for(let s=0;s<e.length;s++){const r=e[s];if(this.options.extensions?.renderers?.[r.type]){const e=r;const t=this.options.extensions.renderers[e.type].call({parser:this},e);if(t!==false||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}const i=r;switch(i.type){case"space":{n+=this.renderer.space(i);continue}case"hr":{n+=this.renderer.hr(i);continue}case"heading":{n+=this.renderer.heading(i);continue}case"code":{n+=this.renderer.code(i);continue}case"table":{n+=this.renderer.table(i);continue}case"blockquote":{n+=this.renderer.blockquote(i);continue}case"list":{n+=this.renderer.list(i);continue}case"html":{n+=this.renderer.html(i);continue}case"paragraph":{n+=this.renderer.paragraph(i);continue}case"text":{let r=i;let l=this.renderer.text(r);while(s+1<e.length&&e[s+1].type==="text"){r=e[++s];l+="\n"+this.renderer.text(r)}if(t){n+=this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l,escaped:true}]})}else{n+=l}continue}default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent){console.error(e);return""}else{throw new Error(e)}}}}return n}parseInline(e,t=this.renderer){let n="";for(let s=0;s<e.length;s++){const r=e[s];if(this.options.extensions?.renderers?.[r.type]){const e=this.options.extensions.renderers[r.type].call({parser:this},r);if(e!==false||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){n+=e||"";continue}}const i=r;switch(i.type){case"escape":{n+=t.text(i);break}case"html":{n+=t.html(i);break}case"link":{n+=t.link(i);break}case"image":{n+=t.image(i);break}case"strong":{n+=t.strong(i);break}case"em":{n+=t.em(i);break}case"codespan":{n+=t.codespan(i);break}case"br":{n+=t.br(i);break}case"del":{n+=t.del(i);break}case"text":{n+=t.text(i);break}default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent){console.error(e);return""}else{throw new Error(e)}}}}return n}}class ze{options;block;constructor(e){this.options=e||r}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?$e.lex:$e.lexInline}provideParser(){return this.block?Te.parse:Te.parseInline}}class Ae{defaults=s();options=this.setOptions;parse=this.parseMarkdown(true);parseInline=this.parseMarkdown(false);Parser=Te;Renderer=Re;TextRenderer=Se;Lexer=$e;Tokenizer=ye;Hooks=ze;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(const s of e){n=n.concat(t.call(this,s));switch(s.type){case"table":{const e=s;for(const s of e.header){n=n.concat(this.walkTokens(s.tokens,t))}for(const s of e.rows){for(const e of s){n=n.concat(this.walkTokens(e.tokens,t))}}break}case"list":{const e=s;n=n.concat(this.walkTokens(e.items,t));break}default:{const e=s;if(this.defaults.extensions?.childTokens?.[e.type]){this.defaults.extensions.childTokens[e.type].forEach((s=>{const r=e[s].flat(Infinity);n=n.concat(this.walkTokens(r,t))}))}else if(e.tokens){n=n.concat(this.walkTokens(e.tokens,t))}}}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};e.forEach((e=>{const n={...e};n.async=this.defaults.async||n.async||false;if(e.extensions){e.extensions.forEach((e=>{if(!e.name){throw new Error("extension name required")}if("renderer"in e){const n=t.renderers[e.name];if(n){t.renderers[e.name]=function(...t){let s=e.renderer.apply(this,t);if(s===false){s=n.apply(this,t)}return s}}else{t.renderers[e.name]=e.renderer}}if("tokenizer"in e){if(!e.level||e.level!=="block"&&e.level!=="inline"){throw new Error("extension level must be 'block' or 'inline'")}const n=t[e.level];if(n){n.unshift(e.tokenizer)}else{t[e.level]=[e.tokenizer]}if(e.start){if(e.level==="block"){if(t.startBlock){t.startBlock.push(e.start)}else{t.startBlock=[e.start]}}else if(e.level==="inline"){if(t.startInline){t.startInline.push(e.start)}else{t.startInline=[e.start]}}}}if("childTokens"in e&&e.childTokens){t.childTokens[e.name]=e.childTokens}}));n.extensions=t}if(e.renderer){const t=this.defaults.renderer||new Re(this.defaults);for(const n in e.renderer){if(!(n in t)){throw new Error(`renderer '${n}' does not exist`)}if(["options","parser"].includes(n)){continue}const s=n;const r=e.renderer[s];const i=t[s];t[s]=(...e)=>{let n=r.apply(t,e);if(n===false){n=i.apply(t,e)}return n||""}}n.renderer=t}if(e.tokenizer){const t=this.defaults.tokenizer||new ye(this.defaults);for(const n in e.tokenizer){if(!(n in t)){throw new Error(`tokenizer '${n}' does not exist`)}if(["options","rules","lexer"].includes(n)){continue}const s=n;const r=e.tokenizer[s];const i=t[s];t[s]=(...e)=>{let n=r.apply(t,e);if(n===false){n=i.apply(t,e)}return n}}n.tokenizer=t}if(e.hooks){const t=this.defaults.hooks||new ze;for(const n in e.hooks){if(!(n in t)){throw new Error(`hook '${n}' does not exist`)}if(["options","block"].includes(n)){continue}const s=n;const r=e.hooks[s];const i=t[s];if(ze.passThroughHooks.has(n)){t[s]=e=>{if(this.defaults.async){return Promise.resolve(r.call(t,e)).then((e=>i.call(t,e)))}const n=r.call(t,e);return i.call(t,n)}}else{t[s]=(...e)=>{let n=r.apply(t,e);if(n===false){n=i.apply(t,e)}return n}}}n.hooks=t}if(e.walkTokens){const t=this.defaults.walkTokens;const s=e.walkTokens;n.walkTokens=function(e){let n=[];n.push(s.call(this,e));if(t){n=n.concat(t.call(this,e))}return n}}this.defaults={...this.defaults,...n}}));return this}setOptions(e){this.defaults={...this.defaults,...e};return this}lexer(e,t){return $e.lex(e,t??this.defaults)}parser(e,t){return Te.parse(e,t??this.defaults)}parseMarkdown(e){const t=(t,n)=>{const s={...n};const r={...this.defaults,...s};const i=this.onError(!!r.silent,!!r.async);if(this.defaults.async===true&&s.async===false){return i(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."))}if(typeof t==="undefined"||t===null){return i(new Error("marked(): input parameter is undefined or null"))}if(typeof t!=="string"){return i(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"))}if(r.hooks){r.hooks.options=r;r.hooks.block=e}const l=r.hooks?r.hooks.provideLexer():e?$e.lex:$e.lexInline;const o=r.hooks?r.hooks.provideParser():e?Te.parse:Te.parseInline;if(r.async){return Promise.resolve(r.hooks?r.hooks.preprocess(t):t).then((e=>l(e,r))).then((e=>r.hooks?r.hooks.processAllTokens(e):e)).then((e=>r.walkTokens?Promise.all(this.walkTokens(e,r.walkTokens)).then((()=>e)):e)).then((e=>o(e,r))).then((e=>r.hooks?r.hooks.postprocess(e):e)).catch(i)}try{if(r.hooks){t=r.hooks.preprocess(t)}let e=l(t,r);if(r.hooks){e=r.hooks.processAllTokens(e)}if(r.walkTokens){this.walkTokens(e,r.walkTokens)}let n=o(e,r);if(r.hooks){n=r.hooks.postprocess(n)}return n}catch(a){return i(a)}};return t}onError(e,t){return n=>{n.message+="\nPlease report this to https://github.com/markedjs/marked.";if(e){const e="<p>An error occurred:</p><pre>"+ge(n.message+"",true)+"</pre>";if(t){return Promise.resolve(e)}return e}if(t){return Promise.reject(n)}throw n}}}const _e=new Ae;function Ie(e,t){return _e.parse(e,t)}Ie.options=Ie.setOptions=function(e){_e.setOptions(e);Ie.defaults=_e.defaults;i(Ie.defaults);return Ie};Ie.getDefaults=s;Ie.defaults=r;Ie.use=function(...e){_e.use(...e);Ie.defaults=_e.defaults;i(Ie.defaults);return Ie};Ie.walkTokens=function(e,t){return _e.walkTokens(e,t)};Ie.parseInline=_e.parseInline;Ie.Parser=Te;Ie.parser=Te.parse;Ie.Renderer=Re;Ie.TextRenderer=Se;Ie.Lexer=$e;Ie.lexer=$e.lex;Ie.Tokenizer=ye;Ie.Hooks=ze;Ie.parse=Ie;const Pe=Ie.options;const Le=Ie.setOptions;const Ce=Ie.use;const Be=Ie.walkTokens;const qe=Ie.parseInline;const Ee=Ie;const Ze=Te.parse;const ve=$e.lex}}]);